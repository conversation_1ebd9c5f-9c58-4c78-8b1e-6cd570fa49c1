import React from "react";
import {
  View,
  Text,
  Pressable,
  TouchableWithoutFeedback,
  Keyboard,
} from "react-native";
import { styles as chatboxStyles, topStyles } from "../helpers/stylesheet";
import SidebarIcon from "@/assets/icons/SidebarIcon";
import NewConversation from "@/assets/icons/NewConversationIcon";
import VeriskLogo from "@/assets/icons/VeriskLogo";
import ProfileIcon from "@/assets/icons/ProfileIcon";

interface AppHeaderProps {
  onSidebarPress: () => void;
  onNewConversationPress?: () => void;
  onProfilePress: () => void;
  isDisabled?: boolean;
  showNewConversation?: boolean;
}

export function AppHeader({
  onSidebarPress,
  onNewConversationPress,
  onProfilePress,
  isDisabled = false,
  showNewConversation = true,
}: AppHeaderProps) {
  return (
    <>
      <TouchableWithoutFeedback onPress={Keyboard.dismiss} accessible={false}>
        <View style={chatboxStyles.topToolbar} />
      </TouchableWithoutFeedback>
      <TouchableWithoutFeedback onPress={Keyboard.dismiss} accessible={false}>
        <View style={chatboxStyles.header}>
          <View style={topStyles.topRow}>
            {/* Sidebar  */}
            <View style={topStyles.leftTop}>
              <Pressable
                testID="sidebar-button"
                disabled={isDisabled}
                onPress={(e) => {
                  e.stopPropagation();
                  onSidebarPress();
                }}
              >
                <SidebarIcon isActive={isDisabled} />
              </Pressable>
              {showNewConversation && onNewConversationPress && (
                <Pressable
                  testID="new-conv-button"
                  disabled={isDisabled}
                  onPress={onNewConversationPress}
                >
                  <NewConversation />
                </Pressable>
              )}
            </View>

            {/* Verisk Logo  */}
            <View style={topStyles.centerTop}>
              <VeriskLogo />
            </View>

            {/* User profile  */}
            <View style={topStyles.rightTop}>
              <Pressable
                testID="icon-button"
                disabled={isDisabled}
                onPress={onProfilePress}
              >
                <ProfileIcon />
              </Pressable>
            </View>
          </View>

          {/* Banner  */}
          <View style={chatboxStyles.banner}>
            <Text
              style={topStyles.topBannerText}
              adjustsFontSizeToFit
              numberOfLines={1}
              minimumFontScale={1}
            >
              Premium Audit Advisory Service
            </Text>
          </View>
        </View>
      </TouchableWithoutFeedback>
    </>
  );
}
