
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for app/modals</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../index.html">All files</a> app/modals</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">91.92% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>148/161</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">84.68% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>94/111</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">81.42% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>57/70</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">93.28% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>139/149</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line high'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file high" data-value="options.tsx"><a href="options.tsx.html">options.tsx</a></td>
	<td data-value="100" class="pic high">
	<div class="chart"><div class="cover-fill cover-full" style="width: 100%"></div><div class="cover-empty" style="width: 0%"></div></div>
	</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="17" class="abs high">17/17</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="6" class="abs high">6/6</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="10" class="abs high">10/10</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="13" class="abs high">13/13</td>
	</tr>

<tr>
	<td class="file high" data-value="popup.tsx"><a href="popup.tsx.html">popup.tsx</a></td>
	<td data-value="95.83" class="pic high">
	<div class="chart"><div class="cover-fill" style="width: 95%"></div><div class="cover-empty" style="width: 5%"></div></div>
	</td>
	<td data-value="95.83" class="pct high">95.83%</td>
	<td data-value="48" class="abs high">46/48</td>
	<td data-value="84.37" class="pct high">84.37%</td>
	<td data-value="64" class="abs high">54/64</td>
	<td data-value="80.95" class="pct high">80.95%</td>
	<td data-value="21" class="abs high">17/21</td>
	<td data-value="95.65" class="pct high">95.65%</td>
	<td data-value="46" class="abs high">44/46</td>
	</tr>

<tr>
	<td class="file high" data-value="sidebar.tsx"><a href="sidebar.tsx.html">sidebar.tsx</a></td>
	<td data-value="88.54" class="pic high">
	<div class="chart"><div class="cover-fill" style="width: 88%"></div><div class="cover-empty" style="width: 12%"></div></div>
	</td>
	<td data-value="88.54" class="pct high">88.54%</td>
	<td data-value="96" class="abs high">85/96</td>
	<td data-value="82.92" class="pct high">82.92%</td>
	<td data-value="41" class="abs high">34/41</td>
	<td data-value="76.92" class="pct medium">76.92%</td>
	<td data-value="39" class="abs medium">30/39</td>
	<td data-value="91.11" class="pct high">91.11%</td>
	<td data-value="90" class="abs high">82/90</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-04-01T01:07:34.081Z
            </div>
        <script src="../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../sorter.js"></script>
        <script src="../../block-navigation.js"></script>
    </body>
</html>
    