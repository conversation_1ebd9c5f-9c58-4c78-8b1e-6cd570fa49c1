import React from 'react';
import { View, StyleSheet } from 'react-native';
import Svg, { ClipPath, Defs, G, Path } from 'react-native-svg';

const styles = StyleSheet.create({
    icon: {
        height: 30,
        width: 30
    },  
});

export default function GroupLogo(props: any) { 
    const getIcon = (props: any) => {
        return (
            <Svg xmlns="http://www.w3.org/2000/svg" fill="none" {...props}>
            <G fill="#fff">
              <Path d="M.887.744H.252V.348h1.772v.396H1.39v1.944H.92L.887.744ZM2.526.348h.769l.468 1.692.501-1.692H5v2.34h-.468V.672L3.93 2.688H3.53L2.96.672v2.016h-.434V.348Z" />
            </G>
          </Svg>
        );
    };

    return (
    <View style={styles.icon}>
        { getIcon(null) }
    </View>
    );
};