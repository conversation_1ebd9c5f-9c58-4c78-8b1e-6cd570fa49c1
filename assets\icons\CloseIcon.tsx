import React from 'react';
import { View, StyleSheet } from 'react-native';
import Svg, { Mask, Path, G } from 'react-native-svg';

const styles = StyleSheet.create({
    icon: {
        height: 40,
        width: 40,
        padding: 1,
    },  
});

export default function Close({
}: { 
}) { 
    const getIcon = (props: any) => {
        return (
            <Svg xmlns="http://www.w3.org/2000/svg" fill="none" {...props}>
            <Mask id="a" width={32} height={32} x={0} y={0} maskUnits="userSpaceOnUse">
              <Path fill="#D9D9D9" d="M0 0h32v32H0z" />
            </Mask>
            <G mask="url(#a)">
              <Path
                fill="#00358E"
                d="m8.533 25.333-1.866-1.866L14.133 16 6.667 8.533l1.866-1.866L16 14.133l7.467-7.466 1.866 1.866L17.867 16l7.466 7.467-1.866 1.866L16 17.867l-7.467 7.466Z"
              />
            </G>
          </Svg>
        );
    };

    return (
    <View style={styles.icon}>
        { getIcon(null) }
    </View>
    );
};