/* eslint-disable */
import * as Router from 'expo-router';

export * from 'expo-router';

declare module 'expo-router' {
  export namespace ExpoRouter {
    export interface __routes<T extends string | object = string> {
      hrefInputParams: { pathname: Router.RelativePathString, params?: Router.UnknownInputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownInputParams } | { pathname: `/auth-popup`; params?: Router.UnknownInputParams; } | { pathname: `/authentication`; params?: Router.UnknownInputParams; } | { pathname: `/chatbox`; params?: Router.UnknownInputParams; } | { pathname: `/gateway-auth`; params?: Router.UnknownInputParams; } | { pathname: `/messages`; params?: Router.UnknownInputParams; } | { pathname: `/stylesheet`; params?: Router.UnknownInputParams; } | { pathname: `/_sitemap`; params?: Router.UnknownInputParams; } | { pathname: `/__tests__/app-test`; params?: Router.UnknownInputParams; } | { pathname: `/__tests__/authentication-test`; params?: Router.UnknownInputParams; } | { pathname: `/__tests__/chatbox-test`; params?: Router.UnknownInputParams; } | { pathname: `/__tests__/sidebar-test`; params?: Router.UnknownInputParams; } | { pathname: `/helpers/authContext`; params?: Router.UnknownInputParams; } | { pathname: `/helpers/mock`; params?: Router.UnknownInputParams; } | { pathname: `/helpers/model`; params?: Router.UnknownInputParams; } | { pathname: `/helpers/service`; params?: Router.UnknownInputParams; } | { pathname: `/helpers/voice-recognition`; params?: Router.UnknownInputParams; } | { pathname: `/loaders/fontLoader`; params?: Router.UnknownInputParams; } | { pathname: `/mock/mock`; params?: Router.UnknownInputParams; } | { pathname: `/modals/options`; params?: Router.UnknownInputParams; } | { pathname: `/modals/popup`; params?: Router.UnknownInputParams; } | { pathname: `/modals/sidebar`; params?: Router.UnknownInputParams; };
      hrefOutputParams: { pathname: Router.RelativePathString, params?: Router.UnknownOutputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownOutputParams } | { pathname: `/auth-popup`; params?: Router.UnknownOutputParams; } | { pathname: `/authentication`; params?: Router.UnknownOutputParams; } | { pathname: `/chatbox`; params?: Router.UnknownOutputParams; } | { pathname: `/gateway-auth`; params?: Router.UnknownOutputParams; } | { pathname: `/messages`; params?: Router.UnknownOutputParams; } | { pathname: `/stylesheet`; params?: Router.UnknownOutputParams; } | { pathname: `/_sitemap`; params?: Router.UnknownOutputParams; } | { pathname: `/__tests__/app-test`; params?: Router.UnknownOutputParams; } | { pathname: `/__tests__/authentication-test`; params?: Router.UnknownOutputParams; } | { pathname: `/__tests__/chatbox-test`; params?: Router.UnknownOutputParams; } | { pathname: `/__tests__/sidebar-test`; params?: Router.UnknownOutputParams; } | { pathname: `/helpers/authContext`; params?: Router.UnknownOutputParams; } | { pathname: `/helpers/mock`; params?: Router.UnknownOutputParams; } | { pathname: `/helpers/model`; params?: Router.UnknownOutputParams; } | { pathname: `/helpers/service`; params?: Router.UnknownOutputParams; } | { pathname: `/helpers/voice-recognition`; params?: Router.UnknownOutputParams; } | { pathname: `/loaders/fontLoader`; params?: Router.UnknownOutputParams; } | { pathname: `/mock/mock`; params?: Router.UnknownOutputParams; } | { pathname: `/modals/options`; params?: Router.UnknownOutputParams; } | { pathname: `/modals/popup`; params?: Router.UnknownOutputParams; } | { pathname: `/modals/sidebar`; params?: Router.UnknownOutputParams; };
      href: Router.RelativePathString | Router.ExternalPathString | `/auth-popup${`?${string}` | `#${string}` | ''}` | `/authentication${`?${string}` | `#${string}` | ''}` | `/chatbox${`?${string}` | `#${string}` | ''}` | `/gateway-auth${`?${string}` | `#${string}` | ''}` | `/messages${`?${string}` | `#${string}` | ''}` | `/stylesheet${`?${string}` | `#${string}` | ''}` | `/_sitemap${`?${string}` | `#${string}` | ''}` | `/__tests__/app-test${`?${string}` | `#${string}` | ''}` | `/__tests__/authentication-test${`?${string}` | `#${string}` | ''}` | `/__tests__/chatbox-test${`?${string}` | `#${string}` | ''}` | `/__tests__/sidebar-test${`?${string}` | `#${string}` | ''}` | `/helpers/authContext${`?${string}` | `#${string}` | ''}` | `/helpers/mock${`?${string}` | `#${string}` | ''}` | `/helpers/model${`?${string}` | `#${string}` | ''}` | `/helpers/service${`?${string}` | `#${string}` | ''}` | `/helpers/voice-recognition${`?${string}` | `#${string}` | ''}` | `/loaders/fontLoader${`?${string}` | `#${string}` | ''}` | `/mock/mock${`?${string}` | `#${string}` | ''}` | `/modals/options${`?${string}` | `#${string}` | ''}` | `/modals/popup${`?${string}` | `#${string}` | ''}` | `/modals/sidebar${`?${string}` | `#${string}` | ''}` | { pathname: Router.RelativePathString, params?: Router.UnknownInputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownInputParams } | { pathname: `/auth-popup`; params?: Router.UnknownInputParams; } | { pathname: `/authentication`; params?: Router.UnknownInputParams; } | { pathname: `/chatbox`; params?: Router.UnknownInputParams; } | { pathname: `/gateway-auth`; params?: Router.UnknownInputParams; } | { pathname: `/messages`; params?: Router.UnknownInputParams; } | { pathname: `/stylesheet`; params?: Router.UnknownInputParams; } | { pathname: `/_sitemap`; params?: Router.UnknownInputParams; } | { pathname: `/__tests__/app-test`; params?: Router.UnknownInputParams; } | { pathname: `/__tests__/authentication-test`; params?: Router.UnknownInputParams; } | { pathname: `/__tests__/chatbox-test`; params?: Router.UnknownInputParams; } | { pathname: `/__tests__/sidebar-test`; params?: Router.UnknownInputParams; } | { pathname: `/helpers/authContext`; params?: Router.UnknownInputParams; } | { pathname: `/helpers/mock`; params?: Router.UnknownInputParams; } | { pathname: `/helpers/model`; params?: Router.UnknownInputParams; } | { pathname: `/helpers/service`; params?: Router.UnknownInputParams; } | { pathname: `/helpers/voice-recognition`; params?: Router.UnknownInputParams; } | { pathname: `/loaders/fontLoader`; params?: Router.UnknownInputParams; } | { pathname: `/mock/mock`; params?: Router.UnknownInputParams; } | { pathname: `/modals/options`; params?: Router.UnknownInputParams; } | { pathname: `/modals/popup`; params?: Router.UnknownInputParams; } | { pathname: `/modals/sidebar`; params?: Router.UnknownInputParams; };
    }
  }
}
