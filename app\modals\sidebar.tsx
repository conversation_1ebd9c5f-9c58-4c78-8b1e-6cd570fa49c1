import React, { useEffect, useRef, useState, useCallback } from "react";
import {
  Text,
  View,
  ScrollView,
  TextInput,
  Pressable,
  Modal,
  Animated,
} from "react-native";
import {
  Conversation,
  EmitData,
  EmitType,
  ModalData,
  ModalState,
  ModalType,
  UserData,
} from "../helpers/model";
import { Options } from "./options";
import { sidebarStyles } from "../helpers/stylesheet";
import { PopUp } from "./popup";
import EllipsesLogo from "@/assets/icons/EllipsesIcon";
import SearchIcon from "@/assets/icons/SearchIcon";

export function Sidebar({
  userData,
  isVisible,
  emitChange,
  onBackdropPress
}: {
  userData: UserData;
  isVisible: boolean;
  emitChange: (emitData: EmitData) => void;
  onBackdropPress: () => void;
}) {
  const [filteredUserData, setFilteredUserData] = useState(userData);
  const [searchText, setSearchText] = useState("");
  const [isOptionsVisible, setOptionsVisible] = useState(false);
  const slideAnim = useRef(new Animated.Value(-500)).current;
  const scrollViewRef = useRef<ScrollView>(null);
  const [modalState, setModalState] = useState(new ModalState());
  const [isClosing, setIsClosing] = useState(false);

  useEffect(() => {
    setFilteredUserData(userData);
  }, [userData]);

  // Clean animation for Modal opening
  useEffect(() => {
    if (isVisible && !isClosing) {
      setIsClosing(false);
      setOptionsVisible(false);
      slideAnim.setValue(-500);

      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 200,
        useNativeDriver: true,
      }).start();
    } else if (!isVisible) {
      setSearchText("");
      setFilteredUserData(userData);
      setIsClosing(false);
      slideAnim.setValue(-500);
    }
  }, [isVisible, isClosing, slideAnim, userData]);

  const handleClose = useCallback(
    (emitData: EmitData) => {
      if (!isVisible || isClosing) return;
      setIsClosing(true);
      emitChange(emitData);

      Animated.timing(slideAnim, {
        toValue: -500,
        duration: 200,
        useNativeDriver: true,
      }).start(() => {
        slideAnim.setValue(-500);
        setOptionsVisible(false);
        setIsClosing(false);
      });
    },
    [isVisible, isClosing, slideAnim, emitChange]
  );

  const handleBackdropClose = useCallback(() => {
    if (onBackdropPress) {
      onBackdropPress();
    } else {
      if (!isVisible || isClosing) return;
      setIsClosing(true);

      Animated.timing(slideAnim, {
        toValue: -500,
        duration: 200,
        useNativeDriver: true,
      }).start(() => {
        slideAnim.setValue(-500);
        setOptionsVisible(false);
        setIsClosing(false);
      });
    }
  }, [onBackdropPress, isVisible, isClosing, slideAnim]);

  // Scroll back to to and open menu
  const handleOptionsClick = () => {
    scrollViewRef.current?.scrollTo({ y: 0, animated: true });
    setOptionsVisible(true);
  };

  const submitSearch = () => {
    const searchWords = searchText.toLowerCase().split(' ');
    const filteredData = userData.conversations.filter((conversation) => {
      const title = conversation.title.toLowerCase();
      return searchWords.every((word) => title.includes(word));
    });
    setFilteredUserData({ ...userData, conversations: filteredData });
  };

  const changeTab = (conversationId: string) => {
    const newEmitData = {
      ...new EmitData(),
      isConfirmed: true,
      emitType: EmitType.changeTab,
      conversationId: conversationId,
    };
    handleClose(newEmitData);
  };

  const handleOptionsClose = (emitData: EmitData) => {
    setOptionsVisible(false);

    if (emitData.isConfirmed) {
      handleClose(emitData);
    }
  };

  // Open modal
  const toggleModal = (modalType: ModalType) => {
    setModalState({
      modalType: modalType,
      isVisible: !modalState.isVisible,
      conversationName: modalState.conversationName || '',
    });
  };

  const onHandlePopupClose = (emitData: EmitData) => {
    if (emitData.isConfirmed === false) return;
    emitChange(emitData);
  };

  const getConversations = (category: string) => {
    switch (category) {
      case "today": {
        const activeConversation = filteredUserData.conversations.find(
          (conversation) => conversation.isActive
        );
        const todayConversations = filteredUserData.conversations.filter(
          (conversation) => {
            const today = new Date();
            const conversationDate = new Date(conversation.date);
            return (
              conversationDate.toDateString() === today.toDateString() &&
              (conversation.title !== "New Conversation" ||
                conversation.isActive)
            );
          }
        );
        if (activeConversation) {
          return [
            activeConversation,
            ...todayConversations.filter(
              (conversation) => !conversation.isActive
            ),
          ];
        }
        return todayConversations.sort(
          (a, b) => new Date(b.date).getTime() - new Date(a.date).getTime()
        );
      }
      case "lastWeek": {
        const lastWeekConversations = filteredUserData.conversations.filter(
          (conversation) => {
            const today = new Date();
            const conversationDate = new Date(conversation.date);
            const diffTime = Math.abs(
              today.getTime() - conversationDate.getTime()
            );
            const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
            return (
              diffDays > 0 &&
              diffDays <= 7 &&
              conversationDate.getDate() !== today.getDate() &&
              !conversation.isActive
            );
          }
        );
        return lastWeekConversations.sort(
          (a, b) => new Date(b.date).getTime() - new Date(a.date).getTime()
        );
      }
      case "lastMonth": {
        const lastMonthConversations = filteredUserData.conversations.filter(
          (conversation) => {
            const today = new Date();
            const conversationDate = new Date(conversation.date);
            const diffTime = Math.abs(
              today.getTime() - conversationDate.getTime()
            );
            const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
            return diffDays > 7 && diffDays <= 30 && !conversation.isActive;
          }
        );
        return lastMonthConversations.sort(
          (a, b) => new Date(b.date).getTime() - new Date(a.date).getTime()
        );
      }
      default: {
        const lastMonthConversations = filteredUserData.conversations.filter(
          (conversation) => {
            const today = new Date();
            const conversationDate = new Date(conversation.date);
            const diffTime = Math.abs(
              today.getTime() - conversationDate.getTime()
            );
            const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
            return diffDays > 30 && !conversation.isActive;
          }
        );
        return lastMonthConversations.sort(
          (a, b) => new Date(b.date).getTime() - new Date(a.date).getTime()
        );
      }
    }
  };

  const displayTab = (conversation: Conversation) => {
    return (
      <View style={sidebarStyles.titleRow}>
        <Pressable
          testID={`sidebar-conversation-${conversation.conversationId}`}
          style={sidebarStyles.titleClick}
          onPress={
            conversation.isActive
              ? undefined
              : () => changeTab(conversation.conversationId)
          }
        >
          <Text
            numberOfLines={1}
            ellipsizeMode="tail"
            style={
              conversation.isActive
                ? sidebarStyles.activeTitle
                : sidebarStyles.title
            }
          >
            {conversation.title}
          </Text>
        </Pressable>

        {conversation.isActive && conversation.title !== "New Conversation" && (
          <Pressable
            style={sidebarStyles.options}
            testID="conv-options-button"
            onPress={() => handleOptionsClick()}
          >
            <View testID="ellipses" style={sidebarStyles.optionsIcon}>
              <EllipsesLogo />
            </View>
          </Pressable>
        )}
      </View>
    );
  };

  return (
    <Modal
      animationType="none"
      transparent={true}
      visible={isVisible && !isClosing}
      onRequestClose={() => {
        handleClose(new EmitData());
      }}
    >
      <View style={sidebarStyles.outerContainer}>
        <Pressable
          testID="sidebar-backdrop"
          onPress={handleBackdropClose}
          style={sidebarStyles.backdrop}
        ></Pressable>

        <Animated.View
          testID="sidebar"
          style={[
            sidebarStyles.container,
            { transform: [{ translateX: slideAnim }] },
          ]}
        >
          <View style={sidebarStyles.topBoundary} />

          <View style={sidebarStyles.searchRow}>
            <View style={sidebarStyles.inputContainer}>
              <TextInput
                testID="search-input"
                style={[
                  sidebarStyles.searchBar,
                  { fontStyle: searchText ? "normal" : "italic" },
                ]}
                onChangeText={setSearchText}
                value={searchText}
                placeholder="Search"
                placeholderTextColor="#767676"
                enablesReturnKeyAutomatically
                onSubmitEditing={submitSearch}
              />
            </View>

            <Pressable
              testID="search-button"
              onPress={() => submitSearch()}
              style={sidebarStyles.searchButton}
            >
              <SearchIcon />
            </Pressable>
          </View>

          <Options
            isVisible={isOptionsVisible}
            onClose={(emitData) => handleOptionsClose(emitData)}
          />

          <ScrollView
            keyboardShouldPersistTaps="never"
            ref={scrollViewRef}
            style={sidebarStyles.content}
          >
            {getConversations("today").map((conversation, index) => (
              <View key={index}>
                {index === 0 && (
                  <View style={sidebarStyles.category}>
                    <Text>Today</Text>
                  </View>
                )}
                {displayTab(conversation)}
              </View>
            ))}

            {getConversations("lastWeek").map((conversation, index) => (
              <View key={index}>
                {index === 0 && (
                  <View style={sidebarStyles.category}>
                    <Text>Last 7 Days</Text>
                  </View>
                )}
                {displayTab(conversation)}
              </View>
            ))}

            {getConversations("lastMonth").map((conversation, index) => (
              <View key={index}>
                {index === 0 && (
                  <View style={sidebarStyles.category}>
                    <Text>Last 30 Days</Text>
                  </View>
                )}
                {displayTab(conversation)}
              </View>
            ))}

            {getConversations("older").map((conversation, index) => (
              <View key={index}>
                {index === 0 && (
                  <View style={sidebarStyles.category}>
                    <Text>Older</Text>
                  </View>
                )}
                {displayTab(conversation)}
              </View>
            ))}
          </ScrollView>

          <View style={sidebarStyles.footer}>
            <View style={sidebarStyles.footerButton}>
              <Pressable
                testID="how-to-use-button"
                onPress={() => toggleModal(ModalType.HowToUse)}
              >
                <Text style={sidebarStyles.buttonText}>How to Use</Text>
              </Pressable>
            </View>
            <View style={sidebarStyles.footerButtonRight}>
              <Pressable
                testID="disclaimer-button"
                onPress={() => toggleModal(ModalType.Disclaimer)}
              >
                <Text style={sidebarStyles.buttonText}>Disclaimers</Text>
              </Pressable>
            </View>
          </View>

          <View style={sidebarStyles.bottomBoundary} />
        </Animated.View>
      </View>

      <PopUp
        modalType={modalState.modalType}
        isVisible={modalState.isVisible}
        conversationName={modalState.conversationName}
        index={0}
        onClose={(emitData) => {
          toggleModal(modalState.modalType);
          onHandlePopupClose(emitData);
        }}
      />
    </Modal>
  );
}
