import React, { useEffect } from 'react';
import { Stack } from 'expo-router';
import { AuthProvider } from './helpers/authContext';
import { AppStateProvider } from './helpers/appStateContext';
import { loadFonts } from './loaders/fontLoader';

export default function RootLayout() {
  const [fontsLoaded, setFontsLoaded] = React.useState(false);

  useEffect(() => {
    async function load() {
      await loadFonts();
      setFontsLoaded(true);
    }
    load();
  }, []);

  if (!fontsLoaded) {
    return null;
  }

  return (
    <AuthProvider>
      <AppStateProvider>
        <Stack
          screenOptions={{
            headerShown: false, 
          }}
        >
          <Stack.Screen name="index" />
          <Stack.Screen
            name="paas-source/[id]"
            options={{
              presentation: 'modal',
              animation: 'slide_from_right',
            }}
          />
        </Stack>
      </AppStateProvider>
    </AuthProvider>
  );
}
