// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Chatbox matches snapshot 1`] = `
<View
  style={
    {
      "flex": 1,
      "height": "100%",
    }
  }
>
  <View
    style={
      {
        "flex": 1,
        "flexDirection": "column",
        "height": "100%",
      }
    }
  >
    <View
      accessibilityState={
        {
          "busy": undefined,
          "checked": undefined,
          "disabled": undefined,
          "expanded": undefined,
          "selected": undefined,
        }
      }
      accessible={false}
      focusable={true}
      onClick={[Function]}
      onResponderGrant={[Function]}
      onResponderMove={[Function]}
      onResponderRelease={[Function]}
      onResponderTerminate={[Function]}
      onResponderTerminationRequest={[Function]}
      onStartShouldSetResponder={[Function]}
      style={
        {
          "height": "5.5%",
          "width": "100%",
        }
      }
    />
    <View
      accessibilityState={
        {
          "busy": undefined,
          "checked": undefined,
          "disabled": undefined,
          "expanded": undefined,
          "selected": undefined,
        }
      }
      accessible={false}
      focusable={true}
      onClick={[Function]}
      onResponderGrant={[Function]}
      onResponderMove={[Function]}
      onResponderRelease={[Function]}
      onResponderTerminate={[Function]}
      onResponderTerminationRequest={[Function]}
      onStartShouldSetResponder={[Function]}
      style={
        {
          "flexDirection": "column",
        }
      }
    >
      <View
        style={
          {
            "alignContent": "center",
            "alignItems": "center",
            "flexDirection": "row",
            "flexWrap": "nowrap",
            "justifyContent": "space-between",
            "paddingLeft": 10,
            "paddingRight": 10,
          }
        }
      >
        <View
          style={
            {
              "alignContent": "center",
              "alignItems": "center",
              "flexDirection": "row",
              "gap": 8,
              "justifyContent": "flex-start",
              "width": "25%",
            }
          }
        >
          <View
            accessibilityState={
              {
                "busy": undefined,
                "checked": undefined,
                "disabled": false,
                "expanded": undefined,
                "selected": undefined,
              }
            }
            accessibilityValue={
              {
                "max": undefined,
                "min": undefined,
                "now": undefined,
                "text": undefined,
              }
            }
            accessible={true}
            collapsable={false}
            focusable={true}
            onBlur={[Function]}
            onClick={[Function]}
            onFocus={[Function]}
            onResponderGrant={[Function]}
            onResponderMove={[Function]}
            onResponderRelease={[Function]}
            onResponderTerminate={[Function]}
            onResponderTerminationRequest={[Function]}
            onStartShouldSetResponder={[Function]}
            testID="sidebar-button"
          >
            <View
              style={
                {
                  "height": 40,
                  "width": 42,
                }
              }
            >
              <RNSVGSvgView
                bbHeight="100%"
                bbWidth="100%"
                fill="none"
                focusable={false}
                style={
                  [
                    {
                      "backgroundColor": "transparent",
                      "borderWidth": 0,
                    },
                    {
                      "flex": 0,
                      "height": "100%",
                      "width": "100%",
                    },
                  ]
                }
                xmlns="http://www.w3.org/2000/svg"
              >
                <RNSVGGroup
                  fill={null}
                  propList={
                    [
                      "fill",
                    ]
                  }
                >
                  <RNSVGMask
                    fill={
                      {
                        "payload": 4278190080,
                        "type": 0,
                      }
                    }
                    height={40}
                    maskContentUnits={1}
                    maskType={0}
                    maskUnits={1}
                    name="a"
                    width={40}
                    x={0}
                    y={0}
                  >
                    <RNSVGPath
                      d="M0 0h40v40H0z"
                      fill={
                        {
                          "payload": 4292467161,
                          "type": 0,
                        }
                      }
                      propList={
                        [
                          "fill",
                        ]
                      }
                    />
                  </RNSVGMask>
                  <RNSVGGroup
                    fill={
                      {
                        "payload": 4278190080,
                        "type": 0,
                      }
                    }
                    mask="a"
                  >
                    <RNSVGPath
                      d="M5 30v-3.333h30V30H5Zm0-8.333v-3.334h30v3.334H5Zm0-8.334V10h30v3.333H5Z"
                      fill={
                        {
                          "payload": 4278203790,
                          "type": 0,
                        }
                      }
                      propList={
                        [
                          "fill",
                        ]
                      }
                    />
                  </RNSVGGroup>
                </RNSVGGroup>
              </RNSVGSvgView>
            </View>
          </View>
          <View
            accessibilityState={
              {
                "busy": undefined,
                "checked": undefined,
                "disabled": false,
                "expanded": undefined,
                "selected": undefined,
              }
            }
            accessibilityValue={
              {
                "max": undefined,
                "min": undefined,
                "now": undefined,
                "text": undefined,
              }
            }
            accessible={true}
            collapsable={false}
            focusable={true}
            onBlur={[Function]}
            onClick={[Function]}
            onFocus={[Function]}
            onResponderGrant={[Function]}
            onResponderMove={[Function]}
            onResponderRelease={[Function]}
            onResponderTerminate={[Function]}
            onResponderTerminationRequest={[Function]}
            onStartShouldSetResponder={[Function]}
            testID="new-conv-button"
          >
            <View
              style={
                {
                  "height": 40,
                  "padding": 5,
                  "width": 40,
                }
              }
            >
              <RNSVGSvgView
                bbHeight="100%"
                bbWidth="100%"
                fill="none"
                focusable={false}
                style={
                  [
                    {
                      "backgroundColor": "transparent",
                      "borderWidth": 0,
                    },
                    {
                      "flex": 0,
                      "height": "100%",
                      "width": "100%",
                    },
                  ]
                }
                xmlns="http://www.w3.org/2000/svg"
              >
                <RNSVGGroup
                  fill={null}
                  propList={
                    [
                      "fill",
                    ]
                  }
                >
                  <RNSVGPath
                    d="M6.667 28a2.568 2.568 0 0 1-1.884-.783A2.568 2.568 0 0 1 4 25.333V6.667c0-.734.261-1.361.783-1.884A2.568 2.568 0 0 1 6.667 4h11.9L15.9 6.667H6.667v18.666h18.666v-9.266L28 13.4v11.933c0 .734-.261 1.362-.783 1.884a2.568 2.568 0 0 1-1.884.783H6.667ZM12 20v-5.667L24.233 2.1c.267-.267.567-.467.9-.6a2.633 2.633 0 0 1 2.9.6L29.9 4c.244.267.433.561.567.883.133.323.2.65.2.984 0 .333-.061.66-.184.983a2.497 2.497 0 0 1-.583.883L17.667 20H12Zm2.667-2.667h1.866L24.267 9.6l-.934-.933-.966-.934-7.7 7.7v1.9Z"
                    fill={
                      {
                        "payload": 4294952448,
                        "type": 0,
                      }
                    }
                    propList={
                      [
                        "fill",
                      ]
                    }
                  />
                </RNSVGGroup>
              </RNSVGSvgView>
            </View>
          </View>
        </View>
        <View
          style={
            {
              "alignContent": "center",
              "alignItems": "center",
              "flexDirection": "row",
              "justifyContent": "center",
              "marginRight": 20,
              "width": "50%",
            }
          }
        >
          <View
            style={
              {
                "height": 40,
                "paddingTop": 5,
                "width": 100,
              }
            }
          >
            <RNSVGSvgView
              bbHeight="100%"
              bbWidth="100%"
              fill="none"
              focusable={false}
              style={
                [
                  {
                    "backgroundColor": "transparent",
                    "borderWidth": 0,
                  },
                  {
                    "flex": 0,
                    "height": "100%",
                    "width": "100%",
                  },
                ]
              }
              xmlns="http://www.w3.org/2000/svg"
            >
              <RNSVGGroup
                fill={null}
                propList={
                  [
                    "fill",
                  ]
                }
              >
                <RNSVGGroup
                  clipPath="a"
                  fill={
                    {
                      "payload": 4278190080,
                      "type": 0,
                    }
                  }
                >
                  <RNSVGPath
                    d="M64.534 10.39a.112.112 0 0 1 .078.104v2.279a.108.108 0 0 1-.145.1c-.291-.107-1.01-.328-2.029-.316-.608 0-1.167.1-1.716.302a.11.11 0 0 0-.07.1v8.38c0 .06-.049.11-.109.11H58.18a.108.108 0 0 1-.108-.11V11.305c0-.038.022-.075.052-.094.313-.183 1.984-1.089 4.311-1.089a7.71 7.71 0 0 1 2.096.265l.004.004Zm-8.343 4.796v1.197c0 .06-.049.108-.108.108h-7.445a.109.109 0 0 0-.108.12 3.02 3.02 0 0 0 1.16 2.085c.634.496 1.462.768 2.335.768 1.779.019 3.151-.727 3.606-1.01a.107.107 0 0 1 .164.089v2.051a.1.1 0 0 1-.044.086c-.246.175-1.53 1-3.898 1.014-1.715 0-3.174-.537-4.218-1.548-1.06-1.025-1.619-2.472-1.619-4.188 0-1.72.534-3.2 1.54-4.282.978-1.048 2.343-1.626 3.846-1.626 3.043 0 4.789 1.873 4.789 5.14v-.004Zm-2.544-.406c.06 0 .112-.053.108-.112-.1-1.597-.936-2.436-2.424-2.436-1.335 0-2.331.899-2.689 2.42a.108.108 0 0 0 .104.131h4.905l-.004-.003Zm24.112 1.17c-.574-.54-1.458-.928-2.775-1.215-1.253-.343-1.697-.709-1.697-1.388 0-.73.608-1.186 1.586-1.186 1.353-.044 2.547.728 2.976 1.045.07.052.171 0 .171-.086v-2.134a.105.105 0 0 0-.048-.09c-.242-.163-1.406-.864-3.174-.838-.302 0-.63.015-.966.048a3.334 3.334 0 0 0-3.025 3.316c-.01 1.936.918 2.902 3.308 3.45 1.4.37 2.022.679 2.022 1.536 0 .981-.959 1.186-1.76 1.186-1.496 0-2.798-.842-3.249-1.17a.108.108 0 0 0-.171.085v2.215a.11.11 0 0 0 .048.09c.239.164 1.399.873 3.424.891 1.245 0 2.286-.283 3.01-.824.801-.597 1.204-1.47 1.204-2.596 0-1.022-.28-1.76-.88-2.327l-.004-.007ZM67.65 8.638c.876 0 1.618-.754 1.618-1.645s-.742-1.619-1.619-1.619c-.876 0-1.644.728-1.644 1.619s.753 1.645 1.644 1.645ZM66.45 21.444h2.368c.06 0 .108-.048.108-.108V10.401a.108.108 0 0 0-.108-.108h-2.364a.108.108 0 0 0-.108.108v10.935c0 .06.048.108.108.108h-.004Zm18.734-6.146 4.673-4.815c.067-.067.019-.183-.078-.183h-2.898c-.03 0-.06.011-.078.034l-3.827 4.155c-.067.07-.186.026-.186-.071V6.645a.108.108 0 0 0-.109-.108h-2.364a.108.108 0 0 0-.108.108V21.34c0 .06.048.108.108.108h2.368c.06 0 .108-.048.108-.108v-4.815c0-.1.127-.145.19-.067l4.021 4.95a.104.104 0 0 0 .082.04h2.809c.09 0 .141-.104.082-.175L85.18 15.44a.104.104 0 0 1 .008-.142h-.004ZM44.625 6.6 39.49 18.3c-.037.086-.156.086-.197 0L34.156 6.6a.111.111 0 0 0-.097-.063h-2.637a.104.104 0 0 0-.097.15L37.32 20.34c.79 1.805 3.353 1.805 4.143 0l5.994-13.654a.107.107 0 0 0-.097-.149h-2.637a.107.107 0 0 0-.097.063ZM2 7.014c.194-.313.54-.503.914-.503l4.226.011c.156 0 .257.16.197.302L6.21 9.375a.213.213 0 0 1-.198.127l-4.9-.022a.217.217 0 0 1-.202-.291c.298-.761.664-1.492 1.093-2.178l-.004.003Zm3.76-3.48c-.21 0-.295-.268-.127-.387A13.835 13.835 0 0 1 13.729.573c3.025.008 5.814.977 8.078 2.611.168.123.079.388-.127.388L5.76 3.535ZM9.443 24.56a.213.213 0 0 1 .198-.127l11.995.03c.209 0 .294.268.127.387a13.802 13.802 0 0 1-8.098 2.574 13.826 13.826 0 0 1-4.848-.884.215.215 0 0 1-.12-.287l.75-1.693h-.004Zm1.507-3.41 1.127-2.55a.213.213 0 0 1 .197-.127l14.013.037c.149 0 .253.15.2.291a13.006 13.006 0 0 1-1.096 2.186c-.193.313-.54.503-.913.503l-13.334-.033a.216.216 0 0 1-.197-.302l.003-.004Zm16.377-8.413a12.43 12.43 0 0 1-.008 2.596.218.218 0 0 1-.216.193l-13.326-.033a.217.217 0 0 1-.197-.302l.738-1.671a1.684 1.684 0 0 1 1.548-1.003l11.245.03a.21.21 0 0 1 .212.193l.004-.003Z"
                    fill={
                      {
                        "payload": 4278203790,
                        "type": 0,
                      }
                    }
                    propList={
                      [
                        "fill",
                      ]
                    }
                  />
                  <RNSVGPath
                    d="M26.5 9.23a13.457 13.457 0 0 0-1.09-2.19 1.072 1.072 0 0 0-.91-.503H11.756c-.642 0-1.22.377-1.477.966l-4.23 9.648a.109.109 0 0 1-.096.064.109.109 0 0 1-.097-.064l-1.608-3.68a1.614 1.614 0 0 0-1.477-.967H.28a.209.209 0 0 0-.213.194c-.045.43-.067.865-.067 1.31 0 .443.022.865.063 1.29a.217.217 0 0 0 .213.194h1.23c.087 0 .161.048.195.126l3.252 7.348c.19.425.586.638.98.638.396 0 .795-.213.985-.642l5.476-12.468a1.61 1.61 0 0 1 1.476-.962h12.424c.15 0 .254-.15.201-.291l.004-.011Z"
                    fill={
                      {
                        "payload": 4280974817,
                        "type": 0,
                      }
                    }
                    propList={
                      [
                        "fill",
                      ]
                    }
                  />
                </RNSVGGroup>
                <RNSVGDefs>
                  <RNSVGClipPath
                    fill={
                      {
                        "payload": 4278190080,
                        "type": 0,
                      }
                    }
                    name="a"
                  >
                    <RNSVGPath
                      d="M0 .573h90v26.853H0z"
                      fill={
                        {
                          "payload": 4294967295,
                          "type": 0,
                        }
                      }
                      propList={
                        [
                          "fill",
                        ]
                      }
                    />
                  </RNSVGClipPath>
                </RNSVGDefs>
              </RNSVGGroup>
            </RNSVGSvgView>
          </View>
        </View>
        <View
          style={
            {
              "alignItems": "center",
              "flexDirection": "row",
              "height": 42,
              "justifyContent": "center",
              "width": "15%",
            }
          }
        >
          <View
            accessibilityState={
              {
                "busy": undefined,
                "checked": undefined,
                "disabled": false,
                "expanded": undefined,
                "selected": undefined,
              }
            }
            accessibilityValue={
              {
                "max": undefined,
                "min": undefined,
                "now": undefined,
                "text": undefined,
              }
            }
            accessible={true}
            collapsable={false}
            focusable={true}
            onBlur={[Function]}
            onClick={[Function]}
            onFocus={[Function]}
            onResponderGrant={[Function]}
            onResponderMove={[Function]}
            onResponderRelease={[Function]}
            onResponderTerminate={[Function]}
            onResponderTerminationRequest={[Function]}
            onStartShouldSetResponder={[Function]}
            testID="icon-button"
          >
            <View
              style={
                {
                  "height": 27,
                  "width": 27,
                }
              }
            >
              <RNSVGSvgView
                align="xMidYMid"
                bbHeight="100%"
                bbWidth="100%"
                fill="none"
                focusable={false}
                meetOrSlice={0}
                minX={0}
                minY={0}
                style={
                  [
                    {
                      "backgroundColor": "transparent",
                      "borderWidth": 0,
                    },
                    {
                      "flex": 0,
                      "height": "100%",
                      "width": "100%",
                    },
                  ]
                }
                vbHeight={16}
                vbWidth={16}
                xmlns="http://www.w3.org/2000/svg"
              >
                <RNSVGGroup
                  fill={null}
                  propList={
                    [
                      "fill",
                    ]
                  }
                >
                  <RNSVGPath
                    d="M3.9 11.4a6.957 6.957 0 0 1 1.9-1.025C6.5 10.125 7.233 10 8 10c.767 0 1.5.125 2.2.375.7.25 1.333.592 1.9 1.025.389-.456.692-.972.908-1.55A5.227 5.227 0 0 0 13.333 8c0-1.478-.52-2.736-1.558-3.775C10.736 3.186 9.478 2.667 8 2.667c-1.478 0-2.736.519-3.775 1.558C3.186 5.264 2.667 6.522 2.667 8c0 .655.108 1.272.325 1.85.216.578.52 1.094.908 1.55ZM8 8.667c-.656 0-1.208-.225-1.658-.675-.45-.45-.675-1.003-.675-1.659 0-.655.225-1.208.675-1.658C6.792 4.225 7.344 4 8 4c.656 0 1.208.225 1.658.675.45.45.675 1.003.675 1.658 0 .656-.225 1.209-.675 1.659-.45.45-1.002.675-1.658.675Zm0 6a6.492 6.492 0 0 1-2.6-.525 6.732 6.732 0 0 1-2.117-1.425A6.733 6.733 0 0 1 1.858 10.6 6.492 6.492 0 0 1 1.333 8c0-.922.175-1.789.525-2.6a6.732 6.732 0 0 1 1.425-2.117c.6-.6 1.306-1.075 2.117-1.425A6.492 6.492 0 0 1 8 1.333c.922 0 1.789.175 2.6.525.811.35 1.517.825 2.117 1.425.6.6 1.075 1.306 1.425 2.117.35.811.525 1.678.525 2.6 0 .922-.175 1.789-.525 2.6a6.733 6.733 0 0 1-1.425 2.117c-.6.6-1.306 1.075-2.117 1.425a6.491 6.491 0 0 1-2.6.525Z"
                    fill={
                      {
                        "payload": 4278203790,
                        "type": 0,
                      }
                    }
                    propList={
                      [
                        "fill",
                      ]
                    }
                  />
                </RNSVGGroup>
              </RNSVGSvgView>
            </View>
          </View>
        </View>
      </View>
      <View
        style={
          {
            "backgroundColor": "#00358E",
            "flexWrap": "nowrap",
            "justifyContent": "center",
            "paddingBottom": 5,
            "paddingTop": 5,
          }
        }
      >
        <Text
          adjustsFontSizeToFit={true}
          minimumFontScale={1}
          numberOfLines={1}
          style={
            {
              "color": "white",
              "flexShrink": 1,
              "fontFamily": "Roboto",
              "fontSize": 21,
              "fontStyle": "normal",
              "fontWeight": "500",
              "lineHeight": 36,
              "textAlign": "center",
            }
          }
        >
          Premium Audit Advisory Service
        </Text>
      </View>
    </View>
    <View
      style={
        {
          "height": "60%",
        }
      }
    />
    <View
      style={
        {
          "backgroundColor": "#00358E",
          "flex": 1,
          "flexDirection": "column",
          "height": "22%",
        }
      }
    >
      <RCTScrollView
        keyboardShouldPersistTaps="never"
      >
        <View>
          <View
            style={
              {
                "alignItems": "center",
                "justifyContent": "center",
                "padding": 0,
                "paddingBottom": 10,
                "paddingTop": 20,
              }
            }
          >
            <View
              style={
                {
                  "height": 52,
                  "width": 165,
                }
              }
            >
              <RNSVGSvgView
                bbHeight="100%"
                bbWidth="100%"
                fill="none"
                focusable={false}
                style={
                  [
                    {
                      "backgroundColor": "transparent",
                      "borderWidth": 0,
                    },
                    {
                      "flex": 0,
                      "height": "100%",
                      "width": "100%",
                    },
                  ]
                }
                xmlns="http://www.w3.org/2000/svg"
              >
                <RNSVGGroup
                  fill={null}
                  propList={
                    [
                      "fill",
                    ]
                  }
                >
                  <RNSVGGroup
                    clipPath="a"
                    fill={
                      {
                        "payload": 4278190080,
                        "type": 0,
                      }
                    }
                  >
                    <RNSVGPath
                      d="M106.933 17.533c.062 0 .124.067.124.2V21.8c0 .133-.124.2-.248.2-.494-.2-1.668-.6-3.337-.533-.989 0-1.916.2-2.844.533-.061 0-.123.133-.123.2v15c0 .133-.062.2-.186.2h-3.894c-.123 0-.185-.067-.185-.2V19.267c0-.067.062-.134.062-.134.494-.333 3.276-1.933 7.17-1.933 1.73-.133 3.09.2 3.461.333Zm-13.784 8.6v2.134c0 .133-.062.2-.185.2H80.602c-.124 0-.186.066-.186.2.186 1.533.804 2.8 1.916 3.733 1.051.867 2.41 1.4 3.894 1.4 2.967 0 5.192-1.333 5.996-1.8.124-.067.247 0 .247.133V35.8c0 .067 0 .133-.061.133-.433.334-2.535 1.8-6.49 1.8-2.844 0-5.255-.933-6.985-2.733-1.731-1.867-2.658-4.4-2.658-7.467 0-3.066.865-5.733 2.534-7.666a8.346 8.346 0 0 1 6.367-2.934c5.068 0 7.973 3.334 7.973 9.2Zm-4.265-.733c.124 0 .186-.067.186-.2-.186-2.867-1.546-4.333-4.018-4.333-2.225 0-3.832 1.6-4.45 4.333 0 .133.061.267.185.267l8.097-.067Zm39.992 2.067c-.927-1-2.411-1.667-4.574-2.2-2.102-.6-2.843-1.267-2.843-2.467 0-1.333.989-2.133 2.658-2.133 2.225-.067 4.203 1.266 4.944 1.866.124.067.309 0 .309-.133v-3.8c0-.067 0-.133-.061-.133-.433-.267-2.349-1.534-5.254-1.534-.495 0-1.051 0-1.607.067-2.844.267-5.007 2.867-5.007 5.933 0 3.467 1.545 5.2 5.501 6.134 2.287.666 3.338 1.2 3.338 2.733 0 1.733-1.607 2.133-2.905 2.133-2.473 0-4.636-1.533-5.378-2.066-.123-.067-.309 0-.309.133v3.933c0 .067 0 .134.062.134.371.266 2.349 1.533 5.687 1.6 2.039 0 3.77-.534 5.006-1.467 1.298-1.067 1.978-2.6 1.978-4.667-.062-1.733-.556-3.066-1.545-4.066ZM112.125 14.4c1.484 0 2.658-1.333 2.658-2.933 0-1.6-1.236-2.867-2.658-2.867-1.421 0-2.719 1.267-2.719 2.867s1.236 2.933 2.719 2.933Zm-1.978 22.933h3.894c.124 0 .186-.066.186-.2V17.6c0-.133-.062-.2-.186-.2h-3.894c-.123 0-.185.067-.185.2v19.533c0 .067.062.2.185.2Zm31.029-11 7.727-8.6c.123-.133 0-.333-.124-.333h-4.759c-.062 0-.124 0-.124.067l-6.366 7.4c-.124.133-.31.066-.31-.134V10.867c0-.134-.061-.2-.185-.2h-3.894c-.124 0-.185.066-.185.2v26.266c0 .134.061.2.185.2h3.894c.124 0 .185-.066.185-.2v-8.6c0-.2.186-.266.31-.133l6.675 8.867c.041.044.083.066.124.066h4.636c.123 0 .247-.2.123-.333l-7.973-10.4c0-.133 0-.2.061-.267Zm10.632-5.666v-2.534c0-.066.062-.066.062-.066h.803c.062 0 .062-.067.062-.067v-.467c0-.066-.062-.066-.062-.066h-2.349c-.061 0-.061.066-.061.066V18c0 .067.061.067.061.067h.804c.062 0 .062.066.062.066v2.534c0 .066.062.066.062.066h.494l.062-.066Zm3.029.066h.494c.041 0 .062-.022.062-.066l.618-2.134h.062l.309 2.134c0 .066.062.066.062.066h.494c.062 0 .062-.066.062-.066l-.433-3.2c0-.067-.061-.067-.061-.067h-.68c-.042 0-.062.022-.062.067l-.618 2.266h-.062l-.618-2.266c0-.067-.062-.067-.062-.067h-.68c-.041 0-.062.022-.062.067l-.432 3.2c0 .044.02.066.061.066h.495c.041 0 .062-.022.062-.066l.309-2.134c0-.066.062-.066.062 0l.618 2.134c-.062 0-.062.066 0 .066ZM73.926 10.8l-8.53 20.933a.17.17 0 0 1-.309 0L56.619 10.8c0-.067-.062-.133-.185-.133h-4.39c-.123 0-.184.133-.184.266l10.013 24.4c1.298 3.2 5.563 3.2 6.86 0l9.952-24.4c.062-.133-.061-.266-.185-.266h-4.389c-.061 0-.123.066-.185.133Zm-70.65.8c.309-.6.927-.933 1.545-.933h6.985c.247 0 .433.266.309.533l-1.854 4.533a.337.337 0 0 1-.31.2L1.794 16c-.248 0-.433-.267-.31-.533A27.03 27.03 0 0 1 3.276 11.6Zm6.243-6.267c-.371 0-.495-.466-.186-.666C13.103 1.733 17.74 0 22.747 0c5.006 0 9.642 1.733 13.413 4.6.309.2.123.667-.186.667l-26.455.066ZM15.7 42.867a.337.337 0 0 1 .31-.2h19.902c.371 0 .495.466.186.666C32.328 46.267 27.69 48 22.685 48c-2.844 0-5.563-.533-8.036-1.533-.185-.067-.309-.334-.185-.534l1.236-3.066Zm2.472-6.067 1.855-4.533a.337.337 0 0 1 .309-.2h23.24c.248 0 .433.266.31.533-.495 1.4-1.113 2.667-1.793 3.933-.309.534-.865.934-1.483.934H18.482c-.248-.134-.433-.4-.31-.667Zm27.135-15.133c.062.733.124 1.533.124 2.333 0 .8-.062 1.533-.123 2.333 0 .2-.186.334-.371.334H22.87c-.247 0-.433-.267-.309-.534l1.236-3c.433-1.066 1.422-1.8 2.534-1.8h18.606c.185 0 .309.134.37.334Zm-1.421-6.2c-.495-1.4-1.113-2.667-1.793-3.934-.309-.533-.865-.933-1.483-.933H19.47c-1.05 0-2.04.667-2.472 1.733L10.013 29.6c-.061.067-.123.133-.185.133s-.124-.066-.185-.133L6.985 23a2.534 2.534 0 0 0-2.411-1.667H.494c-.185 0-.309.134-.37.334A27.89 27.89 0 0 0 0 24c0 .8.062 1.533.124 2.333 0 .2.185.334.37.334h2.04c.124 0 .248.066.31.2C3.4 28.2 6.303 35.4 8.22 40c.309.733.989 1.133 1.607 1.133.68 0 1.298-.4 1.607-1.133l9.086-22.267c.433-1.066 1.36-1.733 2.473-1.733h20.583c.247 0 .432-.267.309-.533Z"
                      fill={
                        {
                          "payload": 4294967295,
                          "type": 0,
                        }
                      }
                      propList={
                        [
                          "fill",
                        ]
                      }
                    />
                  </RNSVGGroup>
                  <RNSVGDefs>
                    <RNSVGClipPath
                      fill={
                        {
                          "payload": 4278190080,
                          "type": 0,
                        }
                      }
                      name="a"
                    >
                      <RNSVGPath
                        d="M0 0h157v48H0z"
                        fill={
                          {
                            "payload": 4294967295,
                            "type": 0,
                          }
                        }
                        propList={
                          [
                            "fill",
                          ]
                        }
                      />
                    </RNSVGClipPath>
                  </RNSVGDefs>
                </RNSVGGroup>
              </RNSVGSvgView>
            </View>
          </View>
          <Text
            style={
              {
                "color": "#FFF",
                "flexWrap": "wrap",
                "fontFamily": "Roboto",
                "fontSize": 12,
                "fontStyle": "normal",
                "fontWeight": "400",
                "justifyContent": "center",
                "lineHeight": 20,
                "paddingBottom": 10,
                "paddingLeft": 20,
                "paddingRight": 20,
                "paddingTop": 5,
                "textAlign": "center",
                "width": "100%",
              }
            }
          >
            Copyright © 2024 Insurance Services Office, Inc, All rights reserved.
          </Text>
          <Text
            style={
              {
                "color": "#FFF",
                "fontFamily": "Roboto",
                "fontSize": 12,
                "fontStyle": "normal",
                "fontWeight": "400",
                "lineHeight": 20,
                "paddingBottom": 1,
                "textAlign": "center",
                "width": "100%",
              }
            }
          >
            Verisk Corporate Home
          </Text>
          <Text
            style={
              {
                "color": "#FFF",
                "fontFamily": "Roboto",
                "fontSize": 12,
                "fontStyle": "normal",
                "fontWeight": "400",
                "lineHeight": 20,
                "paddingBottom": 1,
                "textAlign": "center",
                "width": "100%",
              }
            }
          >
            Terms and Conditions
          </Text>
          <Text
            style={
              {
                "color": "#FFF",
                "fontFamily": "Roboto",
                "fontSize": 12,
                "fontStyle": "normal",
                "fontWeight": "400",
                "lineHeight": 20,
                "paddingBottom": 1,
                "textAlign": "center",
                "width": "100%",
              }
            }
          >
            Privacy and Security Policy
          </Text>
          <Text
            style={
              {
                "color": "#FFF",
                "fontFamily": "Roboto",
                "fontSize": 12,
                "fontStyle": "normal",
                "fontWeight": "400",
                "lineHeight": 20,
                "paddingBottom": 1,
                "textAlign": "center",
                "width": "100%",
              }
            }
          >
            Contact Us
          </Text>
          <Text
            style={
              {
                "alignItems": "center",
                "color": "#FFFFFF",
                "fontFamily": "Roboto",
                "fontSize": 12,
                "fontWeight": "500",
                "lineHeight": 18,
                "paddingBottom": 10,
                "paddingLeft": 15,
                "paddingRight": 15,
                "paddingTop": 10,
                "textAlign": "center",
              }
            }
          >
            The information displayed here is illustrative and for display purposes only, and should not be relied on. It may contain a compilation of certain examples of actual data as well as fictitious information.
          </Text>
        </View>
      </RCTScrollView>
    </View>
    <View
      style={
        {
          "backgroundColor": "#00358E",
          "height": "3%",
          "width": "100%",
        }
      }
    />
  </View>
</View>
`;
