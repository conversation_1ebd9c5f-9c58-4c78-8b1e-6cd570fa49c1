import React from 'react';
import { View, StyleSheet } from 'react-native';
import Svg, { Path } from 'react-native-svg';

const styles = StyleSheet.create({
    icon: {
        height: 27,
        width: 27,
    },  
});

export default function ProfileIcon() { 
    const getIcon = (props: any) => {
        return (
            <Svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 16 16"
            {...props}
          >
            <Path
              fill="#00358E"
              d="M3.9 11.4a6.957 6.957 0 0 1 1.9-1.025C6.5 10.125 7.233 10 8 10c.767 0 1.5.125 2.2.375.7.25 1.333.592 1.9 1.025.389-.456.692-.972.908-1.55A5.227 5.227 0 0 0 13.333 8c0-1.478-.52-2.736-1.558-3.775C10.736 3.186 9.478 2.667 8 2.667c-1.478 0-2.736.519-3.775 1.558C3.186 5.264 2.667 6.522 2.667 8c0 .655.108 1.272.325 1.85.216.578.52 1.094.908 1.55ZM8 8.667c-.656 0-1.208-.225-1.658-.675-.45-.45-.675-1.003-.675-1.659 0-.655.225-1.208.675-1.658C6.792 4.225 7.344 4 8 4c.656 0 1.208.225 1.658.675.45.45.675 1.003.675 1.658 0 .656-.225 1.209-.675 1.659-.45.45-1.002.675-1.658.675Zm0 6a6.492 6.492 0 0 1-2.6-.525 6.732 6.732 0 0 1-2.117-1.425A6.733 6.733 0 0 1 1.858 10.6 6.492 6.492 0 0 1 1.333 8c0-.922.175-1.789.525-2.6a6.732 6.732 0 0 1 1.425-2.117c.6-.6 1.306-1.075 2.117-1.425A6.492 6.492 0 0 1 8 1.333c.922 0 1.789.175 2.6.525.811.35 1.517.825 2.117 1.425.6.6 1.075 1.306 1.425 2.117.35.811.525 1.678.525 2.6 0 .922-.175 1.789-.525 2.6a6.733 6.733 0 0 1-1.425 2.117c-.6.6-1.306 1.075-2.117 1.425a6.491 6.491 0 0 1-2.6.525Z"
            />
          </Svg>

        );
    };

    return (
    <View style={styles.icon}>
        { getIcon(null) }
    </View>
    );
};