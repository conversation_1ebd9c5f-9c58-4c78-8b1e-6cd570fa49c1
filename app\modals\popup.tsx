import { useEffect, useMemo, useRef, useState } from "react";
import { Text, View, StyleSheet, ScrollView, TextInput, 
    Image, Button, Pressable, Modal, 
    Animated,
    KeyboardAvoidingView,
    Platform,
    Keyboard,
    TouchableWithoutFeedback} from "react-native";
import RadioGroup from 'react-native-radio-buttons-group';
import { EmitData, EmitType, ModalType } from "../helpers/model";
import { ModalData } from "../helpers/model";
import { popupStyles } from "../helpers/stylesheet";
import Close from "@/assets/icons/CloseIcon";
import { useAuth } from "../helpers/authContext";
import { RenderHTML } from 'react-native-render-html';

export function PopUp({
  modalType,
  conversationName,
  isVisible,
  index,
  onClose
}: { 
  modalType: ModalType, 
  conversationName: string,
  isVisible: boolean, 
  index: number
  onClose: (emitData: EmitData) => void 
}) { 
  const { authData, setAuthData } = useAuth();
  const slideAnim = useRef(new Animated.Value(500)).current;
  const [modalData, setModalData] = useState(new ModalData());
  const [renameText, setRenameText] = useState('');
  const [radioOption, setRadio] = useState('1');
  const [feedbackInput, setFeedback] = useState('');

  const radioButtons = useMemo(() => ([
    {
        id: '1',
        label: 'Not relevant enough',
        value: 'Not relevant enough',
        size: 14
    },
    {
        id: '2',
        label: 'Issue with this content',
        value: 'Issue with this content',
        size: 14
    },
    {
        id: '3',
        label: 'Other',
        value: 'Other',
        size: 14
    }
  ]), []);

  useEffect(() => {
    if (!isVisible) {
      setRenameText('');
      setRadio('1');
      setFeedback('');
    }
  }, [isVisible]);

  // Feedback: Clear input if user changes radio
  useEffect(() => {
    if (radioOption !== '3') setFeedback('');
  }, [radioOption]);

  // Set modal data
  useEffect(() => {
    setRenameText('');
    modalData.modalType !== modalType && setModalData(new ModalData(modalType));
    if (modalType === ModalType.HowToUse) {
      setModalData((prev) => ({ ...prev, content: authData.sitecore.HowToUseText.value}));
    } else if (modalType === ModalType.Disclaimer) {
      setModalData((prev) => ({ ...prev, content: authData.sitecore.TermsOfUseText.value }));
      setModalData((prev) => ({ ...prev, content: `${authData.sitecore.TermsOfUseText.value}`}));
    } else if (modalType === ModalType.Delete) {
      // TODO - add conversation name to this like "Conversation {name} will be deleted"
      const text = "This conversation will permanently be deleted. \n" +
        "Do you want to continue?";
      setModalData((prev) => ({ ...prev, content: text }));
    }
  }, [modalType]);

  // animation for Modal opening
  useEffect(() => {
    if (isVisible) {
      Animated.timing(slideAnim, {
        toValue: 0, 
        duration: 300,
        useNativeDriver: true,
      }).start();
    } 
  }, [isVisible]);

  // animation for modal closing
  const handleClose = (isConfirmed: boolean) => {
    Animated.timing(slideAnim, {
      toValue: 500,
      duration: 300,
      useNativeDriver: true,
    }).start(() => {
      slideAnim.setValue(500);
      onClose(getEmitData(isConfirmed));
    });
  };

  const getEmitData = (isConfirmed: boolean) => {
    const getEmitType = () => {
      switch (modalType) {
        case ModalType.Rename:
          return EmitType.rename;
        case ModalType.Delete:
          return EmitType.delete;
        case ModalType.Feedback:
          return EmitType.rateDown;
        default:
          return EmitType.default;
      }
    }
    return ({ ...new EmitData(), 
      name: modalType === ModalType.Rename ? renameText.trim() : '',
      feedback: modalType === ModalType.Feedback ? feedbackInput.trim() : '',
      index: modalType === ModalType.Feedback ? index : -1,
      isConfirmed: modalType === ModalType.Feedback ? true : isConfirmed, 
      emitType: getEmitType(),
      message: '',
      conversationId: ''
     });
  }

  const isDisabled = () => {
   if (modalType === ModalType.Rename &&  
    (renameText.trim() === '' || renameText.trim() === ' ' 
      || renameText.toLocaleLowerCase().trim() === 'new conversation')) {
      return true;
    } else if (modalType === ModalType.Feedback && 
      (radioOption === '3' && (feedbackInput.trim() === '' || feedbackInput.trim() === ' '))) {
      return true;
    }
  }

  //TO DO - Potentially implement different offset for different iphone
  const getKeyboardOffset = () =>{
    return Platform.OS === 'ios' ? 
      (modalType === ModalType.Feedback ? -50 : -150 ) 
      : 0;
  }

  return (
    <Modal
      animationType="none"
      transparent={true}
      visible={isVisible}>
        <KeyboardAvoidingView 
              behavior={ Platform.OS === 'ios' ? 'padding' : 'height' }
              keyboardVerticalOffset={ getKeyboardOffset() }
              style={popupStyles.outerContainer}>
        <Pressable 
          testID='popup-backdrop'
          style={popupStyles.backdrop} 
          onPress={() => handleClose(false)}/>
        <Animated.View style={[popupStyles.container, 
            { transform: [{ translateY: slideAnim }] }]}>

          {/* Title */}
          <TouchableWithoutFeedback onPress={Keyboard.dismiss} accessible={false}>
            <View style = {popupStyles.header}>
              <Text style={popupStyles.title}>{modalData.title}</Text>
              <Pressable testID="x-button" onPress={() => handleClose(false)}>
                <Close/>
              </Pressable>
            </View>
          </TouchableWithoutFeedback>

          {/* Body Content */}
            {modalType === ModalType.Rename ? (
              <View testID='rename-popup' style={popupStyles.rename}>
                <View style={popupStyles.renameContainer}>
                  <TextInput
                    testID='rename-input'
                    style={[popupStyles.input, 
                      { fontStyle: renameText ? 'normal' : 'italic' }]}
                    value={renameText}
                    onChangeText={setRenameText}
                    placeholder="Enter new name"
                    placeholderTextColor="grey"
                    multiline
                    numberOfLines={2}
                    onSubmitEditing={() => renameText.trim() === '' || renameText.trim() === ' '
                      ? () => {} 
                      : handleClose(true)
                    }
                  />
                </View>
              </View>

            ) : modalType === ModalType.Feedback ? (
              <TouchableWithoutFeedback onPress={Keyboard.dismiss} accessible={false}>
                <View testID='feedback-popup' style={popupStyles.bodyFeedback}>
                    <Text>{modalData.content}</Text>
                    <RadioGroup 
                      testID='feedback-radio'
                      radioButtons={radioButtons} 
                      onPress={setRadio}
                      selectedId={radioOption}
                      containerStyle={popupStyles.feedbackRadio}
                    />
                    <View style={popupStyles.inputContainer}>
                      <TextInput
                          testID='feedback-input'
                          style={[popupStyles.feedbackInput, 
                            { fontStyle: feedbackInput ? 'normal' : 'italic' }]}
                          onChangeText={setFeedback}
                          value={feedbackInput}
                          placeholderTextColor="grey"
                          placeholder="Tell us more"
                          enablesReturnKeyAutomatically
                          multiline
                          numberOfLines={2}
                          onSubmitEditing={() => feedbackInput.trim() === '' || feedbackInput.trim() === ' '
                            ? () => {} 
                            : handleClose(true)}
                          editable={radioOption === '3'}
                      />
                    </View >
                </View>
              </TouchableWithoutFeedback>

            ) : (
              <ScrollView keyboardShouldPersistTaps='never'>
                <View testID='misc-popup' style={popupStyles.bodyContainer}>
                  <View style={
                      modalType === ModalType.Disclaimer 
                      ? popupStyles.bodyDisclaimer 
                      : popupStyles.body }>
                    <RenderHTML contentWidth={300} source={{ html: modalData.content }} />
                  </View>
                </View>
              </ScrollView>
            )}

          {/* Footer - Confirmation or OK */}
          {modalData.requiresConfirmation ? (
          <TouchableWithoutFeedback onPress={Keyboard.dismiss} accessible={false}>    
              <View style = {popupStyles.footer}>

                {/* Confirmation */}
                <Pressable 
                  testID='confirm-button'
                  onPress={() => handleClose(true)} 
                  style={[
                    popupStyles.confirmButton, 
                    isDisabled() && popupStyles.disabledButton
                  ]}
                  disabled={isDisabled()}>
                  <Text 
                    style={ isDisabled() ? popupStyles.disabledText : popupStyles.confirmText}>
                      {modalData.submitText}
                  </Text>
                </Pressable>

                {/* Cancel */}
                <Pressable 
                  testID='cancel-button'
                  onPress={() => handleClose(false)} 
                  style={popupStyles.cancelButton}>
                  <Text style={popupStyles.cancelText}>
                    {modalType === ModalType.Feedback ? 'Skip' : 'Cancel'}
                  </Text>
                </Pressable>                 
              </View>   
            </TouchableWithoutFeedback>       

            ) : (
              <TouchableWithoutFeedback onPress={Keyboard.dismiss} accessible={false}>          
                <View style = {popupStyles.footer}>
                  <Pressable 
                    testID='ok-button'
                    onPress={() => handleClose(false)}
                    style={popupStyles.cancelButton}>
                    <Text style={popupStyles.cancelText}>OK</Text>
                  </Pressable>
                </View>
              </TouchableWithoutFeedback>
            )}

          </Animated.View>     
        </KeyboardAvoidingView>       
    </Modal>
  );
}