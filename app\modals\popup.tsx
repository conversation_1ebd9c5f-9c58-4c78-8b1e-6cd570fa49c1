import { useEffect, useMemo, useRef, useState } from "react";
import { Text, View, StyleSheet, ScrollView, TextInput,
  Image, Button, Pressable, Modal,
  Animated,
  KeyboardAvoidingView,
  Platform,
  Keyboard,
  TouchableWithoutFeedback} from "react-native";
import RadioGroup from 'react-native-radio-buttons-group';
import { EmitData, EmitType, ModalType } from "../helpers/model";
import { ModalData } from "../helpers/model";
import { popupStyles } from "../helpers/stylesheet";
import Close from "@/assets/icons/CloseIcon";
import { useAuth } from "../helpers/authContext";
import { RenderHTML } from 'react-native-render-html';

export function PopUp({
  modalType,
  conversationName,
  isVisible,
  index,
  onClose
}: {
  modalType: ModalType,
  conversationName: string,
  isVisible: boolean,
  index: number
  onClose: (emitData: EmitData) => void
}) {
  const { authData, setAuthData } = useAuth();
  const slideAnim = useRef(new Animated.Value(500)).current;
  const [modalData, setModalData] = useState(new ModalData());
  const [renameText, setRenameText] = useState('');
  const [radioOption, setRadio] = useState('1');
  const [feedbackInput, setFeedback] = useState('');
  const [termsChecked, setTermsChecked] = useState(false);

  const radioButtons = useMemo(() => ([
    {
      id: '1',
      label: 'Not relevant enough',
      value: 'Not relevant enough',
      size: 14
    },
    {
      id: '2',
      label: 'Issue with this content',
      value: 'Issue with this content',
      size: 14
    },
    {
      id: '3',
      label: 'Other',
      value: 'Other',
      size: 14
    }
  ]), []);

  useEffect(() => {
    if (!isVisible) {
      setRenameText('');
      setRadio('1');
      setFeedback('');
      setTermsChecked(false);
    }
  }, [isVisible]);

  // Feedback: Clear input if user changes radio
  useEffect(() => {
    if (radioOption !== '3') setFeedback('');
  }, [radioOption]);

  // Set modal data
  useEffect(() => {
    setRenameText('');
    modalData.modalType !== modalType && setModalData(new ModalData(modalType));
    if (modalType === ModalType.HowToUse) {
      setModalData((prev) => ({ ...prev, content: authData.sitecore.HowToUseText.value }));
    } else if (modalType === ModalType.Disclaimer || modalType === ModalType.TermsOfAgreement) {
      setModalData((prev) => ({ ...prev, content: authData.sitecore.TermsOfUseText.value }));
    } else if (modalType === ModalType.Delete) {
      // TODO - add conversation name to this like "Conversation {name} will be deleted"
      const text = "This conversation will permanently be deleted. \n" +
        "Do you want to continue?";
      setModalData((prev) => ({ ...prev, content: text }));
    }
  }, [modalType]);

  // animation for Modal opening
  useEffect(() => {
    if (isVisible) {
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }).start();
    }
  }, [isVisible]);

  // animation for modal closing
  const handleClose = (isConfirmed: boolean) => {
    Animated.timing(slideAnim, {
      toValue: 500,
      duration: 300,
      useNativeDriver: true,
    }).start(() => {
      slideAnim.setValue(500);
      onClose(getEmitData(isConfirmed));
    });
  };

  const getEmitData = (isConfirmed: boolean) => {
    const getEmitType = () => {
      switch (modalType) {
        case ModalType.Rename:
          return EmitType.rename;
        case ModalType.Delete:
          return EmitType.delete;
        case ModalType.Feedback:
          return EmitType.rateDown;
        default:
          return EmitType.default;
      }
    }
    return ({ ...new EmitData(),
      name: modalType === ModalType.Rename ? renameText.trim() : '',
      feedback: modalType === ModalType.Feedback ? feedbackInput.trim() : '',
      index: modalType === ModalType.Feedback ? index : -1,
      isConfirmed: modalType === ModalType.Feedback ? true : isConfirmed,
      agreedToTerms: termsChecked,
      emitType: getEmitType(),
      message: '',
      conversationId: ''
    });
  }

  const isDisabled = () => {
    if (modalType === ModalType.Rename &&
      (renameText.trim() === '' || renameText.trim() === ' '
        || renameText.toLocaleLowerCase().trim() === 'new conversation')) {
      return true;
    } else if (modalType === ModalType.Feedback &&
      (radioOption === '3' && (feedbackInput.trim() === '' || feedbackInput.trim() === ' '))) {
      return true;
    }
    return false;
  }

  //TO DO - Potentially implement different offset for different iphone
  const getKeyboardOffset = () =>{
    return Platform.OS === 'ios' ?
      (modalType === ModalType.Feedback ? -50 : -150 )
      : 0;
  }

  return (
    <Modal
      animationType="none"
      transparent={true}
      visible={isVisible}>
      <KeyboardAvoidingView
          behavior={ Platform.OS === 'ios' ? 'padding' : 'height' }
          keyboardVerticalOffset={ getKeyboardOffset() }
        style={popupStyles.outerContainer}>
        <Pressable
          testID='popup-backdrop'
          style={popupStyles.backdrop}
          onPress={() => {
            if (modalType !== ModalType.TermsOfAgreement) {
              handleClose(false);
            }
          }}/>
        <Animated.View style={[popupStyles.container,
        { transform: [{ translateY: slideAnim }] }]}>

          {/* Title */}
          <TouchableWithoutFeedback onPress={Keyboard.dismiss} accessible={false}>
            <View style = {[
              popupStyles.header,
              modalType === ModalType.TermsOfAgreement && popupStyles.termsHeader
            ]}>
              <Text style={[
                popupStyles.title,
                modalType === ModalType.TermsOfAgreement && popupStyles.termsTitle
              ]}>{modalData.title}</Text>
              {modalType !== ModalType.TermsOfAgreement && (
                <Pressable testID="x-button" onPress={() => handleClose(false)}>
                  <Close/>
                </Pressable>
              )}
            </View>
          </TouchableWithoutFeedback>

          {/* Body Content */}
          {modalType === ModalType.TermsOfAgreement || modalType === ModalType.Disclaimer ? (
            <TermsOrDisclaimerBody content={modalData.content} />
          ) : modalType === ModalType.Rename ? (
            <RenameBody 
              renameText={renameText} 
              setRenameText={setRenameText} 
              handleClose={handleClose} 
            />
          ) : modalType === ModalType.Feedback ? (
            <FeedbackBody 
              modalData={modalData}
              radioButtons={radioButtons}
              radioOption={radioOption}
              setRadio={setRadio}
              feedbackInput={feedbackInput}
              setFeedback={setFeedback}
              handleClose={handleClose}
            />
          ) : (
            <MiscBody content={modalData.content} />
          )}

          {/* Footer - Confirmation or OK */}
          {modalType === ModalType.TermsOfAgreement ? (
            <TermsFooter 
              termsChecked={termsChecked} 
              setTermsChecked={setTermsChecked} 
              handleClose={handleClose} 
            />
          ) : modalData.requiresConfirmation ? (
            <ConfirmationFooter 
              isDisabled={isDisabled} 
              handleClose={handleClose} 
              modalData={modalData} 
              modalType={modalType} 
            />
          ) : (
            <OkFooter handleClose={handleClose} />
          )}

        </Animated.View>
      </KeyboardAvoidingView>
    </Modal>
  );
}

// --- Modal Body Components ---
function TermsOrDisclaimerBody({ content }: { content: string }) {
  return (
    <ScrollView
      keyboardShouldPersistTaps='never'
      style={{ flex: 1 }}
      horizontal={false}
      contentContainerStyle={{ flexGrow: 1 }}
      showsHorizontalScrollIndicator={false}
      showsVerticalScrollIndicator={false}
    >
      <View testID='terms-popup' style={popupStyles.termsContent}>
        <RenderHTML contentWidth={752} source={{ html: content }} />
      </View>
    </ScrollView>
  );
}

function RenameBody({ renameText, setRenameText, handleClose }: {
  renameText: string;
  setRenameText: (text: string) => void;
  handleClose: (isConfirmed: boolean) => void;
}) {
  return (
    <View testID='rename-popup' style={popupStyles.rename}>
      <View style={popupStyles.renameContainer}>
        <TextInput
          testID='rename-input'
          style={[popupStyles.input,
             { fontStyle: renameText ? 'normal' : 'italic' }]}
          value={renameText}
          onChangeText={setRenameText}
          placeholder="Enter new name"
          placeholderTextColor="grey"
          multiline
          numberOfLines={2}
          onSubmitEditing={() => renameText.trim() === '' || renameText.trim() === ' '
            ? () => {}
            : handleClose(true)
          }
        />
      </View>
    </View>
  );
}

function FeedbackBody({ modalData, radioButtons, radioOption, setRadio, feedbackInput, setFeedback, handleClose }: {
  modalData: ModalData;
  radioButtons: any[];
  radioOption: string;
  setRadio: (id: string) => void;
  feedbackInput: string;
  setFeedback: (text: string) => void;
  handleClose: (isConfirmed: boolean) => void;
}) {
  return (
    <TouchableWithoutFeedback onPress={Keyboard.dismiss} accessible={false}>
      <View testID='feedback-popup' style={popupStyles.bodyFeedback}>
        <Text>{modalData.content}</Text>
        <RadioGroup
          testID='feedback-radio'
          radioButtons={radioButtons}
          onPress={setRadio}
          selectedId={radioOption}
          containerStyle={popupStyles.feedbackRadio}
        />
        <View style={popupStyles.inputContainer}>
          <TextInput
            testID='feedback-input'
            style={[popupStyles.feedbackInput, { fontStyle: feedbackInput ? 'normal' : 'italic' }]}
            onChangeText={setFeedback}
            value={feedbackInput}
            placeholderTextColor="grey"
            placeholder="Tell us more"
            enablesReturnKeyAutomatically
            multiline
            numberOfLines={2}
            onSubmitEditing={() => feedbackInput.trim() === '' || feedbackInput.trim() === ' '
              ? () => {}
              : handleClose(true)}
            editable={radioOption === '3'}
          />
        </View>
      </View>
    </TouchableWithoutFeedback>
  );
}

function MiscBody({ content }: { content: string }) {
  return (
    <ScrollView keyboardShouldPersistTaps='never'>
      <View testID='misc-popup' style={popupStyles.bodyContainer}>
        <View style={popupStyles.body}>
          <RenderHTML contentWidth={300} source={{ html: content }} />
        </View>
      </View>
    </ScrollView>
  );
}

// --- Modal Footer Components ---
function TermsFooter({ termsChecked, setTermsChecked, handleClose }: {
  termsChecked: boolean;
  setTermsChecked: (checked: boolean) => void;
  handleClose: (isConfirmed: boolean) => void;
}) {
  return (
    <TouchableWithoutFeedback onPress={Keyboard.dismiss} accessible={false}>
      <View style={[popupStyles.footer, popupStyles.termsFooter]}>
        <View style={popupStyles.termsCheckboxContainer}>
          <Pressable
            testID="termsOfUseCheckbox"
            onPress={() => setTermsChecked(!termsChecked)}
            style={popupStyles.termsCheckbox}
          >
            <View style={[
              popupStyles.termsCheckboxInner,
              { backgroundColor: termsChecked ? '#004EAA' : '#fff' }
            ]}>
              {termsChecked && <Text style={popupStyles.termsCheckboxText}>✓</Text>}
            </View>
          </Pressable>
          <Text style={popupStyles.termsAgreementText}>I agree to the Terms of Use</Text>
        </View>
        <Pressable
          testID='confirm-button'
          onPress={() => handleClose(true)}
          style={[
            popupStyles.termsContinueButton,
            { backgroundColor: termsChecked ? '#004EAA' : '#f3f3f3' }
          ]}
          disabled={!termsChecked}>
          <Text
            style={[
              popupStyles.termsContinueText,
              { color: termsChecked ? '#fff' : '#aaa' }
            ]}>
            Continue
          </Text>
        </Pressable>
      </View>
    </TouchableWithoutFeedback>
  );
}

function ConfirmationFooter({ isDisabled, handleClose, modalData, modalType }: {
  isDisabled: () => boolean;
  handleClose: (isConfirmed: boolean) => void;
  modalData: ModalData;
  modalType: ModalType;
}) {
  return (
    <TouchableWithoutFeedback onPress={Keyboard.dismiss} accessible={false}>
      <View style={popupStyles.footer}>
        <Pressable
          testID='confirm-button'
          onPress={() => handleClose(true)}
          style={[
            popupStyles.confirmButton,
            isDisabled() && popupStyles.disabledButton
          ]}
          disabled={isDisabled()}>
          <Text
            style={isDisabled() ? popupStyles.disabledText : popupStyles.confirmText}>
            {modalData.submitText}
          </Text>
        </Pressable>
        <Pressable
          testID='cancel-button'
          onPress={() => handleClose(false)}
          style={popupStyles.cancelButton}>
          <Text style={popupStyles.cancelText}>
            {modalType === ModalType.Feedback ? 'Skip' : 'Cancel'}
          </Text>
        </Pressable>
      </View>
    </TouchableWithoutFeedback>
  );
}

function OkFooter({ handleClose }: { handleClose: (isConfirmed: boolean) => void }) {
  return (
    <TouchableWithoutFeedback onPress={Keyboard.dismiss} accessible={false}>
      <View style={popupStyles.footer}>
        <Pressable
          testID='ok-button'
          onPress={() => handleClose(false)}
          style={popupStyles.cancelButton}>
          <Text style={popupStyles.cancelText}>OK</Text>
        </Pressable>
      </View>
    </TouchableWithoutFeedback>
  );
}