import React, { useRef } from 'react';
import { View, ActivityIndicator, StyleSheet } from 'react-native';
import { WebView } from 'react-native-webview';
import { useNavigation } from '@react-navigation/native';

export default function LoginScreen() {
  const navigation = useNavigation();
  const webviewRef = useRef(null);

  // Fixed login and success URLs
  const loginUrl = 'https://gateway-logint.verisk.com/app/paasmobile/auth/login?appType=webview';
  const successPath = 'https://gateway-logint.verisk.com/app/paasmobile/auth/successful-login';

  // Called on every navigation change in the WebView
  const handleNavStateChange = (navState: { url: any; }) => {
    const { url } = navState;
    if (!url) return;

    // Detect success URL (it always ends at /successful-login)
    if (url.includes(successPath)) {
      // Optional: parse query params or hash if needed
      let params: Record<string, string> = {};
      if (url.includes('?')) {
        const queryString = url.split('?')[1];
        queryString.split('&').forEach((part: { split: (arg0: string) => [any, any]; }) => {
          const [key, value] = part.split('=');
          params[key] = decodeURIComponent(value || '');
        });
      }
      // TODO: Use params as needed (e.g. store token)

      // Close the WebView or navigate away
      // If using React Navigation, go back to previous screen:
      navigation.goBack();
      // If using a callback prop: props.onLoginSuccess(params);
    }
  };

  return (
    <View style={styles.container}>
      <WebView
        ref={webviewRef}
        source={{ uri: loginUrl }}
        onNavigationStateChange={handleNavStateChange}
        // Show a loading indicator while loading
        startInLoadingState={true}
        renderLoading={() => (
          <View style={styles.loader}>
            <ActivityIndicator size="large" color="#0000ff" />
          </View>
        )}
        // Optional: control scaling, errors, etc.
        style={styles.webview}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: { flex: 1 },
  webview: { flex: 1 },
  loader: {
    position: 'absolute', top: 0, left: 0, right: 0, bottom: 0,
    justifyContent: 'center', alignItems: 'center',
    backgroundColor: 'rgba(255,255,255,0.8)'
  },
});
