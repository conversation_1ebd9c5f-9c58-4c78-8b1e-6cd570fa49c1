import * as WebBrowser from 'expo-web-browser';
import React, { useEffect } from 'react';
import { Button, View, Alert, Linking } from 'react-native';

WebBrowser.maybeCompleteAuthSession();

export default function Authentication() {
  useEffect(() => {
    const handleRedirect = (event: { url: any; }) => {
      const { url } = event;
      console.log('Redirect URL:', url);

      if (url.includes('auth/successful-login')) {
        WebBrowser.dismissBrowser();
        Alert.alert('Login Successful', 'You have been successfully logged in.');
      }
    };

    const subscription = Linking.addEventListener('url', handleRedirect);

    return () => {
      subscription.remove();
    };
  }, []);

  const openLogin = async () => {
    const result = await WebBrowser.openAuthSessionAsync(
      'https://gateway-logint.verisk.com/app/paasmobile/auth/login?appType=webview',
      'https://localhost:8081'
    );

    console.log('result', result);
  };

  return (
    <View>
      <Button
        title="Open Login"
        onPress={openLogin}
      />
    </View>
  );
}