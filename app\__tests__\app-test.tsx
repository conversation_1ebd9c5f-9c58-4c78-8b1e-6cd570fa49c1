import { act, cleanup, fireEvent, render, screen, waitFor } from '@testing-library/react-native';
import { Chatbox } from '@/app/components/chatbox';
import { AuthData } from '../helpers/model';
import { useAuth } from '../helpers/authContext';

jest.mock('../helpers/service', () => ({
  getUserConversations: jest.fn(() => Promise.resolve(mockedConversations)),
  postMessage: jest.fn(() => Promise.resolve(mockedPostMessageResponse)),
  postRate: jest.fn(() => Promise.resolve({})),
  fetchSitecoreData: jest.fn(() => Promise.resolve(mockSiteCoreData)),
  checkAgreementHistory: jest.fn(() => Promise.resolve(true)) 
}));

jest.mock('expo-linking', () => {
    const module: typeof import('expo-linking') = {
        ...jest.requireActual('expo-linking'),
        createURL: jest.fn(),
    };
  
    return module;
  });

jest.mock('../helpers/authContext', () => ({
  useAuth: jest.fn(() => ({
        authData: { 
        isAuth: true, 
        userId: 'userId', 
        accessToken: 'test token 1', 
        username: 'test user',
        sitecore: {
          NewConversationText: { value: 'test' },
          GreetingMessageText: { value: 'test' },
          InputPlaceholderText: { value: 'test' },
          ConversationStartersText: { value: "How can I help you today?" },
          HowToUseText: { value: 'test' },
          BotNameText: { value: 'test' },
          TermsOfUseText: { value: 'test' },
        }
      },
    setAuthData: jest.fn((data: AuthData) => {
      data.username = 'test user';
      data.accessToken = 'test token 2';
      data.isAuth = true;
      data.userId = 'userId';
        data.sitecore = {
          NewConversationText: { value: 'test' },
          GreetingMessageText: { value: 'test' },
          InputPlaceholderText: { value: 'test' },
          ConversationStartersText: { value: "How can I help you today?" },
          HowToUseText: { value: 'test' },
          BotNameText: { value: 'test' },
          TermsOfUseText: { value: 'test' },
        };

    }),
    getAuthData: jest.fn((data: AuthData) => {
        data.username = 'test user';
        data.accessToken = 'test token 3';
        data.isAuth = true;
        data.userId = 'userId';
        data.sitecore = {
          NewConversationText: { value: 'test' },
          GreetingMessageText: { value: 'test' },
          InputPlaceholderText: { value: 'test' },
          ConversationStartersText: { value: "How can I help you today?" },
          HowToUseText: { value: 'test' },
          BotNameText: { value: 'test' },
          TermsOfUseText: { value: 'test' },
        };
      }),
    getUserInfo: jest.fn(),
    getAccessToken: jest.fn(),
    getIdToken: jest.fn(),
    getUser: jest.fn(),
    getUserSessionTokenExpiration: jest.fn(),
    getUserSessionTokenExpirationDate: jest.fn(),
    getUserSessionTokenExpirationTime: jest.fn(),
    isAuthenticated: true
  }))
}));

const mockSiteCoreData = {
  NewConversationText: { value: 'test' },
  GreetingMessageText: { value: 'test' },
  InputPlaceholderText: { value: 'test' },
  ConversationStartersText: { value: "How can I help you today?" },
  HowToUseText: { value: 'test' },
  BotNameText: { value: 'test' },
  TermsOfUseText: { value: 'test' },
};

const mockedConversations = [
  {
    conversationId: 'conv1',
    name: 'conversation-name1',
    time: '1111-11-11:11-11-11',
    disabled: false,
    content: [
      {
        userMsg: 'default user message',
        summary: 'default_message',
        botMsg: 'default botmessage',
        source: [],
        time: '1111-11-11:11-11-11',
        rating: 0,
        comment: 'default_comment',
      },
    ],
  },
  {
    conversationId: 'conv2',
    name: 'conversation-name2',
    time: '1111-11-11:11-11-12',
    disabled: false,
    content: [
      {
        userMsg: 'default user message',
        summary: 'default_message',
        botMsg: 'default botmessage',
        source: [],
        time: '1111-11-11:11-11-12',
        rating: 0,
        comment: 'default_comment',
      },
    ],
  },
];

const mockedPostMessageResponse = {
  answer: 'Mocked API response',
  conversationId: 'conv3',
  isDisabled: false,
  metadata: [
    {
      source: 'default_source',
      title: 'default_title',
      docType: 'default_docType',
      itemId: 'default_itemId',
      updateDate: new Date('1111-11-11'),
    },
  ],
};

describe('Chatbox', () => {
  // beforeEach(() => {
  //   (useAuth as jest.Mock).mockReturnValue({
  //     authData: { 
  //       isAuth: true, 
  //       userId: 'userId', 
  //       accessToken: 'test token 4', 
  //       username: 'test user',
  //       sitecore: {
  //         NewConversationText: { value: 'test' },
  //         GreetingMessageText: { value: 'test' },
  //         InputPlaceholderText: { value: 'test' },
  //         ConversationStartersText: { value: "How can I help you today?" },
  //         HowToUseText: { value: 'test' },
  //         BotNameText: { value: 'test' },
  //         TermsOfUseText: { value: 'test' },
  //       }
  //     },
  //     setAuthData: jest.fn(),
  //   });
  // });

  // afterEach(() => {
  //   jest.clearAllMocks();
  //   cleanup();
  // });

  test('matches snapshot', async () => {
    const { toJSON } = render(<Chatbox />);
    await waitFor(() => {
      expect(toJSON()).toMatchSnapshot();
    });
  });

  it ('renders the header', async () => {
      const { getByText } = render(<Chatbox />);
      await waitFor(() => {
        expect(getByText('Premium Audit Advisory Service')).toBeOnTheScreen();
      });
  });

  it ('renders the footer', async () => {
    const { getByText } = render(<Chatbox />);
    await waitFor(() => {
      expect(getByText('Copyright © 2024 Insurance Services Office, Inc, All rights reserved.')).toBeOnTheScreen();
    });
  });

  it('handles new conversation creation', async () => {
    const { getByTestId } = render(<Chatbox />);

    // Verify greeting is not visible
    await waitFor(() => {
      expect(screen.queryByTestId('greeting')).toBeNull();
    });

    // Find the new conversation button and press it
    act(() => { fireEvent.press(getByTestId('new-conv-button')); });

    // Verify greeting message is visible
    await waitFor(() => {
      expect(getByTestId('greeting')).toBeVisible();
    });
  });

  it('logs out the user', async () => {
    const { getByTestId } = render(<Chatbox />);

    // Find the user icon and click it
    act(() => { fireEvent.press(getByTestId('icon-button')); });

    // Verify user profile drop down is visible
    await waitFor(() => {
      expect(getByTestId('logout-button')).toBeVisible();
    });

    // Click the backdrop to close
    act(() => { fireEvent.press(getByTestId('user-icon-backdrop')); });

    // Verify drop down is not visible
    await waitFor(() => {
      expect(screen.queryByTestId('logout-button')).toBeNull();
    });

    // Find the user icon and click it
    act(() => { fireEvent.press(getByTestId('icon-button')); });

    // Click the logout button
    act(() => { fireEvent.press(getByTestId('logout-button')); });

    // Verify drop down is not visible
    await waitFor(() => {
      expect(screen.queryByTestId('logout-button')).toBeNull();
    });

    expect(useAuth().setAuthData).toHaveBeenCalledWith({
      accessToken: '',
      userId: '',
      isAuth: false,
      username: '',
    });
  });
});

  describe('Sidebar', () => {
    beforeEach(() => {
      (useAuth as jest.Mock).mockReturnValue({
        authData: { isAuth: true, userId: 'userId', accessToken: 'testToken', username: 'test user' },
        setAuthData: jest.fn(),
      });
    });
  
    afterEach(() => {
      jest.clearAllMocks();
      cleanup();
    });

    it('toggles the sidebar visibility', async () => {
      const { getByTestId } = render(<Chatbox />);
  
      // Find the sidebar button and press it
      await waitFor(() => {
        act(() => { fireEvent.press(getByTestId('sidebar-button')); });
      });

      // Wait for the sidebar to appear and verify its visibility
      await waitFor(() => {
        expect(getByTestId('sidebar')).toBeVisible();
      });
  
      // Press the sidebar button again to toggle visibility
      act(() => { 
        fireEvent.press(getByTestId('sidebar-button'));
        fireEvent.press(getByTestId('sidebar-backdrop')); 
      });
  
      // Wait for the sidebar to disappear and verify it's not visible
      await waitFor(() => {
        expect(screen.queryByTestId('sidebar')).toBeNull();
      });
    });

    it('handles how to use pop-up', async () => {
      const { getByTestId } = render(<Chatbox />);

      // Find the sidebar button and press it
      act(() => { fireEvent.press(getByTestId('sidebar-button')); });

      // Find the How to Use button and press it
      act(() => { fireEvent.press(getByTestId('how-to-use-button')); });

      // Verify Pop-Up
      await waitFor(() => {
        expect(screen.getByTestId('misc-popup')).toBeVisible();
      });

      // click the backdrop to close
      act(() => { fireEvent.press(getByTestId('popup-backdrop')); });

      // Verify Pop-Up
      await waitFor(() => {
        expect(screen.queryByTestId('misc-popup')).not.toBeVisible();
      });

      // Open popup again
      act(() => { fireEvent.press(getByTestId('how-to-use-button')); });

      // click the X to close
      act(() => { fireEvent.press(getByTestId('x-button')); });

      // Verify Pop-Up
      await waitFor(() => {
        expect(screen.queryByTestId('misc-popup')).not.toBeVisible();
      });

      // Open popup again
      act(() => { fireEvent.press(getByTestId('how-to-use-button')); });

      // click the ok to close
      act(() => { fireEvent.press(getByTestId('ok-button')); });

      // Verify Pop-Up
      await waitFor(() => {
        expect(screen.queryByTestId('misc-popup')).not.toBeVisible();
      });
    });

    it('handles sidebar search', async () => {
      const { getByTestId } = render(<Chatbox />);

      // Find the sidebar button and press it
      await waitFor(() => {
        act(() => { fireEvent.press(getByTestId('sidebar-button')); });
      });

      // Find message input and type a message
      const input = getByTestId('search-input');
      fireEvent.changeText(input, 'name1');

      // Verify input change
      await waitFor(() => {
        expect(input.props.value).toBe('name1');
      });

      // Submit Message
      fireEvent.press(getByTestId('search-button'));

      // Verify conv1 shows, but conv2 is not visible
      await waitFor(() => {
        expect(screen.getByText('conversation-name1')).toBeVisible();

        // TODO: Userdata is [] when filtering in Unit Test
        expect(screen.queryByText('conversation-name2')).not.toBeVisible();
      });
    });


    it('handles dislaimer pop-up', async () => {
      const { getByTestId } = render(<Chatbox />);

      // Find the sidebar button and press it
      act(() => { fireEvent.press(getByTestId('sidebar-button')); });

      // Find the How to Use button and press it
      act(() => { fireEvent.press(getByTestId('disclaimer-button')); });

      // Verify Pop-Up
      await waitFor(() => {
        expect(screen.getByTestId('misc-popup')).toBeVisible();
      });

      // click the backdrop to close
      act(() => { fireEvent.press(getByTestId('popup-backdrop')); });

      // Verify Pop-Up
      await waitFor(() => {
        expect(screen.queryByTestId('misc-popup')).not.toBeVisible();
      });
    });

    describe('Messages', () => {
      beforeEach(() => {
        (useAuth as jest.Mock).mockReturnValue({
          authData: { isAuth: true, userId: 'userId', accessToken: 'testToken', username: 'test user' },
          setAuthData: jest.fn(),
        });
      });
    
      afterEach(() => {
        jest.clearAllMocks();
        cleanup();
      });
  
    
      it ('sends a message, copy, rate up', async () => {
        const { getByTestId } = render(<Chatbox />);

        // Find message input and type a message
        const input = getByTestId('message-input');
        fireEvent.changeText(input, 'Summarize the Davis-Bacon Act');

        // Verify input change
        await waitFor(() => {
          expect(input.props.value).toBe('Summarize the Davis-Bacon Act');
        });

        // Submit Message
        act(() => { fireEvent.press(getByTestId('message-submit-button')); });

        // Verify response from AI
        await waitFor(() => {
          expect(screen.getByText('Mocked API response')).toBeVisible();
        });

        // Rate Up Message
        act(() => { fireEvent.press(getByTestId('rate-up-0')); });

        // Verify if the ThumbUp function has isActive = true
        await waitFor(() => {
          expect(screen.getByTestId('rate-up-0')).toBeDisabled();
        });

        // Copy Message
        act(() => { fireEvent.press(getByTestId('copy-0')); });

        // TODO: Verify if the Copy function has isActive = true
      });    

      it ('sends a message, rate down, submit feedback', async () => {
        const { getByTestId } = render(<Chatbox />);

        // Find message input and type a message
        const input = getByTestId('message-input');
        fireEvent.changeText(input, 'Summarize the Davis-Bacon Act');

        // Verify input change
        await waitFor(() => {
          expect(input.props.value).toBe('Summarize the Davis-Bacon Act');
        });

        // Submit Message
        act(() => { fireEvent.press(getByTestId('message-submit-button')); });

        // Verify response from AI
        await waitFor(() => {
          expect(screen.getByText('Mocked API response')).toBeVisible();
        });

        // Rate Down Message
        await waitFor(() => {
          act(() => { fireEvent.press(getByTestId('rate-down-0')); });
        });

        // Verify Feedback popup is visible
        await waitFor(() => {
          expect(screen.getByTestId('feedback-popup')).toBeVisible();
        });

        // Click radio options
        const radio1 = screen.getByRole('radio', { name: 'Not relevant enough' });
        const radio2 = screen.getByRole('radio', { name: 'Issue with this content' });
        const radio3 = screen.getByRole('radio', { name: 'Other' });

        expect(radio1).toBeChecked();
        expect(radio2).not.toBeChecked();
        expect(radio3).not.toBeChecked();
      
        fireEvent.press(radio2);
      
        expect(radio1).not.toBeChecked();
        expect(radio2).toBeChecked();
        expect(radio3).not.toBeChecked();
      
        fireEvent.press(radio3);
      
        expect(radio1).not.toBeChecked();
        expect(radio2).not.toBeChecked();
        expect(radio3).toBeChecked();

        // Input feedback
        const feedback = getByTestId('feedback-input');
        fireEvent.changeText(feedback, 'Sample feedback');

        // Verify input change
        await waitFor(() => {
          expect(feedback.props.value).toBe('Sample feedback');
        });

        // Submit Message
        act(() => { fireEvent.press(getByTestId('confirm-button')); });

        // Verify pop up is not visible
        await waitFor(() => {
          expect(screen.queryByTestId('feedback-popup')).not.toBeVisible();
        });
      });  
  });
});