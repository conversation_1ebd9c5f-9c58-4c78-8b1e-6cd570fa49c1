import React from 'react';
import * as Font from 'expo-font';
import Svg, { Path } from 'react-native-svg';
import { View, StyleSheet } from 'react-native';

const styles = StyleSheet.create({
    icon: {
        height: 30,
        width: 30,
        padding: 2,
        marginRight: 5
    },  
});

export default function ThumbUp({
    isActive,
}: { 
    isActive: boolean, 
}) { 

    const getColor = () => {
        return isActive ? "#008e18" : "#00358E";
    }

    const getThumbUp = () => {
        return (
            <Svg width="24" height="24" viewBox="0 0 24 24" 
            fill="none">
                <Path id="Vector" 
                d="M8.81818 18.5455H15.3636C15.9673 18.5455 16.4836 18.1818 16.7018 
                17.6582L18.8982 12.5309C18.9636 12.3636 19 12.1891 19 12V10.5455C19 9.74545 18.3455 
                9.09091 17.5455 9.09091H12.9564L13.6473 5.76727L13.6691 5.53455C13.6691 5.23636 13.5455 
                4.96 13.3491 4.76364L13.2853 4.70042C12.8942 4.31305 12.2636 4.31455 
                11.8744 4.70376L7.78545 8.79273C7.52364 9.05455 7.36364 9.41818 7.36364 
                9.81818V17.0909C7.36364 17.8909 8.01818 18.5455 8.81818 18.5455ZM8.81818 
                9.81818L11.9745 6.66182L11 10.5455H17.5455V12L15.3636 17.0909H8.81818V9.81818ZM3 
                10.8182C3 10.2659 3.44772 9.81818 4 9.81818H4.90909C5.46137 9.81818 5.90909 10.2659 
                5.90909 10.8182V17.5455C5.90909 18.0977 5.46138 18.5455 4.90909 18.5455H4C3.44772 
                18.5455 3 18.0977 3 17.5455V10.8182Z" fill={getColor()}/>
            </Svg>
        );
    };

    return (
    <View style={styles.icon}>
        { getThumbUp() }
    </View>
    );
};