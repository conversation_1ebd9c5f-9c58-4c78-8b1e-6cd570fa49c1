import React from 'react';
import { View, StyleSheet } from 'react-native';
import Svg, { <PERSON><PERSON><PERSON><PERSON>, De<PERSON>, G, Path } from 'react-native-svg';

const styles = StyleSheet.create({
    icon: {
        height: 30,
        width: 30,
        right: 4
    },  
});

export default function EllipsesLogo(props: any) { 
    const getIcon = (props: any) => {
        return (
            <Svg xmlns="http://www.w3.org/2000/svg" fill="none" {...props}>
            <Path
              fill="#00358E"
              d="M15.71 7c0-1.283-1.01-2.333-2.245-2.333-1.234 0-2.244 1.05-2.244 2.333 0 1.283 1.01 2.333 2.244 2.333 1.234 0 2.244-1.05 2.244-2.333Zm0 14c0-1.283-1.01-2.333-2.245-2.333-1.234 0-2.244 1.05-2.244 2.333 0 1.283 1.01 2.333 2.244 2.333 1.234 0 2.244-1.05 2.244-2.333Zm0-7c0-1.283-1.01-2.333-2.245-2.333-1.234 0-2.244 1.05-2.244 2.333 0 1.283 1.01 2.333 2.244 2.333 1.234 0 2.244-1.05 2.244-2.333Z"
            />
          </Svg>
        );
    };

    return (
    <View style={styles.icon}>
        { getIcon(null) }
    </View>
    );
};