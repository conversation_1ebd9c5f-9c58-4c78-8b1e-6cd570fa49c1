import React from 'react';
import { View, StyleSheet } from 'react-native';
import Svg, { G, Mask, Path } from 'react-native-svg';

const styles = StyleSheet.create({
  icon: {
      height: 40,
      width: 42,
  },  
});

interface SidebarIconProps {
  isActive: boolean;
}

export default function SidebarIcon({ isActive }: SidebarIconProps) { 
  const getIcon = (props: any) => {
        return (
            <Svg xmlns="http://www.w3.org/2000/svg" fill="none" {...props}>
            <Mask id="a" width={40} height={40} x={0} y={0} maskUnits="userSpaceOnUse">
              <Path fill="#D9D9D9" d="M0 0h40v40H0z" />
            </Mask>
            <G mask="url(#a)">
              <Path
                fill="#00358E"
                d="M5 30v-3.333h30V30H5Zm0-8.333v-3.334h30v3.334H5Zm0-8.334V10h30v3.333H5Z"
              />
            </G>
          </Svg>
        );
    };

    return (
    <View style={styles.icon}>
        { getIcon('') }
    </View>
    );
};