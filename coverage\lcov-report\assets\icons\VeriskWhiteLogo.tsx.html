
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for assets/icons/VeriskWhiteLogo.tsx</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../index.html">All files</a> / <a href="index.html">assets/icons</a> VeriskWhiteLogo.tsx</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">100% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>4/4</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">100% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>0/0</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">100% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>2/2</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">100% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>4/4</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line high'></div>
    <pre><table class="coverage">
<tr><td class="line-count quiet"><a name='L1'></a><a href='#L1'>1</a>
<a name='L2'></a><a href='#L2'>2</a>
<a name='L3'></a><a href='#L3'>3</a>
<a name='L4'></a><a href='#L4'>4</a>
<a name='L5'></a><a href='#L5'>5</a>
<a name='L6'></a><a href='#L6'>6</a>
<a name='L7'></a><a href='#L7'>7</a>
<a name='L8'></a><a href='#L8'>8</a>
<a name='L9'></a><a href='#L9'>9</a>
<a name='L10'></a><a href='#L10'>10</a>
<a name='L11'></a><a href='#L11'>11</a>
<a name='L12'></a><a href='#L12'>12</a>
<a name='L13'></a><a href='#L13'>13</a>
<a name='L14'></a><a href='#L14'>14</a>
<a name='L15'></a><a href='#L15'>15</a>
<a name='L16'></a><a href='#L16'>16</a>
<a name='L17'></a><a href='#L17'>17</a>
<a name='L18'></a><a href='#L18'>18</a>
<a name='L19'></a><a href='#L19'>19</a>
<a name='L20'></a><a href='#L20'>20</a>
<a name='L21'></a><a href='#L21'>21</a>
<a name='L22'></a><a href='#L22'>22</a>
<a name='L23'></a><a href='#L23'>23</a>
<a name='L24'></a><a href='#L24'>24</a>
<a name='L25'></a><a href='#L25'>25</a>
<a name='L26'></a><a href='#L26'>26</a>
<a name='L27'></a><a href='#L27'>27</a>
<a name='L28'></a><a href='#L28'>28</a>
<a name='L29'></a><a href='#L29'>29</a>
<a name='L30'></a><a href='#L30'>30</a>
<a name='L31'></a><a href='#L31'>31</a>
<a name='L32'></a><a href='#L32'>32</a>
<a name='L33'></a><a href='#L33'>33</a>
<a name='L34'></a><a href='#L34'>34</a>
<a name='L35'></a><a href='#L35'>35</a>
<a name='L36'></a><a href='#L36'>36</a></td><td class="line-coverage quiet"><span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">54x</span>
<span class="cline-any cline-yes">54x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">54x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">import React from 'react';
import { View, StyleSheet } from 'react-native';
import Svg, { ClipPath, Defs, G, Path } from 'react-native-svg';
&nbsp;
const styles = StyleSheet.create({
    icon: {
        width: 165,
        height: 52,
    },  
});
&nbsp;
export default function VeriskWhiteLogo(props: any) { 
    const getIcon = (props: any) =&gt; {
        return (
          &lt;Svg xmlns="http://www.w3.org/2000/svg" fill="none" {...props}&gt;
          &lt;G clipPath="url(#a)"&gt;
            &lt;Path
              fill="#fff"
              d="M106.933 17.533c.062 0 .124.067.124.2V21.8c0 .133-.124.2-.248.2-.494-.2-1.668-.6-3.337-.533-.989 0-1.916.2-2.844.533-.061 0-.123.133-.123.2v15c0 .133-.062.2-.186.2h-3.894c-.123 0-.185-.067-.185-.2V19.267c0-.067.062-.134.062-.134.494-.333 3.276-1.933 7.17-1.933 1.73-.133 3.09.2 3.461.333Zm-13.784 8.6v2.134c0 .133-.062.2-.185.2H80.602c-.124 0-.186.066-.186.2.186 1.533.804 2.8 1.916 3.733 1.051.867 2.41 1.4 3.894 1.4 2.967 0 5.192-1.333 5.996-1.8.124-.067.247 0 .247.133V35.8c0 .067 0 .133-.061.133-.433.334-2.535 1.8-6.49 1.8-2.844 0-5.255-.933-6.985-2.733-1.731-1.867-2.658-4.4-2.658-7.467 0-3.066.865-5.733 2.534-7.666a8.346 8.346 0 0 1 6.367-2.934c5.068 0 7.973 3.334 7.973 9.2Zm-4.265-.733c.124 0 .186-.067.186-.2-.186-2.867-1.546-4.333-4.018-4.333-2.225 0-3.832 1.6-4.45 4.333 0 .133.061.267.185.267l8.097-.067Zm39.992 2.067c-.927-1-2.411-1.667-4.574-2.2-2.102-.6-2.843-1.267-2.843-2.467 0-1.333.989-2.133 2.658-2.133 2.225-.067 4.203 1.266 4.944 1.866.124.067.309 0 .309-.133v-3.8c0-.067 0-.133-.061-.133-.433-.267-2.349-1.534-5.254-1.534-.495 0-1.051 0-1.607.067-2.844.267-5.007 2.867-5.007 5.933 0 3.467 1.545 5.2 5.501 6.134 2.287.666 3.338 1.2 3.338 2.733 0 1.733-1.607 2.133-2.905 2.133-2.473 0-4.636-1.533-5.378-2.066-.123-.067-.309 0-.309.133v3.933c0 .067 0 .134.062.134.371.266 2.349 1.533 5.687 1.6 2.039 0 3.77-.534 5.006-1.467 1.298-1.067 1.978-2.6 1.978-4.667-.062-1.733-.556-3.066-1.545-4.066ZM112.125 14.4c1.484 0 2.658-1.333 2.658-2.933 0-1.6-1.236-2.867-2.658-2.867-1.421 0-2.719 1.267-2.719 2.867s1.236 2.933 2.719 2.933Zm-1.978 22.933h3.894c.124 0 .186-.066.186-.2V17.6c0-.133-.062-.2-.186-.2h-3.894c-.123 0-.185.067-.185.2v19.533c0 .067.062.2.185.2Zm31.029-11 7.727-8.6c.123-.133 0-.333-.124-.333h-4.759c-.062 0-.124 0-.124.067l-6.366 7.4c-.124.133-.31.066-.31-.134V10.867c0-.134-.061-.2-.185-.2h-3.894c-.124 0-.185.066-.185.2v26.266c0 .134.061.2.185.2h3.894c.124 0 .185-.066.185-.2v-8.6c0-.2.186-.266.31-.133l6.675 8.867c.041.044.083.066.124.066h4.636c.123 0 .247-.2.123-.333l-7.973-10.4c0-.133 0-.2.061-.267Zm10.632-5.666v-2.534c0-.066.062-.066.062-.066h.803c.062 0 .062-.067.062-.067v-.467c0-.066-.062-.066-.062-.066h-2.349c-.061 0-.061.066-.061.066V18c0 .067.061.067.061.067h.804c.062 0 .062.066.062.066v2.534c0 .066.062.066.062.066h.494l.062-.066Zm3.029.066h.494c.041 0 .062-.022.062-.066l.618-2.134h.062l.309 2.134c0 .066.062.066.062.066h.494c.062 0 .062-.066.062-.066l-.433-3.2c0-.067-.061-.067-.061-.067h-.68c-.042 0-.062.022-.062.067l-.618 2.266h-.062l-.618-2.266c0-.067-.062-.067-.062-.067h-.68c-.041 0-.062.022-.062.067l-.432 3.2c0 .044.02.066.061.066h.495c.041 0 .062-.022.062-.066l.309-2.134c0-.066.062-.066.062 0l.618 2.134c-.062 0-.062.066 0 .066ZM73.926 10.8l-8.53 20.933a.17.17 0 0 1-.309 0L56.619 10.8c0-.067-.062-.133-.185-.133h-4.39c-.123 0-.184.133-.184.266l10.013 24.4c1.298 3.2 5.563 3.2 6.86 0l9.952-24.4c.062-.133-.061-.266-.185-.266h-4.389c-.061 0-.123.066-.185.133Zm-70.65.8c.309-.6.927-.933 1.545-.933h6.985c.247 0 .433.266.309.533l-1.854 4.533a.337.337 0 0 1-.31.2L1.794 16c-.248 0-.433-.267-.31-.533A27.03 27.03 0 0 1 3.276 11.6Zm6.243-6.267c-.371 0-.495-.466-.186-.666C13.103 1.733 17.74 0 22.747 0c5.006 0 9.642 1.733 13.413 4.6.309.2.123.667-.186.667l-26.455.066ZM15.7 42.867a.337.337 0 0 1 .31-.2h19.902c.371 0 .495.466.186.666C32.328 46.267 27.69 48 22.685 48c-2.844 0-5.563-.533-8.036-1.533-.185-.067-.309-.334-.185-.534l1.236-3.066Zm2.472-6.067 1.855-4.533a.337.337 0 0 1 .309-.2h23.24c.248 0 .433.266.31.533-.495 1.4-1.113 2.667-1.793 3.933-.309.534-.865.934-1.483.934H18.482c-.248-.134-.433-.4-.31-.667Zm27.135-15.133c.062.733.124 1.533.124 2.333 0 .8-.062 1.533-.123 2.333 0 .2-.186.334-.371.334H22.87c-.247 0-.433-.267-.309-.534l1.236-3c.433-1.066 1.422-1.8 2.534-1.8h18.606c.185 0 .309.134.37.334Zm-1.421-6.2c-.495-1.4-1.113-2.667-1.793-3.934-.309-.533-.865-.933-1.483-.933H19.47c-1.05 0-2.04.667-2.472 1.733L10.013 29.6c-.061.067-.123.133-.185.133s-.124-.066-.185-.133L6.985 23a2.534 2.534 0 0 0-2.411-1.667H.494c-.185 0-.309.134-.37.334A27.89 27.89 0 0 0 0 24c0 .8.062 1.533.124 2.333 0 .2.185.334.37.334h2.04c.124 0 .248.066.31.2C3.4 28.2 6.303 35.4 8.22 40c.309.733.989 1.133 1.607 1.133.68 0 1.298-.4 1.607-1.133l9.086-22.267c.433-1.066 1.36-1.733 2.473-1.733h20.583c.247 0 .432-.267.309-.533Z"
            /&gt;
          &lt;/G&gt;
          &lt;Defs&gt;
            &lt;ClipPath id="a"&gt;
              &lt;Path fill="#fff" d="M0 0h157v48H0z" /&gt;
            &lt;/ClipPath&gt;
          &lt;/Defs&gt;
        &lt;/Svg&gt;
        );
    };
&nbsp;
    return (
    &lt;View style={styles.icon}&gt;
        { getIcon(null) }
    &lt;/View&gt;
    );
};</pre></td></tr></table></pre>

                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-04-01T01:07:34.081Z
            </div>
        <script src="../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../sorter.js"></script>
        <script src="../../block-navigation.js"></script>
    </body>
</html>
    