import React, { useEffect, useState } from 'react';
import { View, Text, Image, Pressable, Linking } from 'react-native';
import { useRouter } from 'expo-router';
import { ApiUserData, EmitData, EmitType, Message, SitecoreData, Source, UserData, ModalType } from "../helpers/model";
import { msgStyles } from "../helpers/stylesheet";
import { Messages } from "./messages";
import { getUserConversations, fetchSitecoreData, checkAgreementHistory,postAgreementHistory, postMessage, postRate } from "../helpers/service";
import { useAuth } from "../helpers/authContext";
import { useAppState } from "../helpers/appStateContext";
import { AppLayout } from "./AppLayout";
import { PopUp } from "../modals/popup";


export function Chatbox() {
  const { authData, setAuthData } = useAuth();
  const [showTermsModal, setShowTermsModal] = useState(false);
  const [isInitialized, setIsInitialized] = useState(false);
  const router = useRouter();
  const {
    userData,
    setUserData,
    isLoading,
    setLoading,
    setDisabled
  } = useAppState();
  const hardCode = false;

  // Handler for opening PAAS sources using Expo Router
  const handleStackOpen = (source: { title: string; source: string }) => {
    const sourceId = `${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
    router.push({
      pathname: '/paas-source/[id]' as any,
      params: {
        id: sourceId,
        title: source.title,
        url: source.source,
      },
    });
  };

  // Once user is authenticated, fetch sitecore data & check agreement history
  useEffect(() => {
    if (hardCode) {
      initializeHardCodedAuth();
      setIsInitialized(true);
      return;
    }

    if (authData.isAuth === true) {
      setDisabled(false);

      const load = async () => {
        if (!authData.sitecore.TermsOfUseText.value) {
          await getSitecoreData();
          return;
        }
        if (authData.sitecore.TermsOfUseText.value) {
          await checkTermsAgreement();
        }
        setIsInitialized(true);
      };

      load();
    }
  }, [authData.isAuth, authData.sitecore]);

  // Once user has agreed to terms of use, initialize user data
  useEffect(() => {
    if (!isInitialized) {
      return;
    }
     //authData.hasAgreed
    if (authData.hasAgreed) {
      const load = async () => {
        await initUserData();
      };
      load();
    } else if (authData.hasAgreed === false) {
      promptTermsOfUse();
    }
  }, [authData.hasAgreed, isInitialized]);

  function promptTermsOfUse() {
    // open popup
    setShowTermsModal(true);
    // make app not load until setDisplayApp is true
    // setModalState({ modalType: modalType, isVisible: !modalState.isVisible, conversationName: '' });

  }

  async function getSitecoreData() {
    const sitecoreData = await fetchSitecoreData();
    setAuthData({ ...authData, sitecore: sitecoreData });
    return true; 
  }

    const initializeHardCodedAuth = () => {
    setAuthData({
      ...authData,
      accessToken: process.env.EXPO_PUBLIC_ACCESS_TOKEN || "",
      isAuth: true,
      userId: 'I29246',
      username: 'Shelby Kurz'
    });
  };

  const checkTermsAgreement = async () => {
    if (
      authData.sitecore?.TermsOfUseText &&
      authData.sitecore.TermsOfUseText.value &&
      authData.userId &&
      authData.accessToken
    ) {
      const agreed = await checkAgreementHistory(
        authData.userId,
        authData.accessToken,
        authData.sitecore.TermsOfUseText.value
      );

      setAuthData({ ...authData, hasAgreed: agreed });
    }
  };

  function handleAcceptTerms() {
    postAgreementHistory(authData.userId, authData.accessToken);
    setShowTermsModal(false);
    setAuthData({ ...authData, hasAgreed: true });
  }

function formatBotMessage(
  metadata: Message["metadata"] | Source[],
  response: string,
  isCapturingResponse = false,
  stackOpenHandler?: (source: { title: string; source: string }) => void
) {
  let formattedSources: any[] = [];
  let greetingStyling = false;

  if (response === authData.sitecore.GreetingMessageText?.value) {
    greetingStyling = true;
  }

  if (metadata?.length > 0) {
    formattedSources = metadata.map((metadataItem: any) => {
      const docTypeLower = metadataItem.docType.toLowerCase();
      const isTrainingManual = docTypeLower.includes("training manual");
      const isIndustryGuide = docTypeLower.includes("industry guides");

      let url = "";

      if (isTrainingManual) {
        if (metadataItem?.parentItemId) {
          url = `/PAAS/training-manual?id=${metadataItem.parentItemId.replace(
            /[{()}]/g,
            ""
          )}`;
          if (metadataItem?.itemId) {
            url += `&chapterid=${metadataItem.itemId.replace(/[{()}]/g, "")}`;
          }
        } else if (metadataItem?.itemId) {
          url = `/PAAS/training-manual?id=${metadataItem.itemId.replace(
            /[{()}]/g,
            ""
          )}`;
        }
      } else if (isIndustryGuide) {
        if (metadataItem?.parentItemId) {
          url = `/PAAS/industryguide?id=${metadataItem.parentItemId.replace(
            /[{()}]/g,
            ""
          )}`;
          if (metadataItem?.itemId) {
            url += `&chapterid=${metadataItem.itemId.replace(/[{()}]/g, "")}`;
          }
        } else if (metadataItem?.itemId) {
          url = `/PAAS/industryguide?id=${metadataItem.itemId.replace(
            /[{()}]/g,
            ""
          )}`;
        }
      } else {
        url = `/paas/search/?contentType=${metadataItem?.docType?.slice(
          0,
          -1
        )}&id=${metadataItem?.itemId?.replace(/[{()}]/g, "")}`;
      }

      return {
        title: metadataItem.title,
        source: url,
      };
    });
  }

  // Format the main response content
  const formattedContent = response
    .split("\n\n")
    .map((message) => {
      message = message.trim();
      if (message.includes("\n")) {
        return message
          .split("\n")
          .map((subMessage) => {
            subMessage = subMessage.trim();
            if (subMessage.startsWith("-")) {
              return `• ${subMessage.replace(/^-/, "").trim()}`;
            } else {
              return subMessage;
            }
          })
          .join("\n");
      }

      if (message.startsWith("-")) {
        return `• ${message.trim().replace(/^-/, "")}`;
      } else {
        return message;
      }
    })
    .join("\n\n");

  return (
    <View style={msgStyles.messageContainer}>
      <Text
        style={[
          msgStyles.message,
          greetingStyling && msgStyles.greeting,
        ]}
      >
        {formattedContent}
      </Text>
      {formattedSources.length > 0 && (
        <View style={msgStyles.sourcesContainer}>
          <Text style={msgStyles.sourcesLabel}>PAAS Source:</Text>
          {formattedSources.map((source, index) => (
            <Pressable
              key={index}
              onPress={() => {
                if (stackOpenHandler) {
                  stackOpenHandler(source);
                } else {
                  Linking.openURL(source.source);
                }
              }}
              style={msgStyles.sourceLink}
            >
              <Text style={msgStyles.sourceLinkText}>{source.title}</Text>
            </Pressable>
          ))}
        </View>
      )}
    </View>
  );
}

  const handleEmitChange = async (emitData: EmitData) => {
    if (!emitData || emitData.isConfirmed === false) return;

    switch (emitData.emitType) {
      case EmitType.message:
        await handleSendMessage(emitData.message);
        break;
      case EmitType.rateUp:
      case EmitType.rateDown:
        await handleRateMessage(emitData);
        break;
      default:
        break;
    }
  };
  const handleSendMessage = async (message: string) => {
    setLoading(true);

    try {
      const isNewConversation = userData.conversations.find(conv => conv.isActive)?.title === "New Conversation";

      if (isNewConversation) {
        setUserData(currentUserData => ({
          ...currentUserData,
          conversations: [
            ...currentUserData.conversations.map(conv => ({ ...conv, isActive: false })),
            {
              title: message,
              isActive: true,
              messageHistory: [{
                userMsg: message,
                botMsg: '',
                time: new Date(),
                rating: 0,
                metadata: []
              }],
              conversationId: '',
              isDisabled: false,
              date: new Date()
            }
          ]
        }));

        const response = await postMessage(authData.userId, authData.accessToken, message, '');
        setUserData(currentUserData => {
          const updatedConversations = [...currentUserData.conversations];
          const activeIndex = updatedConversations.findIndex(conv => conv.isActive);
          if (activeIndex !== -1) {
            updatedConversations[activeIndex] = {
              ...updatedConversations[activeIndex],
              conversationId: response.conversationId,
              messageHistory: [{
                ...updatedConversations[activeIndex].messageHistory[0],
                botMsg: formatBotMessage(response.metadata || [], response.answer, false, handleStackOpen)
              }]
            };
          }
          return { ...currentUserData, conversations: updatedConversations };
        });
      }
    } catch (error) {
      console.error('Error sending message:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleRateMessage = async (emitData: EmitData) => {
    try {
      const activeConversation = userData.conversations.find(conv => conv.isActive);
      if (activeConversation && emitData.index !== undefined) {
        const rating = emitData.emitType === EmitType.rateUp ? 1 : -1;
        setUserData(currentUserData => {
          const updatedConversations = currentUserData.conversations.map(conv =>
            conv.isActive ? {
              ...conv,
              messageHistory: conv.messageHistory.map((msg, index) =>
                index === emitData.index ? { ...msg, rating } : msg
              )
            } : conv
          );
          return { ...currentUserData, conversations: updatedConversations };
        });

        await postRate(
          authData.userId,
          authData.accessToken,
          activeConversation.conversationId,
          emitData.index,
          rating,
          emitData.feedback || ''
        );
      }
    } catch (error) {
      console.error('Error rating message:', error);
    }
  };

async function initUserData() {
  // get & set user history from API (append to bot message)
  const res = await getUserConversations(
    authData.userId,
    authData.accessToken
  );


  // if user has conversation history
  if (res !== null && (!Array.isArray(res) || res.length > 0)) {
    // convert API data to UserData cache
    const apiConversations = res.map((conversation: ApiUserData) => {
      // date received is in format yyyy-mm-dd-hh-mm-ss
      const formattedTime = conversation.time.replace(/-/g, (_, offset) => offset === 10 ? 'T' : (offset > 10 ? ':' : '-'));
      return {
        isActive: false,
        isDisabled: conversation.disabled,
        conversationId: conversation.conversationId,
        title: conversation.name,
        date: new Date(Date.parse(formattedTime)),
        messageHistory: conversation.content.map((message) => ({
          userMsg: message.userMsg,
          botMsg: formatBotMessage(message.source || [], message.botMsg as string, false, handleStackOpen),
          time: new Date(Date.parse(formattedTime)),
          rating: message.rating,
          metadata: message.source
        })),
      };
    });

    // Append greeting message + temporary new conversation to conversation cache
    setUserData(currentUserData => ({
      ...currentUserData,
      conversations: [
        currentUserData.conversations[0],
        ...apiConversations
      ]
    }));
    return true;
  }
}

  return (
    <AppLayout showNewConversation={true} formatBotMessage={formatBotMessage}>
      {authData.sitecore?.TermsOfUseText?.value && (
        <PopUp
          modalType={ModalType.TermsOfAgreement}
          isVisible={showTermsModal}
          conversationName={""}
          index={0}
          onClose={(emitData) => {
            setShowTermsModal(false);
            if (emitData.isConfirmed && emitData.agreedToTerms) {
              handleAcceptTerms();
            }
          }}
        />
      )}
      <Messages
        userData={userData}
        isLoading={isLoading}
        emitChange={handleEmitChange}
        emitDisabled={(disabled) => setDisabled(disabled)}
      />
    </AppLayout>
  );
}
