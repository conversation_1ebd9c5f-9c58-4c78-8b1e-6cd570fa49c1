import React, { useState } from "react";
import { View, Text, ScrollView, Pressable, StyleSheet } from "react-native";
import { useLocalSearchParams, useRouter } from "expo-router";
import { AppLayout } from "../components/AppLayout";
import { AppFooter } from "../components/AppFooter";
import {
  generatePaasBreadcrumbs,
  parsePaasSourceInfo,
  generateDocumentTitle,
} from "../helpers/breadcrumbUtils";

export default function PaasSourceScreen() {
  const { id, title, url } = useLocalSearchParams<{
    id: string;
    title: string;
    url: string;
  }>();
  const router = useRouter();
  const [activeTab, setActiveTab] = useState<"class-info" | "related-links">(
    "class-info"
  );

  // Parse source information for dynamic content
  const sourceInfo = parsePaasSourceInfo(url || "");
  const documentTitle = generateDocumentTitle(title || "", sourceInfo);
  const breadcrumbs = generatePaasBreadcrumbs(documentTitle, router, url || "");

  const renderBreadcrumb = () => {
    return (
      <View style={styles.breadcrumbContainer}>
        <View style={styles.breadcrumbContent}>
          {breadcrumbs.map((breadcrumb, index) => (
            <React.Fragment key={index}>
              {breadcrumb.onPress ? (
                <Pressable
                  onPress={breadcrumb.onPress}
                  style={styles.breadcrumbItem}
                >
                  <Text style={styles.breadcrumbText}>{breadcrumb.title}</Text>
                </Pressable>
              ) : (
                <Text style={styles.breadcrumbCurrent}>{breadcrumb.title}</Text>
              )}
              {index < breadcrumbs.length - 1 && (
                <Text style={styles.breadcrumbSeparator}> {">"} </Text>
              )}
            </React.Fragment>
          ))}
        </View>
      </View>
    );
  };

  const renderTabContent = () => {
    if (activeTab === "class-info") {
      return (
        <>
          <View style={styles.contentSection}>
            <Text style={styles.sectionTitle}>Note</Text>
            <Text style={styles.sectionText}>
              Code 2923 and Code 2883 "Furniture Mfg. - NOC - Wood" shall not be
              assigned to the same employer unless the operations described by
              these classifications are conducted as separate and distinct
              businesses.
            </Text>
          </View>

          <View style={styles.contentSection}>
            <Text style={styles.sectionTitle}>Contemplated Operations</Text>
            <Text style={styles.sectionText}>
              This is a New York state special classification. The NCCI
              classifies similar operations to code 2923.
            </Text>
            <Text style={styles.sectionText}>
              This classification applies to the manufacturing of musical
              instruments made from wood. Such include, but are not limited to
              violins, guitars, cellos, harmonicas, drums, bass drums,
              clarinets, pianos, player pianos, and organs.
            </Text>
            <Text style={styles.sectionText}>
              Operations manufacturing and the tanning of skins used in
              manufacturing wooden drums, tambourines and banjos have been
              considered incidental to the operations assigned to Code 2923.
            </Text>
            <Text style={styles.sectionText}>
              Organ building and installation contemplates the manufacture of
              the complete organ including installation, as well as the
              manufacture of the keyboards, sounding boards, action boards,
              metal frames and organ pipes. This includes the wind chests, wind
              reservoirs and blower systems.
            </Text>
          </View>

          <View style={styles.contentSection}>
            <Text style={styles.sectionTitle}>Operations Not Contemplated</Text>
            <Text style={styles.sectionText}>
              This classification does not apply to the following operations:
            </Text>
          </View>
        </>
      );
    } else {
      return (
        <View style={styles.contentSection}>
          <Text style={styles.sectionTitle}>Related Links</Text>
          <Text style={styles.sectionText}>
            Related links and references will be displayed here.
          </Text>
          <Text style={styles.sectionText}>Source URL: {url}</Text>
          <Text style={styles.sectionText}>Source ID: {id}</Text>
        </View>
      );
    }
  };

  return (
    <AppLayout showNewConversation={false}>
      <ScrollView style={styles.contentContainer}>
        <View style={styles.content}>
          {renderBreadcrumb()}

          <Text style={styles.title}>{documentTitle}</Text>

          <View style={styles.lobSection}>
            <Text style={styles.lobLabel}>
              LOB: <Text style={styles.lobValue}>Workers Compensation</Text>
            </Text>
            <Text style={styles.lobLabel}>
              Applicable in: <Text style={styles.lobValue}>NY</Text>
            </Text>
          </View>

          <View style={styles.tabContainer}>
            <Pressable
              style={[
                styles.tab,
                activeTab === "class-info" && styles.activeTab,
              ]}
              onPress={() => setActiveTab("class-info")}
            >
              <Text
                style={[
                  styles.tabText,
                  activeTab === "class-info" && styles.activeTabText,
                ]}
              >
                Class Information
              </Text>
            </Pressable>
            <Pressable
              style={[
                styles.tab,
                activeTab === "related-links" && styles.activeTab,
              ]}
              onPress={() => setActiveTab("related-links")}
            >
              <Text
                style={[
                  styles.tabText,
                  activeTab === "related-links" && styles.activeTabText,
                ]}
              >
                Related Links
              </Text>
            </Pressable>
          </View>

          {renderTabContent()}

          <View style={{ marginTop: 40 }}>
            <AppFooter />
          </View>
        </View>
      </ScrollView>
    </AppLayout>
  );
}

const styles = StyleSheet.create({
  breadcrumbContainer: {
    paddingBottom: 16,
  },
  breadcrumbContent: {
    flexDirection: "row",
    alignItems: "center",
  },
  breadcrumbItem: {},
  breadcrumbText: {
    fontSize: 14,
    color: "#00358E",
    fontWeight: "500",
  },
  breadcrumbSeparator: {
    fontSize: 14,
    color: "#6c757d",
  },
  breadcrumbCurrent: {
    fontSize: 14,
    color: "#495057",
    fontWeight: "500",
  },
  contentContainer: {
    flex: 1,
  },
  content: {
    padding: 20,
  },
  title: {
    fontSize: 18,
    fontWeight: "bold",
    color: "#212529",
    marginBottom: 16,
  },
  lobSection: {
    marginBottom: 20,
  },
  lobLabel: {
    fontSize: 14,
    color: "#495057",
    marginBottom: 4,
  },
  lobValue: {
    fontWeight: "600",
    color: "#212529",
  },
  tabContainer: {
    flexDirection: "row",
    borderBottomWidth: 1,
    borderBottomColor: "#dee2e6",
    marginBottom: 20,
  },
  tab: {
    paddingVertical: 12,
    paddingHorizontal: 16,
    marginRight: 20,
  },
  activeTab: {
    borderBottomWidth: 2,
    borderBottomColor: "#00358E",
  },
  tabText: {
    fontSize: 14,
    color: "#6c757d",
    fontWeight: "500",
  },
  activeTabText: {
    color: "#00358E",
    fontWeight: "600",
  },
  contentSection: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: "bold",
    color: "#212529",
    marginBottom: 12,
  },
  sectionText: {
    fontSize: 14,
    color: "#495057",
    lineHeight: 20,
    marginBottom: 12,
  },
});
