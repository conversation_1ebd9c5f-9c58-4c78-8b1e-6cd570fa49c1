import React from 'react';
import { View, StyleSheet } from 'react-native';
import Svg, { Mask, Path, G } from 'react-native-svg';

export default function SubmitIcon({
    isActive,
}: { 
    isActive: boolean, 
}) { 
    const styles = StyleSheet.create({
        icon: {
          width: 24,
          height: 24,
          fontFamily: 'Roboto',
          fontSize: 13,
          backgroundColor: isActive ? '#FFC600' : '#DBDBDB',
          borderRadius: 12, 
          alignContent: 'center',
          justifyContent: 'center',
        },  
        arrow: {
          height: 24,
          width: 24,
          padding: 4
        }
    });

    const getArrowWhite = (props: any) => {
        return (
            <Svg xmlns="http://www.w3.org/2000/svg" fill="none" {...props}>
            <Mask id="a" width={16} height={16} x={0} y={0} maskUnits="userSpaceOnUse">
              <Path fill="#D9D9D9" d="M0 0h16v16H0z" />
            </Mask>
            <G mask="url(#a)">
              <Path
                fill="#fff"
                d="M7.333 13.333V5.217L3.6 8.95 2.667 8 8 2.667 13.333 8l-.933.95-3.733-3.733v8.116H7.333Z"
              />
            </G>
          </Svg>
        );
    }

    const getArrowBlack = (props: any) => {
        return (
            <Svg xmlns="http://www.w3.org/2000/svg" fill="none" {...props}>
            <Mask id="a" width={16} height={16} x={0} y={0} maskUnits="userSpaceOnUse">
              <Path fill="#D9D9D9" d="M0 0h16v16H0z" />
            </Mask>
            <G mask="url(#a)">
              <Path
                fill="#1A1A1A"
                d="M7.333 13.333V5.217L3.6 8.95 2.667 8 8 2.667 13.333 8l-.933.95-3.733-3.733v8.116H7.333Z"
              />
            </G>
          </Svg>
        );
    }


    return (
    <View>
        <View style={styles.icon}>
          <View style={styles.arrow}>
            {isActive ? getArrowBlack(null) : getArrowWhite(null)}
          </View>
        </View>
    </View>
    );
};