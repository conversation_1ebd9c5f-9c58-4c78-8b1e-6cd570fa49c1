import React from 'react';
import { View, StyleSheet } from 'react-native';
import Svg, { ClipP<PERSON>, Defs, G, Mask, Path } from 'react-native-svg';

const styles = StyleSheet.create({
    icon: {
      height: '50%',
      width: '50%',
    },  
});

export default function VeriskGroupLogo(props: any) { 
    const getIcon = (props: any) => {
        return (
            <Svg xmlns="http://www.w3.org/2000/svg" fill="none" {...props}>
            <Path fill="#E6EBF4" d="M0 0h126v36H0z" />
            <G clipPath="url(#a)">
              <Path fill="#fff" d="M-149-1269h433v5698h-433z" />
              <G clipPath="url(#b)">
                <Mask id="d" fill="#fff">
                  <Path d="M-149-238h430V55h-430v-293Z" />
                </Mask>
                <Path fill="#00358E" d="M-149-238h430V55h-430v-293Z" />
                <G clipPath="url(#c)">
                  <Path
                    fill="#fff"
                    d="M33.77 18c0 9.936-7.556 18-16.885 18C7.557 36 0 27.936 0 18S7.557 0 16.885 0c9.329 0 16.886 8.064 16.886 18Z"
                  />
                  <G fill="#0069A6">
                    <Path d="M31.163 8.424a17.568 17.568 0 0 0-2.876-3.672 39.78 39.78 0 0 0-22.837 0 17.57 17.57 0 0 0-2.875 3.672c9.128-2.484 19.46-2.484 28.587 0Z" />
                    <Path d="M18.958 10.98c1.037-1.656 1.806-2.772 2.174-3.312-6.487-.504-13.107.144-19.16 1.908-.434.9-.835 1.836-1.136 2.844 5.684-1.116 11.937-1.584 18.122-1.44ZM16.35 25.056c-.668 1.044-1.47 2.232-2.273 3.42 6.018.288 12.104-.36 17.687-2.016.435-.9.836-1.836 1.137-2.808-5.049.936-10.532 1.44-16.05 1.44-.133-.036-.3-.036-.5-.036ZM18.357 11.88c-6.086-.108-12.238.396-17.789 1.476-.2.792-.334 1.62-.434 2.484 5.049-.324 10.566-.504 16.116-.54.77-1.26 1.471-2.412 2.107-3.42Z" />
                    <Path d="M13.475 29.34c-.936 1.368-1.806 2.556-2.24 3.168 5.684.792 11.535.396 17.052-1.26a17.571 17.571 0 0 0 2.876-3.672 55.403 55.403 0 0 1-14.278 1.872c-1.137 0-2.273-.036-3.41-.108ZM26.649 3.312C23.907 1.224 20.529 0 16.885 0c-3.644 0-7.021 1.224-9.763 3.312a40.713 40.713 0 0 1 19.527 0ZM19.66 19.728c4.849-.036 9.63-.216 14.044-.504.033-.396.033-.792.033-1.188 0-.396-.033-.792-.033-1.188-3.812-.252-7.891-.432-12.07-.504-.636 1.08-1.204 2.124-1.974 3.384ZM22.202 15.372c3.945.072 7.824.216 11.435.468-.1-.864-.234-1.692-.435-2.484-2.875-.576-5.918-.972-9.028-1.224a210.083 210.083 0 0 0-1.972 3.24ZM15.648 16.272c-3.243 0-6.453.072-9.596.216.3.684.903 1.944 1.505 3.096 1.972.072 3.979.108 5.985.144.635-1.008 1.337-2.16 2.106-3.456ZM8.092 20.52l.1.216c.501.864 1.07 1.8 1.538 2.556.134.216.267.396.368.576.3.468.468.756.468.756s.134-.252.434-.684c.1-.18.235-.396.402-.648.401-.648.936-1.548 1.605-2.664-1.672 0-3.31-.036-4.915-.108ZM16.952 24.12c5.584 0 11.134-.504 16.25-1.512.2-.792.334-1.656.435-2.484-4.58.288-9.53.468-14.545.504-.568 1.008-1.27 2.124-2.14 3.492ZM3.176 20.34c-1.036-.072-2.04-.108-3.042-.18.1.864.234 1.692.434 2.484 1.338.252 2.675.468 4.08.684-.636-1.152-1.07-2.124-1.472-2.988ZM7.356 27.612a51.823 51.823 0 0 1-2.14-3.276c-1.505-.18-2.942-.432-4.38-.684.3.972.669 1.908 1.137 2.808 1.772.504 3.61.936 5.45 1.26-.034-.036-.034-.072-.067-.108ZM8.158 28.8a46.838 46.838 0 0 1-5.583-1.188 17.57 17.57 0 0 0 2.875 3.672c1.772.54 3.611.936 5.45 1.224-1.137-1.44-1.973-2.628-2.742-3.708Z" />
                    <Path d="M7.122 32.688C9.864 34.776 13.24 36 16.885 36c3.645 0 7.022-1.224 9.764-3.312a40.745 40.745 0 0 1-19.527 0Z" />
                  </G>
                  <G fill="#fff">
                    <Path d="M60.118 23.292c.034 3.78 2.541 6.804 6.22 6.804 2.741 0 4.513-1.764 5.784-4.212l2.742 1.692c-1.806 3.636-4.882 5.724-8.76 5.724-5.651 0-9.296-4.644-9.296-10.512s3.277-11.052 9.195-11.052 9.329 5.436 8.994 11.52H60.118v.036ZM71.62 20.34c-.434-2.952-2.741-5.328-5.584-5.328-2.842 0-5.383 2.376-5.784 5.328H71.62ZM80.681 14.544h.067c1.137-1.62 2.107-2.772 4.213-2.772 1.104 0 1.973.396 2.91.972l-1.539 3.168c-.635-.468-1.103-.9-1.939-.9-3.544 0-3.712 4.896-3.712 7.56v10.224h-3.243V12.348h3.243v2.196ZM93.387 5.544c0 1.404-1.036 2.484-2.307 2.484-1.27 0-2.307-1.08-2.307-2.484S89.81 3.06 91.08 3.06c1.27.036 2.307 1.152 2.307 2.484Zm-.702 27.216h-3.243V12.348h3.243V32.76ZM103.886 17.064c-.434-1.08-1.404-2.088-2.574-2.088s-2.34.972-2.34 2.268c0 1.872 2.173 2.556 4.413 3.564 2.207 1.008 4.413 2.448 4.413 5.868 0 3.852-2.909 6.66-6.419 6.66-3.177 0-5.684-1.944-6.688-5.184l2.876-1.296c.802 1.908 1.739 3.276 3.879 3.276 1.738 0 3.076-1.26 3.076-3.132 0-4.572-8.56-2.988-8.56-9.432 0-3.42 2.575-5.796 5.651-5.796 2.173 0 4.213 1.656 5.049 3.78l-2.776 1.512ZM124.282 12.348h-4.313l-6.888 7.56V3.096h-3.243V32.76h3.243v-8.388l.836-.9 7.624 9.288h4.28l-9.63-11.736 8.091-8.676ZM56.908 3.096l-7.222 20.808-7.222-20.808h-3.645l10.767 30.276.1.288L60.553 3.096h-3.645Z" />
                  </G>
                  <Path
                    fill="#fff"
                    d="M125.887 12.744h-.635v-.396h1.772v.396h-.635v1.944h-.468l-.034-1.944Z"
                  />
                </G>
              </G>
              <Path fill="#fff" d="M-149-237h430v-2h-430v2Z" mask="url(#d)" />
            </G>
            <Defs>
              <ClipPath id="a">
                <Path fill="#fff" d="M-149-1269h433v5698h-433z" />
              </ClipPath>
              <ClipPath id="b">
                <Path fill="#fff" d="M-149-238h430V55h-430v-293Z" />
              </ClipPath>
              <ClipPath id="c">
                <Path fill="#fff" d="M0 0h130v36H0z" />
              </ClipPath>
            </Defs>
          </Svg>
        );
    };

    return (
    <View style={styles.icon}>
        { getIcon(null) }
    </View>
    );
};