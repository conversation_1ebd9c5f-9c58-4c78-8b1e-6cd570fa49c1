import React from 'react';
import { View, StyleSheet } from 'react-native';
import Svg, { ClipP<PERSON>, Defs, G, Mask, Path } from 'react-native-svg';

const styles = StyleSheet.create({
    icon: {
        height: 30,
        width: 30
    },  
});

export default function AddIcon(props: any) { 
    const getIcon = (props: any) => {
        return (
            <Svg xmlns="http://www.w3.org/2000/svg" fill="none" {...props}>
            <Mask
              id="a"
              width={20}
              height={20}
              x={0}
              y={0}
              maskUnits="userSpaceOnUse"
              style={{
                maskType: "alpha",
              }}
            >
              <Path fill="#D9D9D9" d="M0 0h20v20H0z" />
            </Mask>
            <G mask="url(#a)">
              <Path
                fill="#1A1A1A"
                d="M9.167 10.833h-5V9.167h5v-5h1.666v5h5v1.666h-5v5H9.167v-5Z"
              />
            </G>
          </Svg>
        );
    };

    return (
    <View style={styles.icon}>
        { getIcon(null) }
    </View>
    );
};