import React from 'react';
import * as Font from 'expo-font';
import Svg, { Path } from 'react-native-svg';
import { View } from 'react-native';

const loadFonts = async () => {
    await Font.loadAsync({
      'Roboto': require('../../assets/fonts/Roboto/Roboto-Regular.ttf'),
      'Roboto-Bold': require('../../assets/fonts/Roboto/Roboto-Bold.ttf'),
    });
  };

export default function Microphone({
    isActive,
}: { 
    isActive: boolean, 
}) { 
    const getColor = () => {
        return isActive ? 'black' : 'black';
    }

    const getMicrophoneIcon = (props: any) => {
        return (
            <Svg xmlns="http://www.w3.org/2000/svg" fill="none" {...props}>
            <Path
              stroke="#374151"
              d="m17 16 4-4m0 0-4-4m4 4H7m6 4v1a3 3 0 0 1-3 3H6a3 3 0 0 1-3-3V7a3 3 0 0 1 3-3h4a3 3 0 0 1 3 3v1"
            />
          </Svg>
        );
    };


    return (
    <View>
        <View style={{
            width: 24,
            height: 24,
            //fontFamily: 'Roboto', //TODO
           // fontSize: 13,
            backgroundColor: isActive ? 'lightgreen' : 'white',
            borderRadius: 12,
            alignItems: 'center',
            justifyContent: 'center',
            //color: isActive ? 'white' : 'orange',
        }}>
            {getMicrophoneIcon(null)}
        </View>
    </View>
    );
};