
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for assets/icons/VeriskLogo.tsx</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../index.html">All files</a> / <a href="index.html">assets/icons</a> VeriskLogo.tsx</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">100% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>4/4</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">100% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>0/0</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">100% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>2/2</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">100% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>4/4</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line high'></div>
    <pre><table class="coverage">
<tr><td class="line-count quiet"><a name='L1'></a><a href='#L1'>1</a>
<a name='L2'></a><a href='#L2'>2</a>
<a name='L3'></a><a href='#L3'>3</a>
<a name='L4'></a><a href='#L4'>4</a>
<a name='L5'></a><a href='#L5'>5</a>
<a name='L6'></a><a href='#L6'>6</a>
<a name='L7'></a><a href='#L7'>7</a>
<a name='L8'></a><a href='#L8'>8</a>
<a name='L9'></a><a href='#L9'>9</a>
<a name='L10'></a><a href='#L10'>10</a>
<a name='L11'></a><a href='#L11'>11</a>
<a name='L12'></a><a href='#L12'>12</a>
<a name='L13'></a><a href='#L13'>13</a>
<a name='L14'></a><a href='#L14'>14</a>
<a name='L15'></a><a href='#L15'>15</a>
<a name='L16'></a><a href='#L16'>16</a>
<a name='L17'></a><a href='#L17'>17</a>
<a name='L18'></a><a href='#L18'>18</a>
<a name='L19'></a><a href='#L19'>19</a>
<a name='L20'></a><a href='#L20'>20</a>
<a name='L21'></a><a href='#L21'>21</a>
<a name='L22'></a><a href='#L22'>22</a>
<a name='L23'></a><a href='#L23'>23</a>
<a name='L24'></a><a href='#L24'>24</a>
<a name='L25'></a><a href='#L25'>25</a>
<a name='L26'></a><a href='#L26'>26</a>
<a name='L27'></a><a href='#L27'>27</a>
<a name='L28'></a><a href='#L28'>28</a>
<a name='L29'></a><a href='#L29'>29</a>
<a name='L30'></a><a href='#L30'>30</a>
<a name='L31'></a><a href='#L31'>31</a>
<a name='L32'></a><a href='#L32'>32</a>
<a name='L33'></a><a href='#L33'>33</a>
<a name='L34'></a><a href='#L34'>34</a>
<a name='L35'></a><a href='#L35'>35</a>
<a name='L36'></a><a href='#L36'>36</a>
<a name='L37'></a><a href='#L37'>37</a>
<a name='L38'></a><a href='#L38'>38</a>
<a name='L39'></a><a href='#L39'>39</a>
<a name='L40'></a><a href='#L40'>40</a>
<a name='L41'></a><a href='#L41'>41</a></td><td class="line-coverage quiet"><span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">54x</span>
<span class="cline-any cline-yes">54x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">54x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">import React from 'react';
import { View, StyleSheet } from 'react-native';
import Svg, { ClipPath, Defs, G, Path } from 'react-native-svg';
&nbsp;
const styles = StyleSheet.create({
    icon: {
        height: 40,
        width: 100,
        paddingTop: 5,
    },  
});
&nbsp;
export default function VeriskLogo(props: any) { 
    const getIcon = (props: any) =&gt; {
        return (
          &lt;Svg xmlns="http://www.w3.org/2000/svg" fill="none" {...props}&gt;
          &lt;G clipPath="url(#a)"&gt;
            &lt;Path
              fill="#00358E"
              d="M64.534 10.39a.112.112 0 0 1 .078.104v2.279a.108.108 0 0 1-.145.1c-.291-.107-1.01-.328-2.029-.316-.608 0-1.167.1-1.716.302a.11.11 0 0 0-.07.1v8.38c0 .06-.049.11-.109.11H58.18a.108.108 0 0 1-.108-.11V11.305c0-.038.022-.075.052-.094.313-.183 1.984-1.089 4.311-1.089a7.71 7.71 0 0 1 2.096.265l.004.004Zm-8.343 4.796v1.197c0 .06-.049.108-.108.108h-7.445a.109.109 0 0 0-.108.12 3.02 3.02 0 0 0 1.16 2.085c.634.496 1.462.768 2.335.768 1.779.019 3.151-.727 3.606-1.01a.107.107 0 0 1 .164.089v2.051a.1.1 0 0 1-.044.086c-.246.175-1.53 1-3.898 1.014-1.715 0-3.174-.537-4.218-1.548-1.06-1.025-1.619-2.472-1.619-4.188 0-1.72.534-3.2 1.54-4.282.978-1.048 2.343-1.626 3.846-1.626 3.043 0 4.789 1.873 4.789 5.14v-.004Zm-2.544-.406c.06 0 .112-.053.108-.112-.1-1.597-.936-2.436-2.424-2.436-1.335 0-2.331.899-2.689 2.42a.108.108 0 0 0 .104.131h4.905l-.004-.003Zm24.112 1.17c-.574-.54-1.458-.928-2.775-1.215-1.253-.343-1.697-.709-1.697-1.388 0-.73.608-1.186 1.586-1.186 1.353-.044 2.547.728 2.976 1.045.07.052.171 0 .171-.086v-2.134a.105.105 0 0 0-.048-.09c-.242-.163-1.406-.864-3.174-.838-.302 0-.63.015-.966.048a3.334 3.334 0 0 0-3.025 3.316c-.01 1.936.918 2.902 3.308 3.45 1.4.37 2.022.679 2.022 1.536 0 .981-.959 1.186-1.76 1.186-1.496 0-2.798-.842-3.249-1.17a.108.108 0 0 0-.171.085v2.215a.11.11 0 0 0 .048.09c.239.164 1.399.873 3.424.891 1.245 0 2.286-.283 3.01-.824.801-.597 1.204-1.47 1.204-2.596 0-1.022-.28-1.76-.88-2.327l-.004-.007ZM67.65 8.638c.876 0 1.618-.754 1.618-1.645s-.742-1.619-1.619-1.619c-.876 0-1.644.728-1.644 1.619s.753 1.645 1.644 1.645ZM66.45 21.444h2.368c.06 0 .108-.048.108-.108V10.401a.108.108 0 0 0-.108-.108h-2.364a.108.108 0 0 0-.108.108v10.935c0 .06.048.108.108.108h-.004Zm18.734-6.146 4.673-4.815c.067-.067.019-.183-.078-.183h-2.898c-.03 0-.06.011-.078.034l-3.827 4.155c-.067.07-.186.026-.186-.071V6.645a.108.108 0 0 0-.109-.108h-2.364a.108.108 0 0 0-.108.108V21.34c0 .06.048.108.108.108h2.368c.06 0 .108-.048.108-.108v-4.815c0-.1.127-.145.19-.067l4.021 4.95a.104.104 0 0 0 .082.04h2.809c.09 0 .141-.104.082-.175L85.18 15.44a.104.104 0 0 1 .008-.142h-.004ZM44.625 6.6 39.49 18.3c-.037.086-.156.086-.197 0L34.156 6.6a.111.111 0 0 0-.097-.063h-2.637a.104.104 0 0 0-.097.15L37.32 20.34c.79 1.805 3.353 1.805 4.143 0l5.994-13.654a.107.107 0 0 0-.097-.149h-2.637a.107.107 0 0 0-.097.063ZM2 7.014c.194-.313.54-.503.914-.503l4.226.011c.156 0 .257.16.197.302L6.21 9.375a.213.213 0 0 1-.198.127l-4.9-.022a.217.217 0 0 1-.202-.291c.298-.761.664-1.492 1.093-2.178l-.004.003Zm3.76-3.48c-.21 0-.295-.268-.127-.387A13.835 13.835 0 0 1 13.729.573c3.025.008 5.814.977 8.078 2.611.168.123.079.388-.127.388L5.76 3.535ZM9.443 24.56a.213.213 0 0 1 .198-.127l11.995.03c.209 0 .294.268.127.387a13.802 13.802 0 0 1-8.098 2.574 13.826 13.826 0 0 1-4.848-.884.215.215 0 0 1-.12-.287l.75-1.693h-.004Zm1.507-3.41 1.127-2.55a.213.213 0 0 1 .197-.127l14.013.037c.149 0 .253.15.2.291a13.006 13.006 0 0 1-1.096 2.186c-.193.313-.54.503-.913.503l-13.334-.033a.216.216 0 0 1-.197-.302l.003-.004Zm16.377-8.413a12.43 12.43 0 0 1-.008 2.596.218.218 0 0 1-.216.193l-13.326-.033a.217.217 0 0 1-.197-.302l.738-1.671a1.684 1.684 0 0 1 1.548-1.003l11.245.03a.21.21 0 0 1 .212.193l.004-.003Z"
            /&gt;
            &lt;Path
              fill="#2A7DE1"
              d="M26.5 9.23a13.457 13.457 0 0 0-1.09-2.19 1.072 1.072 0 0 0-.91-.503H11.756c-.642 0-1.22.377-1.477.966l-4.23 9.648a.109.109 0 0 1-.*************** 0 0 1-.097-.064l-1.608-3.68a1.614 1.614 0 0 0-1.477-.967H.28a.209.209 0 0 0-.213.194c-.045.43-.067.865-.067 1.31 0 .443.022.865.063 1.29a.217.217 0 0 0 .213.194h1.23c.087 0 .161.048.195.126l3.252 7.348c.19.425.586.638.98.638.396 0 .795-.213.985-.642l5.476-12.468a1.61 1.61 0 0 1 1.476-.962h12.424c.15 0 .254-.15.201-.291l.004-.011Z"
            /&gt;
          &lt;/G&gt;
          &lt;Defs&gt;
            &lt;ClipPath id="a"&gt;
              &lt;Path fill="#fff" d="M0 .573h90v26.853H0z" /&gt;
            &lt;/ClipPath&gt;
          &lt;/Defs&gt;
        &lt;/Svg&gt;
        );
    };
&nbsp;
    return (
    &lt;View style={styles.icon}&gt;
        { getIcon(null) }
    &lt;/View&gt;
    );
};</pre></td></tr></table></pre>

                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-04-01T01:07:34.081Z
            </div>
        <script src="../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../sorter.js"></script>
        <script src="../../block-navigation.js"></script>
    </body>
</html>
    