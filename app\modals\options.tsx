import { useEffect, useState } from "react";
import { Text, View, Image, Pressable, Modal, 
    } from "react-native";
import * as Font from 'expo-font';
import { EmitData, EmitType, ModalData, ModalState, ModalType } from "../helpers/model";
import { PopUp } from "./popup";
import { optionsStyles } from "../helpers/stylesheet";
import EditIcon from "@/assets/icons/EditIcon";
import DeleteIcon from "@/assets/icons/DeleteIcon";

export function Options({
  isVisible,
  onClose
}: {
  isVisible: boolean, 
  onClose: (emitData: EmitData) => void,
}) { 
  const [modalState, setModalState] = useState(new ModalState());
  const openOptions = (modalType: ModalType) => {
    setModalState(prevState => ({...prevState, modalType: modalType, isVisible: true }));
  };
  const handleClose = (emitData: EmitData) => {
    setModalState(prevState => ({ ...prevState, isVisible: false }));
    if (emitData.isConfirmed) onClose(emitData); 
    if (emitData.emitType === EmitType.default && !emitData.isConfirmed) onClose(emitData);  
  }

  return (
    <Modal
    animationType="none"
    transparent={true}
    visible={isVisible}>
        <View testID="options-popup" style={optionsStyles.container}>
            <Pressable 
              testID='options-outer-backdrop'
              style={optionsStyles.backdrop} 
              onPress={() => handleClose(new EmitData()) }/>
            <Pressable 
              testID='options-inner-backdrop'
              style={optionsStyles.containerInner} 
              onPress={() => handleClose(new EmitData())}>

                <View style={optionsStyles.content}>
                    <Pressable 
                      testID="rename-option"
                      onPress={()=>{ openOptions(ModalType.Rename) }} 
                      style={optionsStyles.tab}>
                        <EditIcon />
                        <Text style={optionsStyles.text}>
                            Rename
                        </Text>
                    </Pressable>

                    <Pressable 
                      testID="delete-option"
                      onPress={()=>{ openOptions(ModalType.Delete) }} 
                      style={[optionsStyles.tab, { backgroundColor: 'rgba(140, 140, 140, 0.05)' }]}>
                        <DeleteIcon />
                        <Text style={optionsStyles.text}>
                            Delete
                        </Text>
                    </Pressable>
                </View>  

            </Pressable>
        </View>  
        <PopUp 
          modalType={modalState.modalType} 
          conversationName={modalState.conversationName}
          isVisible={modalState.isVisible}
          index = {-1}
          onClose={(emitData: EmitData) => handleClose(emitData)}
          />
    </Modal>
  );
}