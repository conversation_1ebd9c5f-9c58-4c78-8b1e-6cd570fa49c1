# This file contains the fastlane.tools configuration
# You can find the documentation at https://docs.fastlane.tools
#
# For a list of all available actions, check out
#
#     https://docs.fastlane.tools/actions
#
# For a list of all available plugins, check out
#
#     https://docs.fastlane.tools/plugins/available-plugins
#

# Uncomment the line if you want fast-lane to automatically update itself
# update_fastlane

default_platform(:ios)

platform :ios do
  desc "Create IPA"
  lane:createipa do
	
	app_store_connect_api_key(
    key_id: "V5W953735F",
    issuer_id: "69a6de7c-e762-47e3-e053-5b8c7c11a4d1",
    key_content: "LS0tLS1CRUdJTiBQUklWQVRFIEtFWS0tLS0tCk1JR1RBZ0VBTUJNR0J5cUdTTTQ5QWdFR0NDcUdTTTQ5QXdFSEJIa3dkd0lCQVFRZ2FRT2UvYnNVbWx0dVVSVmsKVXhzZUdoTW1GeW5ZZFFvRGFzRGt2WW9Ya0VpZ0NnWUlLb1pJemowREFRZWhSQU5DQUFTMVQ1NW14MGhjKzBOdgpwUlhxNTVOK25TZTVhUEdUWnVSKzJaZHNzVXpVUVY1OGg3Z0hsYXZoTDBjcUxwWXZ6Rk1BaHNoWGtpYkw3S3FNCitBQjFyQ0dZCi0tLS0tRU5EIFBSSVZBVEUgS0VZLS0tLS0=",
    is_key_content_base64: true,
    in_house: false #boolean value if team is Enterprise or App Store
  )
	api_key = lane_context[SharedValues::APP_STORE_CONNECT_API_KEY]
	increment_build_number({build_number:ENV['BAMBOO_BUILD_NUMBER']})	
  gym

end

lane:uploadtestflight do
ENV['ITMSTRANSPORTER_FORCE_ITMS_PACKAGE_UPLOAD'] = 'false'

app_store_connect_api_key(
    # key_id:ENV['ASCAPI_KEY_ID'],
    # issuer_id: ENV['ASCAPI_ISSUER_ID'],
    # key_content: ENV['ASCAPI_KEY_CONTENT'],
    # is_key_content_base64: true,
    # duration:1200,
    # in_house: false #boolean value if team is Enterprise or App Store
    key_id: "V5W953735F",
    issuer_id: "69a6de7c-e762-47e3-e053-5b8c7c11a4d1",
    key_content: "LS0tLS1CRUdJTiBQUklWQVRFIEtFWS0tLS0tCk1JR1RBZ0VBTUJNR0J5cUdTTTQ5QWdFR0NDcUdTTTQ5QXdFSEJIa3dkd0lCQVFRZ2FRT2UvYnNVbWx0dVVSVmsKVXhzZUdoTW1GeW5ZZFFvRGFzRGt2WW9Ya0VpZ0NnWUlLb1pJemowREFRZWhSQU5DQUFTMVQ1NW14MGhjKzBOdgpwUlhxNTVOK25TZTVhUEdUWnVSKzJaZHNzVXpVUVY1OGg3Z0hsYXZoTDBjcUxwWXZ6Rk1BaHNoWGtpYkw3S3FNCitBQjFyQ0dZCi0tLS0tRU5EIFBSSVZBVEUgS0VZLS0tLS0=",
    is_key_content_base64: true,
    duration:1200,
    in_house: false #boolean value if team is Enterprise or App Store
  )

	apikey = lane_context[SharedValues::APP_STORE_CONNECT_API_KEY]
	increment_build_number({build_number:ENV['BAMBOO_BUILD_NUMBER']})	
    # increment_build_number(
    #   build_number: ENV['BAMBOO_BUILD_NUMBER'].to_i
    # )''
  
    puts "ENV Build number: #{ENV['BAMBOO_BUILD_NUMBER']}"
    puts "apiKey: #{apikey}"


  gym

	pilot(api_key:apikey,skip_waiting_for_build_processing:true)
 end
    
end