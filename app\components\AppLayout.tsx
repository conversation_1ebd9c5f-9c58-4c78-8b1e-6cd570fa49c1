import React, { useEffect, useCallback } from "react";
import { View, Modal, Pressable, Text } from "react-native";
import { useRouter } from "expo-router";
import { Sidebar } from "../modals/sidebar";
import { AppHeader } from "./AppHeader";

import { useAppState } from "../helpers/appStateContext";
import { useAuth } from "../helpers/authContext";
import { styles, topStyles } from "../helpers/stylesheet";
import { EmitData, EmitType, SitecoreData, UserData } from "../helpers/model";
import {
  deleteConversation,
  postRate,
  postRename,
  postMessage,
} from "../helpers/service";
import LogoutIcon from "@/assets/icons/LogoutIcon";

interface AppLayoutProps {
  children: React.ReactNode;
  showNewConversation?: boolean;
  formatBotMessage?: Function;
}

export function AppLayout({
  children,
  showNewConversation = false,
  formatBotMessage,
}: AppLayoutProps) {
  const router = useRouter();
  const { authData, setAuthData } = useAuth();
  const {
    isVisibleSidebar,
    toggleSidebar,
    isProfileActive,
    setProfileActive,
    isDisabled,

    setEmitChange,
    userData,
    setUserData,
    setLoading,
  } = useAppState();

  // Helper functions for user data management
  const getActiveConversation = useCallback(() => {
    return userData.conversations.find((conversation) => conversation.isActive);
  }, [userData]);

  const isNewConversation = useCallback(() => {
    const activeConversation = getActiveConversation();
    return activeConversation?.title === "New Conversation";
  }, [getActiveConversation]);

  const handleStackOpen = useCallback(
    (source: { title: string; source: string }) => {
      const sourceId = `${Date.now()}_${Math.random()
        .toString(36)
        .substring(2, 11)}`;
      router.push({
        pathname: "/paas-source/[id]",
        params: {
          id: sourceId,
          title: source.title,
          url: source.source,
        },
      });
    },
    [router]
  );

  const closeAnyOpenStacks = useCallback(() => {
    try {
      const currentPath = window?.location?.pathname || "";
      if (currentPath.includes("paas-source")) {
        if (router.canGoBack()) {
          router.back();
        } else {
          router.replace("/chatbox");
        }
      } else {
        console.log("Not on PAAS source screen, no action needed");
      }
    } catch (error) {
      console.log("Error checking path, using fallback:", error);
      if (router.canGoBack()) {
        router.back();
      }
    }
  }, [router]);

  // User data management functions
  const startNewConversation = useCallback(() => {
    setUserData((currentUserData) => ({
      ...currentUserData,
      conversations: currentUserData.conversations.map((conversation) =>
        conversation.title === "New Conversation"
          ? { ...conversation, isActive: true }
          : { ...conversation, isActive: false }
      ),
    }));
  }, [setUserData]);

  const changeTab = useCallback(
    (emitData: EmitData) => {
      setUserData((currentUserData) => ({
        ...currentUserData,
        conversations: currentUserData.conversations.map((conversation) => {
          if (
            emitData.conversationId.toLowerCase() === "new conversation" &&
            conversation.title === "New Conversation"
          ) {
            return { ...conversation, isActive: true };
          } else if (conversation.conversationId === emitData.conversationId) {
            return { ...conversation, isActive: true };
          }
          return { ...conversation, isActive: false };
        }),
      }));
    },
    [setUserData]
  );

  const deleteTab = useCallback(
    async (emitData: EmitData) => {
      const activeConversation = getActiveConversation();
      if (activeConversation?.conversationId) {
        await deleteConversation(
          authData.userId,
          authData.accessToken,
          activeConversation.conversationId
        );

        setUserData((currentUserData) => ({
          ...currentUserData,
          conversations: currentUserData.conversations.filter(
            (conversation) => !conversation.isActive
          ),
        }));
        startNewConversation();
      }
    },
    [getActiveConversation, authData, setUserData, startNewConversation]
  );

  const renameTab = useCallback(
    async (emitData: EmitData) => {
      const activeConversation = getActiveConversation();
      if (activeConversation?.conversationId) {
        await postRename(
          authData.userId,
          authData.accessToken,
          activeConversation.conversationId,
          emitData.name
        );

        setUserData((currentUserData) => ({
          ...currentUserData,
          conversations: currentUserData.conversations.map((conversation) =>
            conversation.isActive
              ? { ...conversation, title: emitData.name }
              : conversation
          ),
        }));
      }
    },
    [getActiveConversation, authData, setUserData]
  );

  const rateMessage = useCallback(
    async (emitData: EmitData) => {
      const activeConversation = getActiveConversation();
      const rating = emitData.emitType === EmitType.rateUp ? 1 : -1;

      if (activeConversation?.conversationId) {
        await postRate(
          authData.userId,
          authData.accessToken,
          activeConversation?.conversationId,
          emitData.index,
          rating,
          ""
        );

        const messageIndex = emitData.index;
        if (
          messageIndex >= 0 &&
          messageIndex < activeConversation.messageHistory.length
        ) {
          setUserData((currentUserData) => ({
            ...currentUserData,
            conversations: currentUserData.conversations.map((conversation) => {
              if (conversation.isActive) {
                const updatedConversation = { ...conversation };
                updatedConversation.messageHistory = [
                  ...conversation.messageHistory,
                ];
                updatedConversation.messageHistory[messageIndex] = {
                  ...updatedConversation.messageHistory[messageIndex],
                  rating: rating,
                };
                return updatedConversation;
              }
              return conversation;
            }),
          }));
        }
      }
    },
    [getActiveConversation, authData, setUserData]
  );

  const sendMessage = useCallback(
    async (emitData: EmitData, formatBotMessageFn: Function) => {
      setLoading(true);
      setUserData((currentUserData) => {
        if (isNewConversation()) {
          return {
            ...currentUserData,
            conversations: [
              ...currentUserData.conversations.map((conversation) => ({
                ...conversation,
                isActive: false,
              })),
              {
                title: emitData.message,
                isActive: true,
                messageHistory: [
                  {
                    userMsg: emitData.message,
                    botMsg: "",
                    time: new Date(),
                    rating: 0,
                    metadata: [],
                  },
                ],
                conversationId: "",
                isDisabled: false,
                date: new Date(),
              },
            ],
          };
        } else {
          return {
            ...currentUserData,
            conversations: currentUserData.conversations.map((conversation) => {
              if (conversation.isActive) {
                return {
                  ...conversation,
                  messageHistory: [
                    ...conversation.messageHistory,
                    {
                      userMsg: emitData.message,
                      botMsg: "",
                      time: new Date(),
                      rating: 0,
                      metadata: [],
                    },
                  ],
                  date: new Date(),
                };
              }
              return conversation;
            }),
          };
        }
      });

      const activeConversation = getActiveConversation();

      const res = await postMessage(
        authData.userId,
        authData.accessToken,
        emitData.message,
        activeConversation ? activeConversation.conversationId : ""
      );

      const aiResponse = res.answer;
      const metadata = res.metadata || [];

      setUserData((currentUserData) => ({
        ...currentUserData,
        conversations: currentUserData.conversations.map((conversation) => {
          if (conversation.isActive) {
            return {
              ...conversation,
              conversationId: res.conversationId,
              messageHistory: conversation.messageHistory.map(
                (message, index) =>
                  index === conversation.messageHistory.length - 1
                    ? {
                        ...message,
                        botMsg: formatBotMessageFn(
                          metadata,
                          aiResponse,
                          false,
                          handleStackOpen
                        ),
                        metadata: metadata,
                      }
                    : message
              ),
            };
          }
          return conversation;
        }),
      }));

      setLoading(false);
    },
    [
      setUserData,
      setLoading,
      isNewConversation,
      getActiveConversation,
      authData,
      handleStackOpen,
    ]
  );

  const handleLogout = async () => {
    setProfileActive(false);
    const newAuthData = {
      accessToken: "",
      userId: "",
      isAuth: false,
      username: "",
      hasAgreed: false,
      sitecore: new SitecoreData(),
    };
    setAuthData(newAuthData);
    router.replace("/");
  };
  const handleSidebarPress = () => {
    if (isVisibleSidebar) {
      return;
    }
    toggleSidebar(true);
  };
  const handleProfilePress = () => {
    setProfileActive(true);
  };

  const handleUserDataChange = async (emitData: EmitData) => {
    if (!emitData) {
      console.error("handleUserDataChange called with undefined emitData");
      return;
    }

    if (emitData.isConfirmed !== false) {
      toggleSidebar(false);
    }

    if (emitData.isConfirmed === false) return;

    switch (emitData.emitType) {
      case EmitType.changeTab:
        closeAnyOpenStacks();
        changeTab(emitData);
        break;

      case EmitType.delete:
        await deleteTab(emitData);
        break;

      case EmitType.rename:
        await renameTab(emitData);
        break;

      case EmitType.message:
        if (formatBotMessage) {
          await sendMessage(emitData, formatBotMessage);
        }
        break;

      case EmitType.rateUp:
      case EmitType.rateDown:
        await rateMessage(emitData);
        break;

      case EmitType.default:
        break;

      default:
        console.warn("Unknown emitType:", emitData.emitType);
        break;
    }
  };

  const handleNewConversation = () => {
    startNewConversation();
  };

  useEffect(() => {
    setEmitChange(handleUserDataChange);
  }, [setEmitChange]);

  return (
    <View style={styles.scrollview}>
      <Modal animationType="none" transparent={true} visible={isProfileActive}>
        <Pressable
          testID="user-icon-backdrop"
          style={topStyles.profileBackdrop}
          onPress={() => setProfileActive(false)}
        ></Pressable>
        <View style={topStyles.userProfileContainer}>
          <View>
            <Pressable
              testID="logout-button"
              style={topStyles.userProfileRow}
              onPress={handleLogout}
            >
              <LogoutIcon />
              <Text style={topStyles.userProfile}>Logout</Text>
            </Pressable>
          </View>
        </View>
      </Modal>

      <Sidebar
        userData={userData}
        isVisible={isVisibleSidebar}
        emitChange={handleUserDataChange}
        onBackdropPress={() => toggleSidebar(false)}
      />

      <View style={styles.container}>
        <AppHeader
          onSidebarPress={handleSidebarPress}
          onNewConversationPress={handleNewConversation}
          onProfilePress={handleProfilePress}
          isDisabled={isDisabled}
          showNewConversation={showNewConversation}
        />

        <View style={{ flex: 1 }}>{children}</View>
      </View>
    </View>
  );
}
