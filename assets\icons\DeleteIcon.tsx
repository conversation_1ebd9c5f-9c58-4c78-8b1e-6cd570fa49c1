import React from 'react';
import { View, StyleSheet } from 'react-native';
import Svg, { <PERSON>lip<PERSON><PERSON>, Defs, G, Mask, Path } from 'react-native-svg';

const styles = StyleSheet.create({
    icon: {
        height: 30,
        width: 30,
        padding: 6
    },  
});

export default function DeleteIcon(props: any) { 
    const getIcon = (props: any) => {
        return (
            <Svg xmlns="http://www.w3.org/2000/svg" fill="none" {...props}>
            <Mask
              id="a"
              width={16}
              height={17}
              x={0}
              y={0}
              maskUnits="userSpaceOnUse"
              style={{
                maskType: "alpha",
              }}
            >
              <Path fill="#D9D9D9" d="M0 .5h16v16H0z" />
            </Mask>
            <G mask="url(#a)">
              <Path
                fill="#00358E"
                d="M4.667 14.5c-.367 0-.68-.13-.942-.392a1.284 1.284 0 0 1-.392-.941V4.5h-.666V3.167H6V2.5h4v.667h3.333V4.5h-.666v8.667c0 .366-.13.68-.392.941a1.284 1.284 0 0 1-.942.392H4.667ZM6 11.833h1.333v-6H6v6Zm2.667 0H10v-6H8.667v6Z"
              />
            </G>
          </Svg>
        );
    };

    return (
    <View style={styles.icon}>
        { getIcon(null) }
    </View>
    );
};