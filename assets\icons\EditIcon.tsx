import React from 'react';
import { View, StyleSheet } from 'react-native';
import Svg, { <PERSON>lipP<PERSON>, Defs, G, Path } from 'react-native-svg';

const styles = StyleSheet.create({
    icon: {
        height: 30,
        width: 30,
        padding: 6
    },  
});

export default function EditIcon(props: any) { 
    const getIcon = (props: any) => {
        return (
            <Svg xmlns="http://www.w3.org/2000/svg" fill="none" {...props}>
            <Path
              fill="#00358E"
              d="M2 12v2.5h2.5l7.373-7.373-2.5-2.5L1.999 12Zm11.806-6.806c.26-.26.26-.68 0-.94l-1.56-1.56a.664.664 0 0 0-.94 0l-1.22 1.22 2.5 2.5 1.22-1.22Z"
            />
          </Svg>
        );
    };

    return (
    <View style={styles.icon}>
        { getIcon(null) }
    </View>
    );
};