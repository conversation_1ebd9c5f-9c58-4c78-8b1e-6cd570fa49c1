import { useRouter } from "expo-router";

export interface BreadcrumbItem {
  title: string;
  onPress?: () => void;
  isActive?: boolean;
}

/**
 * Generate breadcrumb items for PAAS source navigation
 */
export function generatePaasBreadcrumbs(
  sourceTitle: string,
  router: ReturnType<typeof useRouter>,
  sourceUrl?: string
): BreadcrumbItem[] {
  const breadcrumbs: BreadcrumbItem[] = [];

  breadcrumbs.push({
    title: "PAAS AI",
    onPress: () => router.back(),
    isActive: false,
  });

  // Determine document type from URL if available, otherwise from title
  let documentType = "Document";
  if (sourceUrl) {
    const sourceInfo = parsePaasSourceInfo(sourceUrl);
    documentType = getDocumentTypeFromSourceInfo(sourceInfo);
  } else {
    documentType = getDocumentTypeFromTitle(sourceTitle);
  }

  breadcrumbs.push({
    title: documentType,
    isActive: true,
  });

  return breadcrumbs;
}

/**
 * Get document type from parsed source info (more reliable than parsing title)
 */
function getDocumentTypeFromSourceInfo(
  sourceInfo: ReturnType<typeof parsePaasSourceInfo>
): string {
  switch (sourceInfo.type) {
    case "training-manual":
      return "Training manual";
    case "industry-guide":
      return "Industry guide";
    case "search":
      return "Search results";
    case "class-guide":
      return "Class guide";
    default:
      return "Document";
  }
}

/**
 * Extract document type from source title for breadcrumb display (fallback method)
 */
function getDocumentTypeFromTitle(title: string): string {
  // Check for common PAAS document types
  if (
    title.toLowerCase().includes("class guide") ||
    title.toLowerCase().includes("wc class guide")
  ) {
    return "Class guide";
  }

  if (title.toLowerCase().includes("training manual")) {
    return "Training manual";
  }

  if (title.toLowerCase().includes("industry guide")) {
    return "Industry guide";
  }

  if (title.toLowerCase().includes("search result")) {
    return "Search results";
  }

  if (title.toLowerCase().includes("bulletin")) {
    return "Bulletin";
  }

  if (title.toLowerCase().includes("circular")) {
    return "Circular";
  }

  return "Document";
}

/**
 * Parse PAAS source URL to extract document information
 */
export function parsePaasSourceInfo(sourceUrl: string): {
  type: string;
  id?: string;
  chapterId?: string;
  contentType?: string;
} {
  try {
    const urlObj = new URL(sourceUrl, "https://example.com");

    if (sourceUrl.includes("/PAAS/training-manual")) {
      return {
        type: "training-manual",
        id: urlObj.searchParams.get("id") || undefined,
        chapterId: urlObj.searchParams.get("chapterid") || undefined,
      };
    }

    if (sourceUrl.includes("/PAAS/industryguide")) {
      return {
        type: "industry-guide",
        id: urlObj.searchParams.get("id") || undefined,
        chapterId: urlObj.searchParams.get("chapterid") || undefined,
      };
    }

    if (sourceUrl.includes("/paas/search/")) {
      return {
        type: "search",
        contentType: urlObj.searchParams.get("contentType") || undefined,
        id: urlObj.searchParams.get("id") || undefined,
      };
    }

    if (sourceUrl.includes("class") && sourceUrl.includes("guide")) {
      return {
        type: "class-guide",
        id: urlObj.searchParams.get("id") || undefined,
      };
    }

    return { type: "unknown" };
  } catch (error) {
    console.warn("Error parsing PAAS source URL:", error);
    return { type: "unknown" };
  }
}

/**
 * Generate document title based on source info
 */
export function generateDocumentTitle(
  originalTitle: string,
  sourceInfo: ReturnType<typeof parsePaasSourceInfo>
): string {
  if (originalTitle && originalTitle !== "PAAS Source Document") {
    return originalTitle;
  }

  switch (sourceInfo.type) {
    case "training-manual":
      return `Training Manual${sourceInfo.id ? ` - ${sourceInfo.id}` : ""}`;

    case "industry-guide":
      return `Industry Guide${sourceInfo.id ? ` - ${sourceInfo.id}` : ""}`;

    case "search":
      return `Search Results${
        sourceInfo.contentType ? ` - ${sourceInfo.contentType}` : ""
      }`;

    case "class-guide":
      return `WC Class Guide${sourceInfo.id ? ` - ${sourceInfo.id}` : ""}`;

    default:
      return originalTitle || "PAAS Document";
  }
}
