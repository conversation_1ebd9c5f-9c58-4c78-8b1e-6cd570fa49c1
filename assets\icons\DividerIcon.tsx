import React from 'react';
import { View, StyleSheet } from 'react-native';
import Svg, { <PERSON>lip<PERSON><PERSON>, Defs, G, Path } from 'react-native-svg';

const styles = StyleSheet.create({
    icon: {
        height: 30,
        width: 30
    },  
});

export default function DividerIcon(props: any) { 
    const getIcon = (props: any) => {
        return (
            <Svg xmlns="http://www.w3.org/2000/svg" fill="none" {...props}>
            <Path stroke="#A3A3A3" d="M1 0v20" />
          </Svg>
        );
    };

    return (
    <View style={styles.icon}>
        { getIcon(null) }
    </View>
    );
};