import React, { JSX } from "react";

export class AuthData {
  userId: string = "";
  accessToken: string = "";
  isAuth: boolean = false;
  username: string = "";
  hasAgreed: boolean = false;
  sitecore: SitecoreData = new SitecoreData();

  constructor(userId: string = "", accessToken: string = "", isAuth: boolean = false, 
    username: string = "") {
    this.userId = userId;
    this.accessToken = accessToken;
    this.isAuth = isAuth;
    this.username = username;
  }
}

export class SitecoreData {
  NewConversationText: scDatum = { value: '' };
  GreetingMessageText: scDatum = { value: '' };
  InputPlaceholderText: scDatum = { value: '' };
  ConversationStartersText: scDatum = { value: '' };
  BotNameText: scDatum = { value: '' };
  HowToUseText: scDatum = { value: '' };
  TermsOfUseText: scDatum = { value: '' };

  constructor() {
    this.NewConversationText = { value: '' };
    this.GreetingMessageText = { value: '' };
    this.InputPlaceholderText = { value: '' };
    this.ConversationStartersText = { value: '' };
    this.BotNameText = { value: '' };
    this.HowToUseText = { value: '' };
    this.TermsOfUseText = { value: '' };
  }
}

export class scDatum {
  value: string = '';
}

export class UserData {
  userId: string = "";
  conversations: Conversation[] = [];

  constructor() {
    this.userId = "";
    this.conversations = [
      {
        isActive: true,
        isDisabled: false,
        conversationId: "",
        title: "New Conversation",
        date: new Date(),
        messageHistory: [
          {
            botMsg: "",
            userMsg: "",
            time: new Date(),
            rating: 0,
            metadata: []
          }
        ]
      }
    ];
  }
}

export interface Conversation {
  isActive: boolean;
  isDisabled: boolean;
  conversationId: string;
  title: string;
  date: Date;
  messageHistory: Message[];
}

export interface Message {
    userMsg: string | JSX.Element;
    botMsg: string | JSX.Element | React.ReactNode;
    time: Date;
    rating: number;
    metadata: {
      source: Source[];
      title: string;
      docType: string;
      itemId: string;
      updateDate: Date;
    } [] | Source[];
  }

  export type Source = {
  section: string;
  docType: string;
  itemId: string;
  jurisdiction: string;
  lob: string;
  source: string;
  title: string;
  updateDate: string;
}

export interface ApiUserData {
  conversationId: string;
  name: string;
  time: string;
  disabled: boolean;

  content: {
    userMsg: string;
    summary: string;
    botMsg: string;
    source: Source[];
    time: string;
    rating: number;
    comment: string;
  }[];
}

export interface ChatResponse {
  answer: string;
  conversationId: string;
  isDisabled: boolean;
  metadata: {
    source: Source[];
    title: string;
    docType: string;
    itemId: string;
    updateDate: Date;
  } [];
}

export enum DateCategory {
  today = "Today",
  lastSevenDays = "Last 7 Days",
  lastThirtyDays = "Last 30 Days",
  older = "Older"
}

export enum EmitType {
  rename = "rename",
  delete = "delete",
  changeTab = "changeTab",
  rateUp = "rateUp",
  rateDown = "rateDown",
  message = "message",
  default = "default"
}

export class CopyButtonData {
  isActive: boolean = false;
  index: number = -1;

  constructor(isActive: boolean = false, index: number = -1) {
    this.isActive = isActive;
    this.index = index;
  }
}

export class EmitData {
  name: string = "";
  feedback: string = "";
  index: number = -1;
  isConfirmed: boolean = false;
  emitType: EmitType = EmitType.default;
  message: string = "";
  conversationId: string = "New Conversation"

  constructor(name: string = '', feedback: string = '', index: number = -1,
    isConfirmed: boolean = false, emitType: EmitType = EmitType.default, 
    message:string = '', conversationId: string = 'New Conversation') {
      this.name = name;
      this.feedback = feedback;
      this.index = index;
      this.isConfirmed = isConfirmed;
      this.emitType = emitType;
      this.message = message;
      this.conversationId = conversationId;
  }
}

export enum ModalType {
  Default = "Default",
  Disclaimer = "Disclaimer",
  HowToUse = "HowToUse",
  Rename = "Rename",
  Delete = "Delete",
  Feedback = "Feedback",
  TermsOfAgreement = "TermsOfAgreement",
}

export class ModalState {
  modalType: ModalType;
  isVisible: boolean;
  conversationName: string;

  constructor(modalType: ModalType = ModalType.Default, isVisible: boolean = false, conversationName: string = "") {
    this.modalType = modalType;
    this.isVisible = isVisible;
    this.conversationName = conversationName;
  }
}

export class ModalData {
  title: string;
  content: string;
  modalType: ModalType;
  requiresConfirmation: boolean = false;
  submitText: string;

  constructor(modalType: ModalType = ModalType.Default) {
    this.modalType = modalType;

    if (this.modalType === ModalType.Disclaimer) {
      this.title = "Disclaimers";
      this.content = '';
      this.requiresConfirmation = false;
      this.submitText = '';

    } else if (this.modalType === ModalType.TermsOfAgreement){
      this.title = "Terms of Agreement";
      this.content = '';
      this.requiresConfirmation = true;
      this.submitText = 'Agree';

    } else if (this.modalType === ModalType.HowToUse){
      this.title = "How to Use";
      this.content = '';
      this.requiresConfirmation = false;
      this.submitText = '';

    } else if (this.modalType === ModalType.Rename){
      this.title = "Rename Conversation";
      this.content = "";
      this.requiresConfirmation = true;
      this.submitText = "Rename";

    } else if (this.modalType === ModalType.Delete){
      this.title = "Delete Conversation";
      this.content = '';
      this.requiresConfirmation = true;
      this.submitText = "Delete";

    } else if (this.modalType === ModalType.Feedback){
      this.title = "Message Feedback";
      this.content = "Why did you choose this rating? (optional)";
      this.requiresConfirmation = true;
      this.submitText = "Submit";

    } else {
      this.title = "";
      this.content = "";
      this.requiresConfirmation = false;
      this.submitText = '';
    }
  }
}

export default {
  ModalData,
  ModalState,
  ModalType,
  EmitData,
  EmitType,
  DateCategory,
  UserData,
  AuthData,
  CopyButtonData
};