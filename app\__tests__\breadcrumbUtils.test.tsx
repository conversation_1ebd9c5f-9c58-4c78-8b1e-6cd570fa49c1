import { 
  generatePaasBreadcrumbs, 
  parsePaasSourceInfo, 
  generateDocumentTitle 
} from '../helpers/breadcrumbUtils';

// Mock expo-router
const mockRouter = {
  push: jest.fn(),
  back: jest.fn(),
  replace: jest.fn(),
};

describe('Breadcrumb Utilities', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('generatePaasBreadcrumbs', () => {
    it('should generate correct breadcrumbs for class guide', () => {
      const sourceTitle = 'WC Class Guide: Musical Instrument Mfg.';
      const breadcrumbs = generatePaasBreadcrumbs(sourceTitle, mockRouter as any);

      expect(breadcrumbs).toHaveLength(2);
      expect(breadcrumbs[0]).toEqual({
        title: 'PAAS AI',
        onPress: expect.any(Function),
        isActive: false,
      });
      expect(breadcrumbs[1]).toEqual({
        title: 'Class guide',
        isActive: true,
      });
    });

    it('should generate correct breadcrumbs for training manual', () => {
      const sourceTitle = 'Training Manual: Workers Compensation';
      const breadcrumbs = generatePaasBreadcrumbs(sourceTitle, mockRouter as any);

      expect(breadcrumbs[1].title).toBe('Training manual');
    });

    it('should generate correct breadcrumbs for industry guide', () => {
      const sourceTitle = 'Industry Guide: Construction';
      const breadcrumbs = generatePaasBreadcrumbs(sourceTitle, mockRouter as any);

      expect(breadcrumbs[1].title).toBe('Industry guide');
    });

    it('should call router.back when PAAS AI breadcrumb is clicked', () => {
      const sourceTitle = 'Test Document';
      const breadcrumbs = generatePaasBreadcrumbs(sourceTitle, mockRouter as any);

      breadcrumbs[0].onPress?.();
      expect(mockRouter.back).toHaveBeenCalledTimes(1);
    });
  });

  describe('parsePaasSourceInfo', () => {
    it('should parse training manual URLs correctly', () => {
      const url = '/PAAS/training-manual?id=123&chapterid=456';
      const result = parsePaasSourceInfo(url);

      expect(result).toEqual({
        type: 'training-manual',
        id: '123',
        chapterId: '456',
      });
    });

    it('should parse industry guide URLs correctly', () => {
      const url = '/PAAS/industryguide?id=789&chapterid=101';
      const result = parsePaasSourceInfo(url);

      expect(result).toEqual({
        type: 'industry-guide',
        id: '789',
        chapterId: '101',
      });
    });

    it('should parse search URLs correctly', () => {
      const url = '/paas/search/?contentType=document&id=555';
      const result = parsePaasSourceInfo(url);

      expect(result).toEqual({
        type: 'search',
        contentType: 'document',
        id: '555',
      });
    });

    it('should parse class guide URLs correctly', () => {
      const url = '/paas/class-guide?id=2923';
      const result = parsePaasSourceInfo(url);

      expect(result).toEqual({
        type: 'class-guide',
        id: '2923',
      });
    });

    it('should handle unknown URLs gracefully', () => {
      const url = '/unknown/path';
      const result = parsePaasSourceInfo(url);

      expect(result).toEqual({
        type: 'unknown',
      });
    });

    it('should handle invalid URLs gracefully', () => {
      const url = 'not-a-valid-url';
      const result = parsePaasSourceInfo(url);

      expect(result).toEqual({
        type: 'unknown',
      });
    });
  });

  describe('generateDocumentTitle', () => {
    it('should use original title when meaningful', () => {
      const originalTitle = 'WC Class Guide: Musical Instrument Mfg. - Wood- NOC, code 2923A';
      const sourceInfo = { type: 'class-guide', id: '2923' };
      
      const result = generateDocumentTitle(originalTitle, sourceInfo);
      expect(result).toBe(originalTitle);
    });

    it('should generate title for training manual when original is generic', () => {
      const originalTitle = 'PAAS Source Document';
      const sourceInfo = { type: 'training-manual', id: '123' };
      
      const result = generateDocumentTitle(originalTitle, sourceInfo);
      expect(result).toBe('Training Manual - 123');
    });

    it('should generate title for industry guide', () => {
      const originalTitle = '';
      const sourceInfo = { type: 'industry-guide', id: '456' };
      
      const result = generateDocumentTitle(originalTitle, sourceInfo);
      expect(result).toBe('Industry Guide - 456');
    });

    it('should generate title for search results', () => {
      const originalTitle = '';
      const sourceInfo = { type: 'search', contentType: 'document' };
      
      const result = generateDocumentTitle(originalTitle, sourceInfo);
      expect(result).toBe('Search Results - document');
    });

    it('should generate title for class guide', () => {
      const originalTitle = '';
      const sourceInfo = { type: 'class-guide', id: '2923' };
      
      const result = generateDocumentTitle(originalTitle, sourceInfo);
      expect(result).toBe('WC Class Guide - 2923');
    });

    it('should handle unknown types gracefully', () => {
      const originalTitle = '';
      const sourceInfo = { type: 'unknown' };
      
      const result = generateDocumentTitle(originalTitle, sourceInfo);
      expect(result).toBe('PAAS Document');
    });

    it('should return original title for unknown types when provided', () => {
      const originalTitle = 'Custom Document Title';
      const sourceInfo = { type: 'unknown' };
      
      const result = generateDocumentTitle(originalTitle, sourceInfo);
      expect(result).toBe('Custom Document Title');
    });
  });
});
