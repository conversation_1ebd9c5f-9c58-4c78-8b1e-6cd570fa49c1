import React from 'react';
import { View, StyleSheet } from 'react-native';
import Svg, { Path } from 'react-native-svg';

const styles = StyleSheet.create({
    icon: {
        height: 40,
        width: 40,
        padding: 5,
    },  
});

export default function Close() { 
    const getIcon = (props: any) => {
        return (
            <Svg xmlns="http://www.w3.org/2000/svg" fill="none" {...props}>
            <Path
              fill="#FFC600"
              d="M6.667 28a2.568 2.568 0 0 1-1.884-.783A2.568 2.568 0 0 1 4 25.333V6.667c0-.734.261-1.361.783-1.884A2.568 2.568 0 0 1 6.667 4h11.9L15.9 6.667H6.667v18.666h18.666v-9.266L28 13.4v11.933c0 .734-.261 1.362-.783 1.884a2.568 2.568 0 0 1-1.884.783H6.667ZM12 20v-5.667L24.233 2.1c.267-.267.567-.467.9-.6a2.633 2.633 0 0 1 2.9.6L29.9 4c.244.267.433.561.567.883.133.323.2.65.2.984 0 .333-.061.66-.184.983a2.497 2.497 0 0 1-.583.883L17.667 20H12Zm2.667-2.667h1.866L24.267 9.6l-.934-.933-.966-.934-7.7 7.7v1.9Z"
            />
          </Svg>
        );
    };

    return (
    <View style={styles.icon}>
        { getIcon(null) }
    </View>
    );
};