import React, { useEffect, useState } from 'react';
import { StyleSheet, View, Pressable,} from 'react-native';
import Microphone from '@/assets/icons/MicrophoneIcon';

export default function VoiceRecognition({
  isDisabled,
  emitListening,
  emitResult
}: { 
  isDisabled: boolean,
  emitListening: (isListening: boolean) => void
  emitResult: (data: string) => void
}) {
  const [isListening, setListening] = useState(false);
  const [transcript, setTranscript] = useState('microphone test');

  
  useEffect(() => {
    emitListening(isListening);
  }, [isListening]);

  const requestMicrophonePermission = async () => {
    // const { status } = await Permissions
    //   .askAsync(Permissions.AUDIO_RECORDING);
    // if (status !== 'granted') {
    //   alert('Permission denied by user');
    //   return false;
    // }
    // else return true;
    return true;
  };


  const startListening = async () => {
    const hasPermission = await requestMicrophonePermission();
    if (!hasPermission) return;

    try {
      setListening(true);
      setTimeout(() => {
        stopListening();
      }, 3000);
      
    } catch (e) {
      console.error('start listening: ' + e);
    }
  };

  const stopListening = async () => {
    try {
      setListening(false);
      setTranscript('(microphone test)');
      emitResult(transcript);
    } catch (e) {
      console.error('stop listening: ' + e);
    }
  };
  
  return (
    <View>
      <Pressable 
        onPress={startListening} 
        disabled={isDisabled || isListening}>
          <Microphone 
            isActive={isListening} />
      </Pressable>
    </View>
  );
}

const styles = StyleSheet.create({
 
});