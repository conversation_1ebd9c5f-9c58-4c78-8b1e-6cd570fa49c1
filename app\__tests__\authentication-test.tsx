import { act, fireEvent, render, waitFor } from '@testing-library/react-native';
import Authentication from '@/app/components/authentication';
import { AuthData } from '../helpers/model';

jest.mock('expo-linking', () => {
  const module: typeof import('expo-linking') = {
      ...jest.requireActual('expo-linking'),
      createURL: jest.fn(),
  };

  return module;
});

jest.mock('../helpers/authContext', () => ({
  useAuth: jest.fn(() => ({
    setAuthData: jest.fn((data: AuthData) => {
      data.username = 'default_user';
      data.accessToken = 'default_access_token';
      data.isAuth = false;
      data.userId = 'default_user_id';
    }),
    getAuthData: jest.fn(),
    getUserInfo: jest.fn(),
    getAccessToken: jest.fn(),
    getIdToken: jest.fn(),
    getUser: jest.fn(),
    getUserSessionTokenExpiration: jest.fn(),
    getUserSessionTokenExpirationDate: jest.fn(),
    getUserSessionTokenExpirationTime: jest.fn(),
    isAuthenticated: false,
  })),
}));

jest.mock('expo-auth-session', () => ({
  useAuthRequest: jest.fn().mockReturnValue([
      { request: { clientId: 'mockClientId' } },
      { response: { accessToken: 'mockAccessToken', type: 'success' } },
      jest.fn().mockResolvedValue({ type: 'mockType' })
  ]),
  makeRedirectUri: jest.fn().mockReturnValue('mockRedirectUri'),
}));

describe('<Authentication />', () => {
  test('Text renders correctly on Authentication', () => {
    const { getByText } = render(<Authentication />);

    getByText('Login to PAAS');
  });

    it ('creates a new Auth object', async () => {
      const { getByTestId } = render(<Authentication />);
      await waitFor(() => {
        expect(getByTestId('authentication')).toBeVisible();
    });

      act(() => { fireEvent.press(getByTestId('login-button')); });
      await waitFor(() => {
          expect(getByTestId('login-button')).toBeVisible();
      });
    });

    const auth = new AuthData("test", "test", false, "test");

});

