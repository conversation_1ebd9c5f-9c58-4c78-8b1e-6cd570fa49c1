import { act, cleanup, fireEvent, render, screen, waitFor } from '@testing-library/react-native';
import { AuthData, EmitData, EmitType } from '../helpers/model';
import { useAuth } from '../helpers/authContext';
import { Options } from '../modals/options';
import { Sidebar } from '../modals/sidebar';

const mockedConversations = [
    {
      conversationId: 'conv1',
      name: 'conversation-name1',
      time: '1111-11-11:11-11-11',
      disabled: false,
      content: [
        {
          userMsg: 'default user message',
          summary: 'default_message',
          botMsg: 'default botmessage',
          source: [],
          time: '1111-11-11:11-11-11',
          rating: 0,
          comment: 'default_comment',
        },
      ],
    },
    {
      conversationId: 'conv2',
      name: 'conversation-name2',
      time: '1111-11-11:11-11-12',
      disabled: false,
      content: [
        {
          userMsg: 'default user message',
          summary: 'default_message',
          botMsg: 'default botmessage',
          source: [],
          time: '1111-11-11:11-11-12',
          rating: 0,
          comment: 'default_comment',
        },
      ],
    },
  ];
  
  const mockedPostMessageResponse = {
    answer: 'Mocked API response',
    conversationId: 'conv3',
    isDisabled: false,
    metadata: [
      {
        source: 'default_source',
        title: 'default_title',
        docType: 'default_docType',
        itemId: 'default_itemId',
        updateDate: new Date('1111-11-11'),
      },
    ],
  };

jest.mock('../helpers/service', () => ({
  getUserConversations: jest.fn(() => Promise.resolve(mockedConversations)),
  postMessage: jest.fn(() => Promise.resolve(mockedPostMessageResponse)),
  postRate: jest.fn(() => Promise.resolve({})),
}));

jest.mock('expo-linking', () => {
    const module: typeof import('expo-linking') = {
        ...jest.requireActual('expo-linking'),
        createURL: jest.fn(),
    };
  
    return module;
  });

jest.mock('../helpers/authContext', () => ({
  useAuth: jest.fn(() => ({
    setAuthData: jest.fn((data: AuthData) => {
      data.username = 'test user';
      data.accessToken = 'default_access_token';
      data.isAuth = true;
      data.userId = 'userId';
    }),
    getAuthData: jest.fn((data: AuthData) => {
        data.username = 'test user';
        data.accessToken = 'default_access_token';
        data.isAuth = true;
        data.userId = 'userId';
      }),
    getUserInfo: jest.fn(),
    getAccessToken: jest.fn(),
    getIdToken: jest.fn(),
    getUser: jest.fn(),
    getUserSessionTokenExpiration: jest.fn(),
    getUserSessionTokenExpirationDate: jest.fn(),
    getUserSessionTokenExpirationTime: jest.fn(),
    isAuthenticated: true
  }))
}));

describe('Options Popup', () => {
    beforeEach(() => {
      (useAuth as jest.Mock).mockReturnValue({
        authData: { isAuth: true, userId: 'userId', accessToken: 'testToken', username: 'test user' },
        setAuthData: jest.fn(),
      });
    });
  
    afterEach(() => {
      jest.clearAllMocks();
      cleanup();
    });
  
    it ('handles rename conversation', async () => {
      const { getByTestId } = render(<Sidebar 
        userData={{
          userId: 'i29246',
          conversations: [
            {
                 isActive: true,
                 isDisabled: false,
                 conversationId: 'conversation1',
                 title: 'What GL code should I use for a',
                 date: new Date(),
                 messageHistory: [
                    {
                        userMsg: 'What GL code should I use for a gas station that has a car wash?',
                        botMsg: 'A gas station may be assigned to codes 13453, 13454, or 13455.  The car wash operation is separately classified to either code 10368 or code 10367, depending on if the customer is physically washing their own vehicle or not, repectively.  All other store operations or vehicle repair or service work are also separately classified and rated.',
                        time: new Date(),
                        rating: 0,
                        metadata: []
                      },
              ],
            }]}}
        isVisible={true} 
        emitChange={function (emitData: EmitData): void {
            jest.fn();
      } }
      />);
  
        // Verify ellipses to open options is open
        await waitFor(() => {
            expect(getByTestId('ellipses')).toBeVisible();
        });
    
        // Click to open options menu & verify options menu is open
        act(() => { fireEvent.press(getByTestId('conv-options-button')); });
        await waitFor(() => {
            expect(getByTestId('options-popup')).toBeVisible();
        });
    
        // Click Rename option & verify rename popup is open
        act(() => { fireEvent.press(getByTestId('rename-option')); });
        await waitFor(() => {
            expect(getByTestId('rename-popup')).toBeVisible();
        });
    
        // cancel via inner backdrop & verify popup is closed
        act(() => { fireEvent.press(getByTestId('options-inner-backdrop')); });
        await waitFor(() => {
            expect(screen.queryByTestId('rename-popup')).not.toBeVisible();
        });

        //reopen
        act(() => { fireEvent.press(getByTestId('conv-options-button')); });
        await waitFor(() => {
            expect(getByTestId('options-popup')).toBeVisible();
        });
        act(() => { fireEvent.press(getByTestId('rename-option')); });
        await waitFor(() => {
            expect(getByTestId('rename-popup')).toBeVisible();
        });

        // cancel via outer backdrop & verify popup is closed
        act(() => { fireEvent.press(getByTestId('options-outer-backdrop')); });
        await waitFor(() => {
            expect(screen.queryByTestId('rename-popup')).not.toBeVisible();
        });

        //reopen
        act(() => { fireEvent.press(getByTestId('conv-options-button')); });
        await waitFor(() => {
            expect(getByTestId('options-popup')).toBeVisible();
        });
        act(() => { fireEvent.press(getByTestId('rename-option')); });
        await waitFor(() => {
            expect(getByTestId('rename-popup')).toBeVisible();
        });

        // Disallow empty name
        let feedback = getByTestId('rename-input');
        fireEvent.changeText(feedback, '      ');
        await waitFor(() => {
          expect(feedback.props.value).toBe('      ');
        });
        await waitFor(() => {
          expect(screen.getByTestId('confirm-button')).toBeDisabled();
          });

      // Enter text input for new name
       feedback = getByTestId('rename-input');
      fireEvent.changeText(feedback, 'Renamed Name');
      await waitFor(() => {
        expect(feedback.props.value).toBe('Renamed Name');
      });
      act(() => { fireEvent.press(getByTestId('confirm-button')); });
      await waitFor(() => {
        expect(screen.queryByTestId('rename-popup')).not.toBeVisible();
        });
    });

    it ('handles delete conversation', async () => {
        const { getByTestId } = render(<Sidebar 
          userData={{
            userId: 'i29246',
            conversations: [
              {
                   isActive: true,
                   isDisabled: false,
                   conversationId: 'conversationToday',
                   title: 'What GL code should I use for a gas station that has a car wash?',
                   date: new Date(),
                   messageHistory: [
                      {
                          userMsg: 'What GL code should I use for a gas station that has a car wash?',
                          botMsg: 'A gas station may be assigned to codes 13453, 13454, or 13455.  The car wash operation is separately classified to either code 10368 or code 10367, depending on if the customer is physically washing their own vehicle or not, repectively.  All other store operations or vehicle repair or service work are also separately classified and rated.',
                          time: new Date(),
                          rating: 0,
                          metadata: []
                        },
                ],
              }]}}
          isVisible={true} 
          emitChange={function (emitData: EmitData): void {
          jest.fn();
        } }/>);
    
            // Verify ellipses to open options is open
            await waitFor(() => {
                expect(getByTestId('ellipses')).toBeVisible();
            });
        
            // Click to open options menu & verify options menu is open
            act(() => { fireEvent.press(getByTestId('conv-options-button')); });
            await waitFor(() => {
                expect(getByTestId('options-popup')).toBeVisible();
            });
        
            // Click Rename option & verify rename popup is open
            act(() => { fireEvent.press(getByTestId('delete-option')); });
            await waitFor(() => {
                expect(getByTestId('misc-popup')).toBeVisible();
            });
        
            // cancel via Cancel Button & verify popup is closed
            act(() => { fireEvent.press(getByTestId('cancel-button')); });
            await waitFor(() => {
                expect(screen.queryByTestId('misc-popup')).not.toBeVisible();
            });

            //reopen
            act(() => { fireEvent.press(getByTestId('conv-options-button')); });
            await waitFor(() => {
                expect(getByTestId('options-popup')).toBeVisible();
            });
            act(() => { fireEvent.press(getByTestId('delete-option')); });
            await waitFor(() => {
                expect(getByTestId('misc-popup')).toBeVisible();
            });
    
          // Confirm delete
          act(() => { fireEvent.press(getByTestId('confirm-button')); });
          await waitFor(() => {
            expect(screen.queryByTestId('misc-popup')).not.toBeVisible();
            });
        });
      });
