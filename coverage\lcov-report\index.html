
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for All files</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="prettify.css" />
    <link rel="stylesheet" href="base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1>All files</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">87.84% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>484/551</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">74.02% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>208/281</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">84.32% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>156/185</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">88.38% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>472/534</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line high'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file medium" data-value="app"><a href="app/index.html">app</a></td>
	<td data-value="77.59" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 77%"></div><div class="cover-empty" style="width: 23%"></div></div>
	</td>
	<td data-value="77.59" class="pct medium">77.59%</td>
	<td data-value="241" class="abs medium">187/241</td>
	<td data-value="59.37" class="pct medium">59.37%</td>
	<td data-value="128" class="abs medium">76/128</td>
	<td data-value="76.47" class="pct medium">76.47%</td>
	<td data-value="68" class="abs medium">52/68</td>
	<td data-value="77.96" class="pct medium">77.96%</td>
	<td data-value="236" class="abs medium">184/236</td>
	</tr>

<tr>
	<td class="file high" data-value="app/helpers"><a href="app/helpers/index.html">app/helpers</a></td>
	<td data-value="100" class="pic high">
	<div class="chart"><div class="cover-fill cover-full" style="width: 100%"></div><div class="cover-empty" style="width: 0%"></div></div>
	</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="63" class="abs high">63/63</td>
	<td data-value="84.61" class="pct high">84.61%</td>
	<td data-value="26" class="abs high">22/26</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="6" class="abs high">6/6</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="63" class="abs high">63/63</td>
	</tr>

<tr>
	<td class="file high" data-value="app/modals"><a href="app/modals/index.html">app/modals</a></td>
	<td data-value="91.92" class="pic high">
	<div class="chart"><div class="cover-fill" style="width: 91%"></div><div class="cover-empty" style="width: 9%"></div></div>
	</td>
	<td data-value="91.92" class="pct high">91.92%</td>
	<td data-value="161" class="abs high">148/161</td>
	<td data-value="84.68" class="pct high">84.68%</td>
	<td data-value="111" class="abs high">94/111</td>
	<td data-value="81.42" class="pct high">81.42%</td>
	<td data-value="70" class="abs high">57/70</td>
	<td data-value="93.28" class="pct high">93.28%</td>
	<td data-value="149" class="abs high">139/149</td>
	</tr>

<tr>
	<td class="file high" data-value="assets/icons"><a href="assets/icons/index.html">assets/icons</a></td>
	<td data-value="100" class="pic high">
	<div class="chart"><div class="cover-fill cover-full" style="width: 100%"></div><div class="cover-empty" style="width: 0%"></div></div>
	</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="86" class="abs high">86/86</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="16" class="abs high">16/16</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="41" class="abs high">41/41</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="86" class="abs high">86/86</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-04-01T01:07:34.081Z
            </div>
        <script src="prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="sorter.js"></script>
        <script src="block-navigation.js"></script>
    </body>
</html>
    