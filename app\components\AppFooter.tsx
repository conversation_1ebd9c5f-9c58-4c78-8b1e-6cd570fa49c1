import React from "react";
import { View, Text, ScrollView } from "react-native";
import { bottomStyles, styles } from "../helpers/stylesheet";
import VeriskWhiteLogo from "@/assets/icons/VeriskWhiteLogo";

const bottomCopyrightText =
  "Copyright © 2024 Insurance Services Office, Inc, All rights reserved.";
const bottomDisclaimerText =
  "The information displayed here is illustrative and for display " +
  "purposes only, and should not be relied on. It may contain a compilation of certain " +
  "examples of actual data as well as fictitious information.";

export function AppFooter() {
  return (
    <>
      {/* Footer  */}
      <View style={styles.footer}>
        <ScrollView keyboardShouldPersistTaps="never">
          <View style={bottomStyles.bottomLogoContainer}>
            <VeriskWhiteLogo />
          </View>
          <Text style={bottomStyles.bottomCopyrightText}>
            {bottomCopyrightText}
          </Text>
          <Text style={bottomStyles.bottomLinkText}>Verisk Corporate Home</Text>
          <Text style={bottomStyles.bottomLinkText}>Terms and Conditions</Text>
          <Text style={bottomStyles.bottomLinkText}>
            Privacy and Security Policy
          </Text>
          <Text style={bottomStyles.bottomLinkText}>Contact Us</Text>
          <Text style={bottomStyles.bottomDisclaimerText}>
            {bottomDisclaimerText}
          </Text>
        </ScrollView>
      </View>
      <View style={styles.bottomToolbar} />
    </>
  );
}
