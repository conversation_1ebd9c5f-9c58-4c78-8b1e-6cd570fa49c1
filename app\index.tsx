import React, { useEffect, useState } from "react";
import { View, StyleSheet } from "react-native";
import { Chatbox } from "./components/chatbox";
import { useAuth } from "./helpers/authContext";
import Authentication from "./components/authentication";
import { SitecoreData } from "./helpers/model";

export default function Index() {
  const { authData, setAuthData } = useAuth();
  const [displayApp, setDisplayApp] = useState(true);
  const hardCode = false;

  // Once user is authenticated, fetch sitecore data & check agreement history
  useEffect(() => {
    if (hardCode) {
      const newAuthData = {
        userId: "I29246",
        accessToken: process.env.EXPO_PUBLIC_ACCESS_TOKEN || "",
        isAuth: true,
        username: "<PERSON>",
        hasAgreed: false,
        sitecore: new SitecoreData(),
      };
      setAuthData(newAuthData);
    }
  }, [hardCode]);

  return (
    <View style={styles.container}>
      {authData.isAuth === true || hardCode ? <Chatbox /> : <Authentication />}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#F5FCFF",
  },
});
