
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for app</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../prettify.css" />
    <link rel="stylesheet" href="../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../index.html">All files</a> app</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">77.59% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>187/241</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">59.37% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>76/128</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">76.47% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>52/68</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">77.96% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>184/236</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line medium'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file medium" data-value="authentication.tsx"><a href="authentication.tsx.html">authentication.tsx</a></td>
	<td data-value="64" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 64%"></div><div class="cover-empty" style="width: 36%"></div></div>
	</td>
	<td data-value="64" class="pct medium">64%</td>
	<td data-value="25" class="abs medium">16/25</td>
	<td data-value="41.66" class="pct low">41.66%</td>
	<td data-value="12" class="abs low">5/12</td>
	<td data-value="75" class="pct medium">75%</td>
	<td data-value="4" class="abs medium">3/4</td>
	<td data-value="64" class="pct medium">64%</td>
	<td data-value="25" class="abs medium">16/25</td>
	</tr>

<tr>
	<td class="file medium" data-value="chatbox.tsx"><a href="chatbox.tsx.html">chatbox.tsx</a></td>
	<td data-value="76.59" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 76%"></div><div class="cover-empty" style="width: 24%"></div></div>
	</td>
	<td data-value="76.59" class="pct medium">76.59%</td>
	<td data-value="141" class="abs medium">108/141</td>
	<td data-value="54.83" class="pct medium">54.83%</td>
	<td data-value="62" class="abs medium">34/62</td>
	<td data-value="80" class="pct high">80%</td>
	<td data-value="40" class="abs high">32/40</td>
	<td data-value="76.64" class="pct medium">76.64%</td>
	<td data-value="137" class="abs medium">105/137</td>
	</tr>

<tr>
	<td class="file high" data-value="messages.tsx"><a href="messages.tsx.html">messages.tsx</a></td>
	<td data-value="81.81" class="pic high">
	<div class="chart"><div class="cover-fill" style="width: 81%"></div><div class="cover-empty" style="width: 19%"></div></div>
	</td>
	<td data-value="81.81" class="pct high">81.81%</td>
	<td data-value="66" class="abs high">54/66</td>
	<td data-value="68.51" class="pct medium">68.51%</td>
	<td data-value="54" class="abs medium">37/54</td>
	<td data-value="70.83" class="pct medium">70.83%</td>
	<td data-value="24" class="abs medium">17/24</td>
	<td data-value="83.07" class="pct high">83.07%</td>
	<td data-value="65" class="abs high">54/65</td>
	</tr>

<tr>
	<td class="file high" data-value="stylesheet.tsx"><a href="stylesheet.tsx.html">stylesheet.tsx</a></td>
	<td data-value="100" class="pic high">
	<div class="chart"><div class="cover-fill cover-full" style="width: 100%"></div><div class="cover-empty" style="width: 0%"></div></div>
	</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="9" class="abs high">9/9</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="9" class="abs high">9/9</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-04-01T01:07:34.081Z
            </div>
        <script src="../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../sorter.js"></script>
        <script src="../block-navigation.js"></script>
    </body>
</html>
    