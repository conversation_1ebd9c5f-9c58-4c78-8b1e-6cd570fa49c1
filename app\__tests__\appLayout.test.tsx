import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import { View, Text, Pressable } from 'react-native';
import { AppLayout } from '../components/AppLayout';
import { AppStateProvider, useAppState } from '../helpers/appStateContext';
import { AuthProvider } from '../helpers/authContext';

// Mock expo-router
jest.mock('expo-router', () => ({
  useRouter: () => ({
    push: jest.fn(),
    back: jest.fn(),
    replace: jest.fn(),
  }),
}));

// Mock the sidebar component
jest.mock('../modals/sidebar', () => {
  const { View, Text } = require('react-native');
  return {
    Sidebar: ({ isVisible }: { isVisible: boolean }) =>
      isVisible ? <View testID="sidebar"><Text>Sidebar</Text></View> : null,
  };
});

// Test component to interact with AppState
function TestAppStateComponent() {
  const {
    isVisibleSidebar,
    toggleSidebar,
    isProfileActive,
    setProfileActive,
    isDisabled,
    setDisabled
  } = useAppState();

  return (
    <View>
      <Pressable testID="toggle-sidebar" onPress={() => toggleSidebar()}>
        <Text>Toggle Sidebar</Text>
      </Pressable>
      <Pressable testID="show-profile" onPress={() => setProfileActive(true)}>
        <Text>Show Profile</Text>
      </Pressable>
      <Pressable testID="hide-profile" onPress={() => setProfileActive(false)}>
        <Text>Hide Profile</Text>
      </Pressable>
      <Pressable testID="set-disabled" onPress={() => setDisabled(true)}>
        <Text>Set Disabled</Text>
      </Pressable>
      <Text testID="sidebar-visible">{isVisibleSidebar.toString()}</Text>
      <Text testID="profile-active">{isProfileActive.toString()}</Text>
      <Text testID="is-disabled">{isDisabled.toString()}</Text>
    </View>
  );
}

describe('AppStateContext', () => {
  const renderWithProviders = (component: React.ReactElement) => {
    return render(
      <AuthProvider>
        <AppStateProvider>
          {component}
        </AppStateProvider>
      </AuthProvider>
    );
  };

  it('should initialize with correct default values', () => {
    const { getByTestId } = renderWithProviders(<TestAppStateComponent />);

    expect(getByTestId('sidebar-visible')).toHaveTextContent('false');
    expect(getByTestId('profile-active')).toHaveTextContent('false');
    expect(getByTestId('is-disabled')).toHaveTextContent('true');
  });

  it('should toggle sidebar visibility', () => {
    const { getByTestId } = renderWithProviders(<TestAppStateComponent />);

    expect(getByTestId('sidebar-visible')).toHaveTextContent('false');

    fireEvent.press(getByTestId('toggle-sidebar'));
    expect(getByTestId('sidebar-visible')).toHaveTextContent('true');

    fireEvent.press(getByTestId('toggle-sidebar'));
    expect(getByTestId('sidebar-visible')).toHaveTextContent('false');
  });

  it('should manage profile modal state', () => {
    const { getByTestId } = renderWithProviders(<TestAppStateComponent />);

    expect(getByTestId('profile-active')).toHaveTextContent('false');

    fireEvent.press(getByTestId('show-profile'));
    expect(getByTestId('profile-active')).toHaveTextContent('true');

    fireEvent.press(getByTestId('hide-profile'));
    expect(getByTestId('profile-active')).toHaveTextContent('false');
  });

  it('should manage disabled state', () => {
    const { getByTestId } = renderWithProviders(<TestAppStateComponent />);

    expect(getByTestId('is-disabled')).toHaveTextContent('true');

    fireEvent.press(getByTestId('set-disabled'));
    expect(getByTestId('is-disabled')).toHaveTextContent('true');
  });
});

describe('AppLayout', () => {
  const renderWithProviders = (component: React.ReactElement) => {
    return render(
      <AuthProvider>
        <AppStateProvider>
          {component}
        </AppStateProvider>
      </AuthProvider>
    );
  };

  it('should render children correctly', () => {
    const { getByTestId } = renderWithProviders(
      <AppLayout>
        <View testID="test-content"><Text>Test Content</Text></View>
      </AppLayout>
    );

    expect(getByTestId('test-content')).toBeTruthy();
  });

  it('should show new conversation button when enabled', () => {
    const { getByTestId } = renderWithProviders(
      <AppLayout
        showNewConversation={true}
      >
        <View><Text>Content</Text></View>
      </AppLayout>
    );

    const newConvButton = getByTestId('new-conv-button');
    expect(newConvButton).toBeTruthy();

    fireEvent.press(newConvButton);
    // The new conversation functionality is now handled internally
  });

  it('should hide new conversation button when disabled', () => {
    const { queryByTestId } = renderWithProviders(
      <AppLayout showNewConversation={false}>
        <View><Text>Content</Text></View>
      </AppLayout>
    );

    expect(queryByTestId('new-conv-button')).toBeNull();
  });

  it('should handle sidebar toggle', () => {
    const { getByTestId } = renderWithProviders(
      <AppLayout>
        <View><Text>Content</Text></View>
      </AppLayout>
    );

    const sidebarButton = getByTestId('sidebar-button');
    expect(sidebarButton).toBeTruthy();

    fireEvent.press(sidebarButton);
    // Sidebar should become visible
    expect(getByTestId('sidebar')).toBeTruthy();
  });

  it('should handle profile modal', () => {
    const { getByTestId } = renderWithProviders(
      <AppLayout>
        <View><Text>Content</Text></View>
      </AppLayout>
    );

    const profileButton = getByTestId('icon-button');
    expect(profileButton).toBeTruthy();

    fireEvent.press(profileButton);
    // Profile modal should become visible
    // This would be tested by checking if the modal is rendered
  });

  it('should handle logout correctly', () => {
    const { getByTestId } = renderWithProviders(
      <AppLayout>
        <View><Text>Content</Text></View>
      </AppLayout>
    );

    // First open the profile modal
    fireEvent.press(getByTestId('icon-button'));

    // Then click logout
    const logoutButton = getByTestId('logout-button');
    fireEvent.press(logoutButton);

    // This should trigger the logout process
    // The actual logout behavior would be tested in integration tests
  });
});
