import React, {
  createContext,
  useContext,
  useState,
  ReactNode,
  useCallback,
} from "react";
import { UserData, EmitData } from "./model";

interface AppStateContextType {
  // Sidebar state
  isVisibleSidebar: boolean;
  toggleSidebar: (visible?: boolean) => void;
  userData: UserData;
  setUserData: (userData: UserData | ((prev: UserData) => UserData)) => void;

  // Profile modal state
  isProfileActive: boolean;
  setProfileActive: (active: boolean) => void;

  // Loading and disabled states
  isLoading: boolean;
  setLoading: (loading: boolean) => void;
  isDisabled: boolean;
  setDisabled: (disabled: boolean) => void;

  // User interaction handler
  emitChange?: (emitData: EmitData) => void;
  setEmitChange: (handler: (emitData: EmitData) => void) => void;
}

const AppStateContext = createContext<AppStateContextType | undefined>(
  undefined
);

interface AppStateProviderProps {
  children: ReactNode;
}

export function AppStateProvider({ children }: AppStateProviderProps) {
  const [isVisibleSidebar, setIsVisibleSidebar] = useState(false);
  const [userData, setUserDataState] = useState(new UserData());
  const [isProfileActive, setProfileActive] = useState(false);
  const [isLoading, setLoading] = useState(false);
  const [isDisabled, setDisabled] = useState(true);
  const [emitChange, setEmitChange] = useState<
    ((emitData: EmitData) => void) | undefined
  >(undefined);

  // Wrapper function to handle both direct values and functional updates
  const setUserData = useCallback(
    (userData: UserData | ((prev: UserData) => UserData)) => {
      if (typeof userData === "function") {
        setUserDataState(userData);
      } else {
        setUserDataState(userData);
      }
    },
    []
  );

  const toggleSidebar = useCallback(
    (visible?: boolean) => {
      setIsVisibleSidebar(visible !== undefined ? visible : !isVisibleSidebar);
    },
    [isVisibleSidebar]
  );

  const contextValue: AppStateContextType = {
    isVisibleSidebar,
    toggleSidebar,
    userData,
    setUserData,
    isProfileActive,
    setProfileActive,
    isLoading,
    setLoading,
    isDisabled,
    setDisabled,
    emitChange,
    setEmitChange,
  };

  return (
    <AppStateContext.Provider value={contextValue}>
      {children}
    </AppStateContext.Provider>
  );
}

export function useAppState(): AppStateContextType {
  const context = useContext(AppStateContext);
  if (context === undefined) {
    throw new Error("useAppState must be used within an AppStateProvider");
  }
  return context;
}
