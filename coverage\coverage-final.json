{"/Users/<USER>/Documents/PAASApplications/premium.audit.advisory.service.mobile/app/authentication.tsx": {"path": "/Users/<USER>/Documents/PAASApplications/premium.audit.advisory.service.mobile/app/authentication.tsx", "statementMap": {"0": {"start": {"line": 8, "column": 0}, "end": {"line": 8, "column": 38}}, "1": {"start": {"line": 9, "column": 21}, "end": {"line": 9, "column": 65}}, "2": {"start": {"line": 10, "column": 20}, "end": {"line": 10, "column": 65}}, "3": {"start": {"line": 11, "column": 30}, "end": {"line": 11, "column": 85}}, "4": {"start": {"line": 12, "column": 22}, "end": {"line": 12, "column": 78}}, "5": {"start": {"line": 13, "column": 25}, "end": {"line": 13, "column": 85}}, "6": {"start": {"line": 14, "column": 20}, "end": {"line": 16, "column": 2}}, "7": {"start": {"line": 22, "column": 28}, "end": {"line": 22, "column": 99}}, "8": {"start": {"line": 26, "column": 26}, "end": {"line": 26, "column": 35}}, "9": {"start": {"line": 27, "column": 43}, "end": {"line": 37, "column": 3}}, "10": {"start": {"line": 39, "column": 2}, "end": {"line": 45, "column": 17}}, "11": {"start": {"line": 41, "column": 4}, "end": {"line": 44, "column": 5}}, "12": {"start": {"line": 42, "column": 21}, "end": {"line": 42, "column": 62}}, "13": {"start": {"line": 43, "column": 6}, "end": {"line": 43, "column": 26}}, "14": {"start": {"line": 47, "column": 22}, "end": {"line": 85, "column": 3}}, "15": {"start": {"line": 49, "column": 36}, "end": {"line": 59, "column": 5}}, "16": {"start": {"line": 61, "column": 4}, "end": {"line": 84, "column": 5}}, "17": {"start": {"line": 62, "column": 22}, "end": {"line": 69, "column": 8}}, "18": {"start": {"line": 72, "column": 23}, "end": {"line": 72, "column": 43}}, "19": {"start": {"line": 73, "column": 23}, "end": {"line": 73, "column": 53}}, "20": {"start": {"line": 76, "column": 26}, "end": {"line": 81, "column": 7}}, "21": {"start": {"line": 83, "column": 6}, "end": {"line": 83, "column": 31}}, "22": {"start": {"line": 88, "column": 2}, "end": {"line": 120, "column": 4}}, "23": {"start": {"line": 109, "column": 14}, "end": {"line": 109, "column": 28}}, "24": {"start": {"line": 123, "column": 20}, "end": {"line": 212, "column": 2}}}, "fnMap": {"0": {"name": "Authentication", "decl": {"start": {"line": 25, "column": 24}, "end": {"line": 25, "column": 38}}, "loc": {"start": {"line": 25, "column": 41}, "end": {"line": 121, "column": 1}}, "line": 25}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 39, "column": 12}, "end": {"line": 39, "column": 13}}, "loc": {"start": {"line": 39, "column": 18}, "end": {"line": 45, "column": 3}}, "line": 39}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 47, "column": 22}, "end": {"line": 47, "column": 23}}, "loc": {"start": {"line": 47, "column": 64}, "end": {"line": 85, "column": 3}}, "line": 47}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 108, "column": 21}, "end": {"line": 108, "column": 22}}, "loc": {"start": {"line": 108, "column": 27}, "end": {"line": 110, "column": 13}}, "line": 108}}, "branchMap": {"0": {"loc": {"start": {"line": 9, "column": 21}, "end": {"line": 9, "column": 65}}, "type": "binary-expr", "locations": [{"start": {"line": 9, "column": 21}, "end": {"line": 9, "column": 59}}, {"start": {"line": 9, "column": 63}, "end": {"line": 9, "column": 65}}], "line": 9}, "1": {"loc": {"start": {"line": 10, "column": 20}, "end": {"line": 10, "column": 65}}, "type": "binary-expr", "locations": [{"start": {"line": 10, "column": 20}, "end": {"line": 10, "column": 59}}, {"start": {"line": 10, "column": 63}, "end": {"line": 10, "column": 65}}], "line": 10}, "2": {"loc": {"start": {"line": 41, "column": 4}, "end": {"line": 44, "column": 5}}, "type": "if", "locations": [{"start": {"line": 41, "column": 4}, "end": {"line": 44, "column": 5}}, {"start": {}, "end": {}}], "line": 41}, "3": {"loc": {"start": {"line": 55, "column": 25}, "end": {"line": 55, "column": 52}}, "type": "binary-expr", "locations": [{"start": {"line": 55, "column": 25}, "end": {"line": 55, "column": 46}}, {"start": {"line": 55, "column": 50}, "end": {"line": 55, "column": 52}}], "line": 55}, "4": {"loc": {"start": {"line": 61, "column": 4}, "end": {"line": 84, "column": 5}}, "type": "if", "locations": [{"start": {"line": 61, "column": 4}, "end": {"line": 84, "column": 5}}, {"start": {}, "end": {}}], "line": 61}, "5": {"loc": {"start": {"line": 78, "column": 16}, "end": {"line": 78, "column": 74}}, "type": "cond-expr", "locations": [{"start": {"line": 78, "column": 41}, "end": {"line": 78, "column": 63}}, {"start": {"line": 78, "column": 66}, "end": {"line": 78, "column": 74}}], "line": 78}}, "s": {"0": 1, "1": 1, "2": 1, "3": 1, "4": 1, "5": 1, "6": 1, "7": 1, "8": 3, "9": 3, "10": 3, "11": 3, "12": 0, "13": 0, "14": 3, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 3, "23": 1, "24": 1}, "f": {"0": 3, "1": 3, "2": 0, "3": 1}, "b": {"0": [1, 1], "1": [1, 1], "2": [0, 3], "3": [0, 0], "4": [0, 0], "5": [0, 0]}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "1b8ab6ecda8792b34c4e7e989e3fb16e63630ad3"}, "/Users/<USER>/Documents/PAASApplications/premium.audit.advisory.service.mobile/app/chatbox.tsx": {"path": "/Users/<USER>/Documents/PAASApplications/premium.audit.advisory.service.mobile/app/chatbox.tsx", "statementMap": {"0": {"start": {"line": 17, "column": 28}, "end": {"line": 17, "column": 99}}, "1": {"start": {"line": 18, "column": 29}, "end": {"line": 20, "column": 62}}, "2": {"start": {"line": 23, "column": 36}, "end": {"line": 23, "column": 45}}, "3": {"start": {"line": 24, "column": 34}, "end": {"line": 24, "column": 58}}, "4": {"start": {"line": 25, "column": 44}, "end": {"line": 25, "column": 59}}, "5": {"start": {"line": 26, "column": 34}, "end": {"line": 26, "column": 49}}, "6": {"start": {"line": 27, "column": 36}, "end": {"line": 27, "column": 50}}, "7": {"start": {"line": 28, "column": 46}, "end": {"line": 28, "column": 61}}, "8": {"start": {"line": 29, "column": 23}, "end": {"line": 32, "column": 47}}, "9": {"start": {"line": 33, "column": 32}, "end": {"line": 33, "column": 52}}, "10": {"start": {"line": 34, "column": 19}, "end": {"line": 34, "column": 24}}, "11": {"start": {"line": 36, "column": 2}, "end": {"line": 51, "column": 17}}, "12": {"start": {"line": 37, "column": 4}, "end": {"line": 42, "column": 5}}, "13": {"start": {"line": 38, "column": 6}, "end": {"line": 38, "column": 72}}, "14": {"start": {"line": 39, "column": 6}, "end": {"line": 39, "column": 29}}, "15": {"start": {"line": 40, "column": 6}, "end": {"line": 40, "column": 33}}, "16": {"start": {"line": 41, "column": 6}, "end": {"line": 41, "column": 39}}, "17": {"start": {"line": 44, "column": 4}, "end": {"line": 50, "column": 5}}, "18": {"start": {"line": 45, "column": 6}, "end": {"line": 45, "column": 25}}, "19": {"start": {"line": 47, "column": 8}, "end": {"line": 47, "column": 29}}, "20": {"start": {"line": 49, "column": 6}, "end": {"line": 49, "column": 13}}, "21": {"start": {"line": 54, "column": 20}, "end": {"line": 54, "column": 56}}, "22": {"start": {"line": 55, "column": 24}, "end": {"line": 56, "column": 61}}, "23": {"start": {"line": 56, "column": 28}, "end": {"line": 56, "column": 60}}, "24": {"start": {"line": 58, "column": 4}, "end": {"line": 67, "column": 6}}, "25": {"start": {"line": 62, "column": 10}, "end": {"line": 64, "column": 17}}, "26": {"start": {"line": 72, "column": 4}, "end": {"line": 72, "column": 85}}, "27": {"start": {"line": 73, "column": 4}, "end": {"line": 73, "column": 26}}, "28": {"start": {"line": 76, "column": 16}, "end": {"line": 79, "column": 5}}, "29": {"start": {"line": 83, "column": 36}, "end": {"line": 83, "column": 57}}, "30": {"start": {"line": 84, "column": 4}, "end": {"line": 111, "column": 3}}, "31": {"start": {"line": 87, "column": 4}, "end": {"line": 104, "column": 7}}, "32": {"start": {"line": 89, "column": 28}, "end": {"line": 89, "column": 127}}, "33": {"start": {"line": 89, "column": 79}, "end": {"line": 89, "column": 126}}, "34": {"start": {"line": 90, "column": 6}, "end": {"line": 103, "column": 9}}, "35": {"start": {"line": 96, "column": 68}, "end": {"line": 102, "column": 9}}, "36": {"start": {"line": 107, "column": 4}, "end": {"line": 108, "column": 40}}, "37": {"start": {"line": 109, "column": 4}, "end": {"line": 109, "column": 33}}, "38": {"start": {"line": 115, "column": 30}, "end": {"line": 123, "column": 3}}, "39": {"start": {"line": 116, "column": 4}, "end": {"line": 122, "column": 8}}, "40": {"start": {"line": 116, "column": 33}, "end": {"line": 122, "column": 5}}, "41": {"start": {"line": 119, "column": 6}, "end": {"line": 121, "column": 22}}, "42": {"start": {"line": 126, "column": 32}, "end": {"line": 128, "column": 3}}, "43": {"start": {"line": 127, "column": 4}, "end": {"line": 127, "column": 78}}, "44": {"start": {"line": 127, "column": 55}, "end": {"line": 127, "column": 76}}, "45": {"start": {"line": 131, "column": 28}, "end": {"line": 136, "column": 3}}, "46": {"start": {"line": 132, "column": 31}, "end": {"line": 132, "column": 54}}, "47": {"start": {"line": 133, "column": 4}, "end": {"line": 135, "column": 12}}, "48": {"start": {"line": 139, "column": 32}, "end": {"line": 145, "column": 3}}, "49": {"start": {"line": 140, "column": 17}, "end": {"line": 140, "column": 31}}, "50": {"start": {"line": 141, "column": 4}, "end": {"line": 141, "column": 28}}, "51": {"start": {"line": 142, "column": 4}, "end": {"line": 142, "column": 39}}, "52": {"start": {"line": 143, "column": 4}, "end": {"line": 143, "column": 45}}, "53": {"start": {"line": 144, "column": 4}, "end": {"line": 144, "column": 31}}, "54": {"start": {"line": 148, "column": 31}, "end": {"line": 359, "column": 3}}, "55": {"start": {"line": 149, "column": 22}, "end": {"line": 162, "column": 5}}, "56": {"start": {"line": 150, "column": 30}, "end": {"line": 150, "column": 45}}, "57": {"start": {"line": 151, "column": 6}, "end": {"line": 160, "column": 9}}, "58": {"start": {"line": 153, "column": 8}, "end": {"line": 158, "column": 9}}, "59": {"start": {"line": 155, "column": 10}, "end": {"line": 155, "column": 53}}, "60": {"start": {"line": 156, "column": 15}, "end": {"line": 158, "column": 9}}, "61": {"start": {"line": 157, "column": 10}, "end": {"line": 157, "column": 53}}, "62": {"start": {"line": 159, "column": 8}, "end": {"line": 159, "column": 52}}, "63": {"start": {"line": 161, "column": 6}, "end": {"line": 161, "column": 35}}, "64": {"start": {"line": 164, "column": 22}, "end": {"line": 182, "column": 5}}, "65": {"start": {"line": 165, "column": 33}, "end": {"line": 165, "column": 56}}, "66": {"start": {"line": 168, "column": 6}, "end": {"line": 181, "column": 7}}, "67": {"start": {"line": 169, "column": 20}, "end": {"line": 173, "column": 9}}, "68": {"start": {"line": 176, "column": 32}, "end": {"line": 176, "column": 47}}, "69": {"start": {"line": 177, "column": 8}, "end": {"line": 178, "column": 58}}, "70": {"start": {"line": 178, "column": 34}, "end": {"line": 178, "column": 56}}, "71": {"start": {"line": 179, "column": 8}, "end": {"line": 179, "column": 37}}, "72": {"start": {"line": 180, "column": 8}, "end": {"line": 180, "column": 30}}, "73": {"start": {"line": 184, "column": 22}, "end": {"line": 208, "column": 5}}, "74": {"start": {"line": 186, "column": 33}, "end": {"line": 186, "column": 56}}, "75": {"start": {"line": 189, "column": 6}, "end": {"line": 207, "column": 7}}, "76": {"start": {"line": 190, "column": 20}, "end": {"line": 195, "column": 9}}, "77": {"start": {"line": 198, "column": 32}, "end": {"line": 205, "column": 7}}, "78": {"start": {"line": 201, "column": 12}, "end": {"line": 203, "column": 28}}, "79": {"start": {"line": 206, "column": 6}, "end": {"line": 206, "column": 35}}, "80": {"start": {"line": 210, "column": 24}, "end": {"line": 240, "column": 5}}, "81": {"start": {"line": 211, "column": 33}, "end": {"line": 211, "column": 56}}, "82": {"start": {"line": 212, "column": 21}, "end": {"line": 212, "column": 67}}, "83": {"start": {"line": 214, "column": 6}, "end": {"line": 238, "column": 7}}, "84": {"start": {"line": 216, "column": 20}, "end": {"line": 223, "column": 9}}, "85": {"start": {"line": 226, "column": 29}, "end": {"line": 226, "column": 43}}, "86": {"start": {"line": 228, "column": 8}, "end": {"line": 237, "column": 9}}, "87": {"start": {"line": 229, "column": 10}, "end": {"line": 229, "column": 74}}, "88": {"start": {"line": 231, "column": 10}, "end": {"line": 236, "column": 13}}, "89": {"start": {"line": 234, "column": 14}, "end": {"line": 234, "column": 71}}, "90": {"start": {"line": 242, "column": 24}, "end": {"line": 328, "column": 5}}, "91": {"start": {"line": 243, "column": 6}, "end": {"line": 243, "column": 23}}, "92": {"start": {"line": 244, "column": 30}, "end": {"line": 244, "column": 45}}, "93": {"start": {"line": 247, "column": 6}, "end": {"line": 294, "column": 7}}, "94": {"start": {"line": 249, "column": 8}, "end": {"line": 253, "column": 12}}, "95": {"start": {"line": 250, "column": 30}, "end": {"line": 253, "column": 9}}, "96": {"start": {"line": 256, "column": 8}, "end": {"line": 269, "column": 11}}, "97": {"start": {"line": 273, "column": 35}, "end": {"line": 273, "column": 58}}, "98": {"start": {"line": 276, "column": 8}, "end": {"line": 293, "column": 9}}, "99": {"start": {"line": 277, "column": 10}, "end": {"line": 283, "column": 13}}, "100": {"start": {"line": 286, "column": 10}, "end": {"line": 286, "column": 47}}, "101": {"start": {"line": 289, "column": 10}, "end": {"line": 292, "column": 12}}, "102": {"start": {"line": 291, "column": 12}, "end": {"line": 291, "column": 69}}, "103": {"start": {"line": 297, "column": 6}, "end": {"line": 297, "column": 35}}, "104": {"start": {"line": 298, "column": 33}, "end": {"line": 298, "column": 56}}, "105": {"start": {"line": 301, "column": 20}, "end": {"line": 306, "column": 9}}, "106": {"start": {"line": 309, "column": 27}, "end": {"line": 309, "column": 37}}, "107": {"start": {"line": 312, "column": 37}, "end": {"line": 322, "column": 11}}, "108": {"start": {"line": 313, "column": 31}, "end": {"line": 321, "column": 24}}, "109": {"start": {"line": 317, "column": 14}, "end": {"line": 319, "column": 19}}, "110": {"start": {"line": 325, "column": 10}, "end": {"line": 325, "column": 76}}, "111": {"start": {"line": 326, "column": 10}, "end": {"line": 326, "column": 28}}, "112": {"start": {"line": 330, "column": 4}, "end": {"line": 330, "column": 25}}, "113": {"start": {"line": 331, "column": 4}, "end": {"line": 331, "column": 47}}, "114": {"start": {"line": 331, "column": 40}, "end": {"line": 331, "column": 47}}, "115": {"start": {"line": 333, "column": 4}, "end": {"line": 358, "column": 5}}, "116": {"start": {"line": 335, "column": 8}, "end": {"line": 335, "column": 29}}, "117": {"start": {"line": 336, "column": 8}, "end": {"line": 336, "column": 20}}, "118": {"start": {"line": 337, "column": 8}, "end": {"line": 337, "column": 14}}, "119": {"start": {"line": 340, "column": 8}, "end": {"line": 340, "column": 20}}, "120": {"start": {"line": 341, "column": 8}, "end": {"line": 341, "column": 14}}, "121": {"start": {"line": 344, "column": 8}, "end": {"line": 344, "column": 20}}, "122": {"start": {"line": 345, "column": 8}, "end": {"line": 345, "column": 14}}, "123": {"start": {"line": 348, "column": 8}, "end": {"line": 348, "column": 22}}, "124": {"start": {"line": 349, "column": 8}, "end": {"line": 349, "column": 14}}, "125": {"start": {"line": 353, "column": 8}, "end": {"line": 353, "column": 22}}, "126": {"start": {"line": 354, "column": 8}, "end": {"line": 354, "column": 14}}, "127": {"start": {"line": 357, "column": 8}, "end": {"line": 357, "column": 14}}, "128": {"start": {"line": 361, "column": 23}, "end": {"line": 370, "column": 3}}, "129": {"start": {"line": 362, "column": 4}, "end": {"line": 362, "column": 28}}, "130": {"start": {"line": 363, "column": 24}, "end": {"line": 368, "column": 5}}, "131": {"start": {"line": 369, "column": 4}, "end": {"line": 369, "column": 29}}, "132": {"start": {"line": 372, "column": 2}, "end": {"line": 480, "column": 4}}, "133": {"start": {"line": 381, "column": 25}, "end": {"line": 381, "column": 48}}, "134": {"start": {"line": 388, "column": 29}, "end": {"line": 388, "column": 43}}, "135": {"start": {"line": 398, "column": 34}, "end": {"line": 398, "column": 64}}, "136": {"start": {"line": 414, "column": 33}, "end": {"line": 414, "column": 65}}, "137": {"start": {"line": 420, "column": 35}, "end": {"line": 420, "column": 58}}, "138": {"start": {"line": 435, "column": 33}, "end": {"line": 435, "column": 55}}, "139": {"start": {"line": 456, "column": 42}, "end": {"line": 456, "column": 65}}, "140": {"start": {"line": 457, "column": 38}, "end": {"line": 457, "column": 68}}}, "fnMap": {"0": {"name": "Chatbox", "decl": {"start": {"line": 22, "column": 16}, "end": {"line": 22, "column": 23}}, "loc": {"start": {"line": 22, "column": 26}, "end": {"line": 481, "column": 1}}, "line": 22}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 36, "column": 12}, "end": {"line": 36, "column": 13}}, "loc": {"start": {"line": 36, "column": 18}, "end": {"line": 51, "column": 3}}, "line": 36}, "2": {"name": "load", "decl": {"start": {"line": 46, "column": 21}, "end": {"line": 46, "column": 25}}, "loc": {"start": {"line": 46, "column": 28}, "end": {"line": 48, "column": 7}}, "line": 46}, "3": {"name": "formatGreetingText", "decl": {"start": {"line": 53, "column": 17}, "end": {"line": 53, "column": 35}}, "loc": {"start": {"line": 53, "column": 38}, "end": {"line": 68, "column": 3}}, "line": 53}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 56, "column": 20}, "end": {"line": 56, "column": 21}}, "loc": {"start": {"line": 56, "column": 28}, "end": {"line": 56, "column": 60}}, "line": 56}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 61, "column": 27}, "end": {"line": 61, "column": 28}}, "loc": {"start": {"line": 62, "column": 10}, "end": {"line": 64, "column": 17}}, "line": 62}, "6": {"name": "initUserData", "decl": {"start": {"line": 70, "column": 17}, "end": {"line": 70, "column": 29}}, "loc": {"start": {"line": 70, "column": 32}, "end": {"line": 112, "column": 3}}, "line": 70}, "7": {"name": "(anonymous_7)", "decl": {"start": {"line": 87, "column": 16}, "end": {"line": 87, "column": 17}}, "loc": {"start": {"line": 87, "column": 47}, "end": {"line": 104, "column": 5}}, "line": 87}, "8": {"name": "(anonymous_8)", "decl": {"start": {"line": 89, "column": 60}, "end": {"line": 89, "column": 61}}, "loc": {"start": {"line": 89, "column": 79}, "end": {"line": 89, "column": 126}}, "line": 89}, "9": {"name": "(anonymous_9)", "decl": {"start": {"line": 96, "column": 49}, "end": {"line": 96, "column": 50}}, "loc": {"start": {"line": 96, "column": 68}, "end": {"line": 102, "column": 9}}, "line": 96}, "10": {"name": "(anonymous_10)", "decl": {"start": {"line": 115, "column": 30}, "end": {"line": 115, "column": 31}}, "loc": {"start": {"line": 115, "column": 36}, "end": {"line": 123, "column": 3}}, "line": 115}, "11": {"name": "(anonymous_11)", "decl": {"start": {"line": 116, "column": 16}, "end": {"line": 116, "column": 17}}, "loc": {"start": {"line": 116, "column": 33}, "end": {"line": 122, "column": 5}}, "line": 116}, "12": {"name": "(anonymous_12)", "decl": {"start": {"line": 118, "column": 52}, "end": {"line": 118, "column": 53}}, "loc": {"start": {"line": 119, "column": 6}, "end": {"line": 121, "column": 22}}, "line": 119}, "13": {"name": "(anonymous_13)", "decl": {"start": {"line": 126, "column": 32}, "end": {"line": 126, "column": 33}}, "loc": {"start": {"line": 126, "column": 38}, "end": {"line": 128, "column": 3}}, "line": 126}, "14": {"name": "(anonymous_14)", "decl": {"start": {"line": 127, "column": 39}, "end": {"line": 127, "column": 40}}, "loc": {"start": {"line": 127, "column": 55}, "end": {"line": 127, "column": 76}}, "line": 127}, "15": {"name": "(anonymous_15)", "decl": {"start": {"line": 131, "column": 28}, "end": {"line": 131, "column": 29}}, "loc": {"start": {"line": 131, "column": 34}, "end": {"line": 136, "column": 3}}, "line": 131}, "16": {"name": "(anonymous_16)", "decl": {"start": {"line": 139, "column": 32}, "end": {"line": 139, "column": 33}}, "loc": {"start": {"line": 139, "column": 38}, "end": {"line": 145, "column": 3}}, "line": 139}, "17": {"name": "(anonymous_17)", "decl": {"start": {"line": 148, "column": 31}, "end": {"line": 148, "column": 32}}, "loc": {"start": {"line": 148, "column": 55}, "end": {"line": 359, "column": 3}}, "line": 148}, "18": {"name": "(anonymous_18)", "decl": {"start": {"line": 149, "column": 22}, "end": {"line": 149, "column": 23}}, "loc": {"start": {"line": 149, "column": 28}, "end": {"line": 162, "column": 5}}, "line": 149}, "19": {"name": "(anonymous_19)", "decl": {"start": {"line": 152, "column": 13}, "end": {"line": 152, "column": 14}}, "loc": {"start": {"line": 152, "column": 29}, "end": {"line": 160, "column": 7}}, "line": 152}, "20": {"name": "(anonymous_20)", "decl": {"start": {"line": 164, "column": 22}, "end": {"line": 164, "column": 23}}, "loc": {"start": {"line": 164, "column": 28}, "end": {"line": 182, "column": 5}}, "line": 164}, "21": {"name": "(anonymous_21)", "decl": {"start": {"line": 178, "column": 18}, "end": {"line": 178, "column": 19}}, "loc": {"start": {"line": 178, "column": 34}, "end": {"line": 178, "column": 56}}, "line": 178}, "22": {"name": "(anonymous_22)", "decl": {"start": {"line": 184, "column": 22}, "end": {"line": 184, "column": 23}}, "loc": {"start": {"line": 184, "column": 28}, "end": {"line": 208, "column": 5}}, "line": 184}, "23": {"name": "(anonymous_23)", "decl": {"start": {"line": 200, "column": 52}, "end": {"line": 200, "column": 53}}, "loc": {"start": {"line": 201, "column": 12}, "end": {"line": 203, "column": 28}}, "line": 201}, "24": {"name": "(anonymous_24)", "decl": {"start": {"line": 210, "column": 24}, "end": {"line": 210, "column": 25}}, "loc": {"start": {"line": 210, "column": 36}, "end": {"line": 240, "column": 5}}, "line": 210}, "25": {"name": "(anonymous_25)", "decl": {"start": {"line": 233, "column": 54}, "end": {"line": 233, "column": 55}}, "loc": {"start": {"line": 234, "column": 14}, "end": {"line": 234, "column": 71}}, "line": 234}, "26": {"name": "(anonymous_26)", "decl": {"start": {"line": 242, "column": 24}, "end": {"line": 242, "column": 25}}, "loc": {"start": {"line": 242, "column": 36}, "end": {"line": 328, "column": 5}}, "line": 242}, "27": {"name": "(anonymous_27)", "decl": {"start": {"line": 250, "column": 13}, "end": {"line": 250, "column": 14}}, "loc": {"start": {"line": 250, "column": 30}, "end": {"line": 253, "column": 9}}, "line": 250}, "28": {"name": "(anonymous_28)", "decl": {"start": {"line": 290, "column": 15}, "end": {"line": 290, "column": 16}}, "loc": {"start": {"line": 291, "column": 12}, "end": {"line": 291, "column": 69}}, "line": 291}, "29": {"name": "(anonymous_29)", "decl": {"start": {"line": 313, "column": 15}, "end": {"line": 313, "column": 16}}, "loc": {"start": {"line": 313, "column": 31}, "end": {"line": 321, "column": 24}}, "line": 313}, "30": {"name": "(anonymous_30)", "decl": {"start": {"line": 316, "column": 60}, "end": {"line": 316, "column": 61}}, "loc": {"start": {"line": 317, "column": 14}, "end": {"line": 319, "column": 19}}, "line": 317}, "31": {"name": "(anonymous_31)", "decl": {"start": {"line": 361, "column": 23}, "end": {"line": 361, "column": 24}}, "loc": {"start": {"line": 361, "column": 35}, "end": {"line": 370, "column": 3}}, "line": 361}, "32": {"name": "(anonymous_32)", "decl": {"start": {"line": 381, "column": 19}, "end": {"line": 381, "column": 20}}, "loc": {"start": {"line": 381, "column": 25}, "end": {"line": 381, "column": 48}}, "line": 381}, "33": {"name": "(anonymous_33)", "decl": {"start": {"line": 388, "column": 23}, "end": {"line": 388, "column": 24}}, "loc": {"start": {"line": 388, "column": 29}, "end": {"line": 388, "column": 43}}, "line": 388}, "34": {"name": "(anonymous_34)", "decl": {"start": {"line": 398, "column": 20}, "end": {"line": 398, "column": 21}}, "loc": {"start": {"line": 398, "column": 34}, "end": {"line": 398, "column": 64}}, "line": 398}, "35": {"name": "(anonymous_35)", "decl": {"start": {"line": 414, "column": 27}, "end": {"line": 414, "column": 28}}, "loc": {"start": {"line": 414, "column": 33}, "end": {"line": 414, "column": 65}}, "line": 414}, "36": {"name": "(anonymous_36)", "decl": {"start": {"line": 420, "column": 29}, "end": {"line": 420, "column": 30}}, "loc": {"start": {"line": 420, "column": 35}, "end": {"line": 420, "column": 58}}, "line": 420}, "37": {"name": "(anonymous_37)", "decl": {"start": {"line": 435, "column": 27}, "end": {"line": 435, "column": 28}}, "loc": {"start": {"line": 435, "column": 33}, "end": {"line": 435, "column": 55}}, "line": 435}, "38": {"name": "(anonymous_38)", "decl": {"start": {"line": 456, "column": 26}, "end": {"line": 456, "column": 27}}, "loc": {"start": {"line": 456, "column": 42}, "end": {"line": 456, "column": 65}}, "line": 456}, "39": {"name": "(anonymous_39)", "decl": {"start": {"line": 457, "column": 24}, "end": {"line": 457, "column": 25}}, "loc": {"start": {"line": 457, "column": 38}, "end": {"line": 457, "column": 68}}, "line": 457}}, "branchMap": {"0": {"loc": {"start": {"line": 37, "column": 4}, "end": {"line": 42, "column": 5}}, "type": "if", "locations": [{"start": {"line": 37, "column": 4}, "end": {"line": 42, "column": 5}}, {"start": {}, "end": {}}], "line": 37}, "1": {"loc": {"start": {"line": 38, "column": 29}, "end": {"line": 38, "column": 71}}, "type": "binary-expr", "locations": [{"start": {"line": 38, "column": 29}, "end": {"line": 38, "column": 65}}, {"start": {"line": 38, "column": 69}, "end": {"line": 38, "column": 71}}], "line": 38}, "2": {"loc": {"start": {"line": 44, "column": 4}, "end": {"line": 50, "column": 5}}, "type": "if", "locations": [{"start": {"line": 44, "column": 4}, "end": {"line": 50, "column": 5}}, {"start": {}, "end": {}}], "line": 44}, "3": {"loc": {"start": {"line": 84, "column": 4}, "end": {"line": 111, "column": 3}}, "type": "if", "locations": [{"start": {"line": 84, "column": 4}, "end": {"line": 111, "column": 3}}, {"start": {}, "end": {}}], "line": 84}, "4": {"loc": {"start": {"line": 84, "column": 8}, "end": {"line": 84, "column": 63}}, "type": "binary-expr", "locations": [{"start": {"line": 84, "column": 8}, "end": {"line": 84, "column": 20}}, {"start": {"line": 84, "column": 25}, "end": {"line": 84, "column": 44}}, {"start": {"line": 84, "column": 48}, "end": {"line": 84, "column": 62}}], "line": 84}, "5": {"loc": {"start": {"line": 89, "column": 79}, "end": {"line": 89, "column": 126}}, "type": "cond-expr", "locations": [{"start": {"line": 89, "column": 95}, "end": {"line": 89, "column": 98}}, {"start": {"line": 89, "column": 102}, "end": {"line": 89, "column": 125}}], "line": 89}, "6": {"loc": {"start": {"line": 89, "column": 102}, "end": {"line": 89, "column": 125}}, "type": "cond-expr", "locations": [{"start": {"line": 89, "column": 116}, "end": {"line": 89, "column": 119}}, {"start": {"line": 89, "column": 122}, "end": {"line": 89, "column": 125}}], "line": 89}, "7": {"loc": {"start": {"line": 119, "column": 6}, "end": {"line": 121, "column": 22}}, "type": "cond-expr", "locations": [{"start": {"line": 120, "column": 10}, "end": {"line": 120, "column": 45}}, {"start": {"line": 121, "column": 10}, "end": {"line": 121, "column": 22}}], "line": 119}, "8": {"loc": {"start": {"line": 133, "column": 11}, "end": {"line": 135, "column": 11}}, "type": "cond-expr", "locations": [{"start": {"line": 134, "column": 6}, "end": {"line": 134, "column": 53}}, {"start": {"line": 135, "column": 6}, "end": {"line": 135, "column": 11}}], "line": 133}, "9": {"loc": {"start": {"line": 153, "column": 8}, "end": {"line": 158, "column": 9}}, "type": "if", "locations": [{"start": {"line": 153, "column": 8}, "end": {"line": 158, "column": 9}}, {"start": {"line": 156, "column": 15}, "end": {"line": 158, "column": 9}}], "line": 153}, "10": {"loc": {"start": {"line": 153, "column": 12}, "end": {"line": 154, "column": 54}}, "type": "binary-expr", "locations": [{"start": {"line": 153, "column": 12}, "end": {"line": 153, "column": 72}}, {"start": {"line": 154, "column": 13}, "end": {"line": 154, "column": 54}}], "line": 153}, "11": {"loc": {"start": {"line": 156, "column": 15}, "end": {"line": 158, "column": 9}}, "type": "if", "locations": [{"start": {"line": 156, "column": 15}, "end": {"line": 158, "column": 9}}, {"start": {}, "end": {}}], "line": 156}, "12": {"loc": {"start": {"line": 168, "column": 6}, "end": {"line": 181, "column": 7}}, "type": "if", "locations": [{"start": {"line": 168, "column": 6}, "end": {"line": 181, "column": 7}}, {"start": {}, "end": {}}], "line": 168}, "13": {"loc": {"start": {"line": 189, "column": 6}, "end": {"line": 207, "column": 7}}, "type": "if", "locations": [{"start": {"line": 189, "column": 6}, "end": {"line": 207, "column": 7}}, {"start": {}, "end": {}}], "line": 189}, "14": {"loc": {"start": {"line": 201, "column": 12}, "end": {"line": 203, "column": 28}}, "type": "cond-expr", "locations": [{"start": {"line": 202, "column": 16}, "end": {"line": 202, "column": 57}}, {"start": {"line": 203, "column": 16}, "end": {"line": 203, "column": 28}}], "line": 201}, "15": {"loc": {"start": {"line": 212, "column": 21}, "end": {"line": 212, "column": 67}}, "type": "cond-expr", "locations": [{"start": {"line": 212, "column": 61}, "end": {"line": 212, "column": 62}}, {"start": {"line": 212, "column": 65}, "end": {"line": 212, "column": 67}}], "line": 212}, "16": {"loc": {"start": {"line": 214, "column": 6}, "end": {"line": 238, "column": 7}}, "type": "if", "locations": [{"start": {"line": 214, "column": 6}, "end": {"line": 238, "column": 7}}, {"start": {}, "end": {}}], "line": 214}, "17": {"loc": {"start": {"line": 228, "column": 8}, "end": {"line": 237, "column": 9}}, "type": "if", "locations": [{"start": {"line": 228, "column": 8}, "end": {"line": 237, "column": 9}}, {"start": {}, "end": {}}], "line": 228}, "18": {"loc": {"start": {"line": 228, "column": 12}, "end": {"line": 228, "column": 88}}, "type": "binary-expr", "locations": [{"start": {"line": 228, "column": 12}, "end": {"line": 228, "column": 29}}, {"start": {"line": 228, "column": 33}, "end": {"line": 228, "column": 88}}], "line": 228}, "19": {"loc": {"start": {"line": 234, "column": 14}, "end": {"line": 234, "column": 71}}, "type": "cond-expr", "locations": [{"start": {"line": 234, "column": 38}, "end": {"line": 234, "column": 56}}, {"start": {"line": 234, "column": 59}, "end": {"line": 234, "column": 71}}], "line": 234}, "20": {"loc": {"start": {"line": 247, "column": 6}, "end": {"line": 294, "column": 7}}, "type": "if", "locations": [{"start": {"line": 247, "column": 6}, "end": {"line": 294, "column": 7}}, {"start": {"line": 272, "column": 13}, "end": {"line": 294, "column": 7}}], "line": 247}, "21": {"loc": {"start": {"line": 276, "column": 8}, "end": {"line": 293, "column": 9}}, "type": "if", "locations": [{"start": {"line": 276, "column": 8}, "end": {"line": 293, "column": 9}}, {"start": {}, "end": {}}], "line": 276}, "22": {"loc": {"start": {"line": 291, "column": 12}, "end": {"line": 291, "column": 69}}, "type": "cond-expr", "locations": [{"start": {"line": 291, "column": 36}, "end": {"line": 291, "column": 54}}, {"start": {"line": 291, "column": 57}, "end": {"line": 291, "column": 69}}], "line": 291}, "23": {"loc": {"start": {"line": 305, "column": 10}, "end": {"line": 305, "column": 69}}, "type": "cond-expr", "locations": [{"start": {"line": 305, "column": 31}, "end": {"line": 305, "column": 64}}, {"start": {"line": 305, "column": 67}, "end": {"line": 305, "column": 69}}], "line": 305}, "24": {"loc": {"start": {"line": 313, "column": 31}, "end": {"line": 321, "column": 24}}, "type": "cond-expr", "locations": [{"start": {"line": 313, "column": 55}, "end": {"line": 321, "column": 9}}, {"start": {"line": 321, "column": 12}, "end": {"line": 321, "column": 24}}], "line": 313}, "25": {"loc": {"start": {"line": 317, "column": 14}, "end": {"line": 319, "column": 19}}, "type": "cond-expr", "locations": [{"start": {"line": 318, "column": 12}, "end": {"line": 318, "column": 46}}, {"start": {"line": 319, "column": 12}, "end": {"line": 319, "column": 19}}], "line": 317}, "26": {"loc": {"start": {"line": 331, "column": 4}, "end": {"line": 331, "column": 47}}, "type": "if", "locations": [{"start": {"line": 331, "column": 4}, "end": {"line": 331, "column": 47}}, {"start": {}, "end": {}}], "line": 331}, "27": {"loc": {"start": {"line": 333, "column": 4}, "end": {"line": 358, "column": 5}}, "type": "switch", "locations": [{"start": {"line": 334, "column": 6}, "end": {"line": 337, "column": 14}}, {"start": {"line": 339, "column": 6}, "end": {"line": 341, "column": 14}}, {"start": {"line": 343, "column": 6}, "end": {"line": 345, "column": 14}}, {"start": {"line": 347, "column": 6}, "end": {"line": 349, "column": 14}}, {"start": {"line": 351, "column": 6}, "end": {"line": 351, "column": 27}}, {"start": {"line": 352, "column": 6}, "end": {"line": 354, "column": 14}}, {"start": {"line": 356, "column": 6}, "end": {"line": 357, "column": 14}}], "line": 333}}, "s": {"0": 1, "1": 1, "2": 53, "3": 53, "4": 53, "5": 53, "6": 53, "7": 53, "8": 53, "9": 53, "10": 53, "11": 53, "12": 11, "13": 0, "14": 0, "15": 0, "16": 0, "17": 11, "18": 11, "19": 11, "20": 11, "21": 53, "22": 53, "23": 159, "24": 53, "25": 159, "26": 11, "27": 11, "28": 11, "29": 11, "30": 11, "31": 11, "32": 22, "33": 88, "34": 22, "35": 22, "36": 11, "37": 11, "38": 53, "39": 0, "40": 0, "41": 0, "42": 53, "43": 6, "44": 8, "45": 53, "46": 2, "47": 2, "48": 53, "49": 1, "50": 1, "51": 1, "52": 1, "53": 1, "54": 53, "55": 6, "56": 1, "57": 1, "58": 4, "59": 2, "60": 2, "61": 0, "62": 2, "63": 1, "64": 6, "65": 0, "66": 0, "67": 0, "68": 0, "69": 0, "70": 0, "71": 0, "72": 0, "73": 6, "74": 0, "75": 0, "76": 0, "77": 0, "78": 0, "79": 0, "80": 6, "81": 2, "82": 2, "83": 2, "84": 2, "85": 2, "86": 2, "87": 2, "88": 2, "89": 4, "90": 6, "91": 2, "92": 2, "93": 2, "94": 2, "95": 2, "96": 2, "97": 0, "98": 0, "99": 0, "100": 0, "101": 0, "102": 0, "103": 2, "104": 2, "105": 2, "106": 2, "107": 2, "108": 4, "109": 2, "110": 2, "111": 2, "112": 6, "113": 6, "114": 1, "115": 5, "116": 1, "117": 1, "118": 1, "119": 0, "120": 0, "121": 0, "122": 0, "123": 2, "124": 2, "125": 2, "126": 2, "127": 0, "128": 53, "129": 1, "130": 1, "131": 1, "132": 53, "133": 1, "134": 1, "135": 1, "136": 5, "137": 1, "138": 2, "139": 15, "140": 4}, "f": {"0": 53, "1": 11, "2": 11, "3": 53, "4": 159, "5": 159, "6": 11, "7": 22, "8": 88, "9": 22, "10": 0, "11": 0, "12": 0, "13": 6, "14": 8, "15": 2, "16": 1, "17": 6, "18": 1, "19": 4, "20": 0, "21": 0, "22": 0, "23": 0, "24": 2, "25": 4, "26": 2, "27": 2, "28": 0, "29": 4, "30": 2, "31": 1, "32": 1, "33": 1, "34": 1, "35": 5, "36": 1, "37": 2, "38": 15, "39": 4}, "b": {"0": [0, 11], "1": [0, 0], "2": [11, 0], "3": [11, 0], "4": [11, 11, 11], "5": [0, 88], "6": [44, 44], "7": [0, 0], "8": [2, 0], "9": [2, 2], "10": [4, 4], "11": [0, 2], "12": [0, 0], "13": [0, 0], "14": [0, 0], "15": [1, 1], "16": [2, 0], "17": [2, 0], "18": [2, 2], "19": [2, 2], "20": [2, 0], "21": [0, 0], "22": [0, 0], "23": [2, 0], "24": [2, 2], "25": [2, 0], "26": [1, 5], "27": [1, 0, 0, 2, 1, 2, 0]}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "5f1e1a5de885ad9b2c11e148285c5972922f47c0"}, "/Users/<USER>/Documents/PAASApplications/premium.audit.advisory.service.mobile/app/messages.tsx": {"path": "/Users/<USER>/Documents/PAASApplications/premium.audit.advisory.service.mobile/app/messages.tsx", "statementMap": {"0": {"start": {"line": 34, "column": 26}, "end": {"line": 34, "column": 38}}, "1": {"start": {"line": 35, "column": 26}, "end": {"line": 35, "column": 38}}, "2": {"start": {"line": 36, "column": 36}, "end": {"line": 36, "column": 62}}, "3": {"start": {"line": 39, "column": 34}, "end": {"line": 39, "column": 49}}, "4": {"start": {"line": 40, "column": 36}, "end": {"line": 40, "column": 51}}, "5": {"start": {"line": 41, "column": 40}, "end": {"line": 41, "column": 70}}, "6": {"start": {"line": 42, "column": 40}, "end": {"line": 42, "column": 55}}, "7": {"start": {"line": 44, "column": 21}, "end": {"line": 44, "column": 30}}, "8": {"start": {"line": 45, "column": 17}, "end": {"line": 45, "column": 34}}, "9": {"start": {"line": 47, "column": 22}, "end": {"line": 47, "column": 53}}, "10": {"start": {"line": 50, "column": 20}, "end": {"line": 50, "column": 47}}, "11": {"start": {"line": 51, "column": 16}, "end": {"line": 51, "column": 25}}, "12": {"start": {"line": 53, "column": 0}, "end": {"line": 56, "column": 16}}, "13": {"start": {"line": 54, "column": 2}, "end": {"line": 54, "column": 25}}, "14": {"start": {"line": 58, "column": 0}, "end": {"line": 60, "column": 30}}, "15": {"start": {"line": 59, "column": 2}, "end": {"line": 59, "column": 42}}, "16": {"start": {"line": 63, "column": 20}, "end": {"line": 71, "column": 1}}, "17": {"start": {"line": 64, "column": 2}, "end": {"line": 65, "column": 44}}, "18": {"start": {"line": 66, "column": 2}, "end": {"line": 66, "column": 51}}, "19": {"start": {"line": 68, "column": 2}, "end": {"line": 68, "column": 35}}, "20": {"start": {"line": 69, "column": 2}, "end": {"line": 70, "column": 61}}, "21": {"start": {"line": 70, "column": 4}, "end": {"line": 70, "column": 53}}, "22": {"start": {"line": 74, "column": 20}, "end": {"line": 76, "column": 1}}, "23": {"start": {"line": 75, "column": 2}, "end": {"line": 75, "column": 76}}, "24": {"start": {"line": 78, "column": 26}, "end": {"line": 80, "column": 1}}, "25": {"start": {"line": 79, "column": 2}, "end": {"line": 79, "column": 45}}, "26": {"start": {"line": 82, "column": 19}, "end": {"line": 95, "column": 1}}, "27": {"start": {"line": 83, "column": 2}, "end": {"line": 94, "column": 3}}, "28": {"start": {"line": 85, "column": 6}, "end": {"line": 86, "column": 45}}, "29": {"start": {"line": 87, "column": 6}, "end": {"line": 87, "column": 19}}, "30": {"start": {"line": 89, "column": 6}, "end": {"line": 89, "column": 12}}, "31": {"start": {"line": 92, "column": 6}, "end": {"line": 92, "column": 79}}, "32": {"start": {"line": 93, "column": 6}, "end": {"line": 93, "column": 12}}, "33": {"start": {"line": 98, "column": 20}, "end": {"line": 106, "column": 1}}, "34": {"start": {"line": 99, "column": 2}, "end": {"line": 105, "column": 3}}, "35": {"start": {"line": 100, "column": 4}, "end": {"line": 100, "column": 20}}, "36": {"start": {"line": 101, "column": 4}, "end": {"line": 101, "column": 36}}, "37": {"start": {"line": 103, "column": 4}, "end": {"line": 104, "column": 49}}, "38": {"start": {"line": 108, "column": 27}, "end": {"line": 111, "column": 1}}, "39": {"start": {"line": 109, "column": 2}, "end": {"line": 109, "column": 45}}, "40": {"start": {"line": 109, "column": 38}, "end": {"line": 109, "column": 45}}, "41": {"start": {"line": 110, "column": 2}, "end": {"line": 110, "column": 23}}, "42": {"start": {"line": 113, "column": 20}, "end": {"line": 115, "column": 1}}, "43": {"start": {"line": 114, "column": 2}, "end": {"line": 114, "column": 31}}, "44": {"start": {"line": 117, "column": 23}, "end": {"line": 122, "column": 1}}, "45": {"start": {"line": 118, "column": 2}, "end": {"line": 121, "column": 3}}, "46": {"start": {"line": 119, "column": 4}, "end": {"line": 119, "column": 33}}, "47": {"start": {"line": 120, "column": 4}, "end": {"line": 120, "column": 17}}, "48": {"start": {"line": 125, "column": 24}, "end": {"line": 232, "column": 1}}, "49": {"start": {"line": 126, "column": 29}, "end": {"line": 127, "column": 48}}, "50": {"start": {"line": 127, "column": 26}, "end": {"line": 127, "column": 47}}, "51": {"start": {"line": 130, "column": 2}, "end": {"line": 229, "column": 3}}, "52": {"start": {"line": 131, "column": 4}, "end": {"line": 151, "column": 6}}, "53": {"start": {"line": 154, "column": 9}, "end": {"line": 229, "column": 3}}, "54": {"start": {"line": 155, "column": 6}, "end": {"line": 228, "column": 9}}, "55": {"start": {"line": 157, "column": 8}, "end": {"line": 227, "column": 13}}, "56": {"start": {"line": 190, "column": 16}, "end": {"line": 192, "column": 23}}, "57": {"start": {"line": 204, "column": 29}, "end": {"line": 204, "column": 56}}, "58": {"start": {"line": 212, "column": 29}, "end": {"line": 212, "column": 64}}, "59": {"start": {"line": 220, "column": 29}, "end": {"line": 220, "column": 66}}, "60": {"start": {"line": 231, "column": 2}, "end": {"line": 231, "column": 14}}, "61": {"start": {"line": 234, "column": 2}, "end": {"line": 302, "column": 4}}, "62": {"start": {"line": 244, "column": 12}, "end": {"line": 244, "column": 67}}, "63": {"start": {"line": 264, "column": 43}, "end": {"line": 266, "column": 52}}, "64": {"start": {"line": 274, "column": 37}, "end": {"line": 274, "column": 65}}, "65": {"start": {"line": 298, "column": 39}, "end": {"line": 299, "column": 44}}}, "fnMap": {"0": {"name": "Messages", "decl": {"start": {"line": 23, "column": 16}, "end": {"line": 23, "column": 24}}, "loc": {"start": {"line": 33, "column": 3}, "end": {"line": 303, "column": 1}}, "line": 33}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 53, "column": 10}, "end": {"line": 53, "column": 11}}, "loc": {"start": {"line": 53, "column": 16}, "end": {"line": 56, "column": 1}}, "line": 53}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 58, "column": 10}, "end": {"line": 58, "column": 11}}, "loc": {"start": {"line": 58, "column": 16}, "end": {"line": 60, "column": 1}}, "line": 58}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 63, "column": 20}, "end": {"line": 63, "column": 21}}, "loc": {"start": {"line": 63, "column": 57}, "end": {"line": 71, "column": 1}}, "line": 63}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 69, "column": 34}, "end": {"line": 69, "column": 35}}, "loc": {"start": {"line": 70, "column": 4}, "end": {"line": 70, "column": 53}}, "line": 70}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 74, "column": 20}, "end": {"line": 74, "column": 21}}, "loc": {"start": {"line": 74, "column": 46}, "end": {"line": 76, "column": 1}}, "line": 74}, "6": {"name": "(anonymous_6)", "decl": {"start": {"line": 78, "column": 26}, "end": {"line": 78, "column": 27}}, "loc": {"start": {"line": 78, "column": 47}, "end": {"line": 80, "column": 1}}, "line": 78}, "7": {"name": "(anonymous_7)", "decl": {"start": {"line": 82, "column": 19}, "end": {"line": 82, "column": 20}}, "loc": {"start": {"line": 82, "column": 43}, "end": {"line": 95, "column": 1}}, "line": 82}, "8": {"name": "(anonymous_8)", "decl": {"start": {"line": 98, "column": 20}, "end": {"line": 98, "column": 21}}, "loc": {"start": {"line": 98, "column": 59}, "end": {"line": 106, "column": 1}}, "line": 98}, "9": {"name": "(anonymous_9)", "decl": {"start": {"line": 108, "column": 27}, "end": {"line": 108, "column": 28}}, "loc": {"start": {"line": 108, "column": 51}, "end": {"line": 111, "column": 1}}, "line": 108}, "10": {"name": "(anonymous_10)", "decl": {"start": {"line": 113, "column": 20}, "end": {"line": 113, "column": 21}}, "loc": {"start": {"line": 113, "column": 38}, "end": {"line": 115, "column": 1}}, "line": 113}, "11": {"name": "(anonymous_11)", "decl": {"start": {"line": 117, "column": 23}, "end": {"line": 117, "column": 24}}, "loc": {"start": {"line": 117, "column": 89}, "end": {"line": 122, "column": 1}}, "line": 117}, "12": {"name": "(anonymous_12)", "decl": {"start": {"line": 125, "column": 24}, "end": {"line": 125, "column": 25}}, "loc": {"start": {"line": 125, "column": 30}, "end": {"line": 232, "column": 1}}, "line": 125}, "13": {"name": "(anonymous_13)", "decl": {"start": {"line": 127, "column": 10}, "end": {"line": 127, "column": 11}}, "loc": {"start": {"line": 127, "column": 26}, "end": {"line": 127, "column": 47}}, "line": 127}, "14": {"name": "(anonymous_14)", "decl": {"start": {"line": 156, "column": 13}, "end": {"line": 156, "column": 14}}, "loc": {"start": {"line": 157, "column": 8}, "end": {"line": 227, "column": 13}}, "line": 157}, "15": {"name": "(anonymous_15)", "decl": {"start": {"line": 189, "column": 36}, "end": {"line": 189, "column": 37}}, "loc": {"start": {"line": 190, "column": 16}, "end": {"line": 192, "column": 23}}, "line": 190}, "16": {"name": "(anonymous_16)", "decl": {"start": {"line": 204, "column": 23}, "end": {"line": 204, "column": 24}}, "loc": {"start": {"line": 204, "column": 29}, "end": {"line": 204, "column": 56}}, "line": 204}, "17": {"name": "(anonymous_17)", "decl": {"start": {"line": 212, "column": 23}, "end": {"line": 212, "column": 24}}, "loc": {"start": {"line": 212, "column": 29}, "end": {"line": 212, "column": 64}}, "line": 212}, "18": {"name": "(anonymous_18)", "decl": {"start": {"line": 220, "column": 23}, "end": {"line": 220, "column": 24}}, "loc": {"start": {"line": 220, "column": 29}, "end": {"line": 220, "column": 66}}, "line": 220}, "19": {"name": "(anonymous_19)", "decl": {"start": {"line": 243, "column": 29}, "end": {"line": 243, "column": 30}}, "loc": {"start": {"line": 243, "column": 35}, "end": {"line": 245, "column": 9}}, "line": 243}, "20": {"name": "(anonymous_20)", "decl": {"start": {"line": 264, "column": 37}, "end": {"line": 264, "column": 38}}, "loc": {"start": {"line": 264, "column": 43}, "end": {"line": 266, "column": 52}}, "line": 264}, "21": {"name": "(anonymous_21)", "decl": {"start": {"line": 265, "column": 24}, "end": {"line": 265, "column": 25}}, "loc": {"start": {"line": 265, "column": 30}, "end": {"line": 265, "column": 32}}, "line": 265}, "22": {"name": "(anonymous_22)", "decl": {"start": {"line": 274, "column": 31}, "end": {"line": 274, "column": 32}}, "loc": {"start": {"line": 274, "column": 37}, "end": {"line": 274, "column": 65}}, "line": 274}, "23": {"name": "(anonymous_23)", "decl": {"start": {"line": 298, "column": 23}, "end": {"line": 298, "column": 24}}, "loc": {"start": {"line": 298, "column": 37}, "end": {"line": 299, "column": 45}}, "line": 298}}, "branchMap": {"0": {"loc": {"start": {"line": 59, "column": 15}, "end": {"line": 59, "column": 40}}, "type": "binary-expr", "locations": [{"start": {"line": 59, "column": 15}, "end": {"line": 59, "column": 26}}, {"start": {"line": 59, "column": 30}, "end": {"line": 59, "column": 40}}], "line": 59}, "1": {"loc": {"start": {"line": 83, "column": 2}, "end": {"line": 94, "column": 3}}, "type": "switch", "locations": [{"start": {"line": 84, "column": 4}, "end": {"line": 89, "column": 12}}, {"start": {"line": 91, "column": 4}, "end": {"line": 93, "column": 12}}], "line": 83}, "2": {"loc": {"start": {"line": 99, "column": 2}, "end": {"line": 105, "column": 3}}, "type": "if", "locations": [{"start": {"line": 99, "column": 2}, "end": {"line": 105, "column": 3}}, {"start": {"line": 102, "column": 9}, "end": {"line": 105, "column": 3}}], "line": 99}, "3": {"loc": {"start": {"line": 109, "column": 2}, "end": {"line": 109, "column": 45}}, "type": "if", "locations": [{"start": {"line": 109, "column": 2}, "end": {"line": 109, "column": 45}}, {"start": {}, "end": {}}], "line": 109}, "4": {"loc": {"start": {"line": 118, "column": 2}, "end": {"line": 121, "column": 3}}, "type": "if", "locations": [{"start": {"line": 118, "column": 2}, "end": {"line": 121, "column": 3}}, {"start": {}, "end": {}}], "line": 118}, "5": {"loc": {"start": {"line": 118, "column": 6}, "end": {"line": 118, "column": 73}}, "type": "binary-expr", "locations": [{"start": {"line": 118, "column": 6}, "end": {"line": 118, "column": 33}}, {"start": {"line": 118, "column": 37}, "end": {"line": 118, "column": 73}}], "line": 118}, "6": {"loc": {"start": {"line": 118, "column": 39}, "end": {"line": 118, "column": 72}}, "type": "binary-expr", "locations": [{"start": {"line": 118, "column": 39}, "end": {"line": 118, "column": 58}}, {"start": {"line": 118, "column": 62}, "end": {"line": 118, "column": 72}}], "line": 118}, "7": {"loc": {"start": {"line": 130, "column": 2}, "end": {"line": 229, "column": 3}}, "type": "if", "locations": [{"start": {"line": 130, "column": 2}, "end": {"line": 229, "column": 3}}, {"start": {"line": 154, "column": 9}, "end": {"line": 229, "column": 3}}], "line": 130}, "8": {"loc": {"start": {"line": 130, "column": 6}, "end": {"line": 130, "column": 75}}, "type": "binary-expr", "locations": [{"start": {"line": 130, "column": 6}, "end": {"line": 130, "column": 24}}, {"start": {"line": 130, "column": 28}, "end": {"line": 130, "column": 75}}], "line": 130}, "9": {"loc": {"start": {"line": 133, "column": 11}, "end": {"line": 148, "column": 11}}, "type": "cond-expr", "locations": [{"start": {"line": 134, "column": 14}, "end": {"line": 134, "column": 19}}, {"start": {"line": 136, "column": 12}, "end": {"line": 147, "column": 19}}], "line": 133}, "10": {"loc": {"start": {"line": 154, "column": 9}, "end": {"line": 229, "column": 3}}, "type": "if", "locations": [{"start": {"line": 154, "column": 9}, "end": {"line": 229, "column": 3}}, {"start": {}, "end": {}}], "line": 154}, "11": {"loc": {"start": {"line": 160, "column": 9}, "end": {"line": 171, "column": 9}}, "type": "binary-expr", "locations": [{"start": {"line": 160, "column": 9}, "end": {"line": 160, "column": 31}}, {"start": {"line": 161, "column": 8}, "end": {"line": 170, "column": 15}}], "line": 160}, "12": {"loc": {"start": {"line": 180, "column": 15}, "end": {"line": 182, "column": 47}}, "type": "cond-expr", "locations": [{"start": {"line": 181, "column": 18}, "end": {"line": 181, "column": 36}}, {"start": {"line": 182, "column": 18}, "end": {"line": 182, "column": 47}}], "line": 180}, "13": {"loc": {"start": {"line": 186, "column": 11}, "end": {"line": 195, "column": 11}}, "type": "binary-expr", "locations": [{"start": {"line": 186, "column": 11}, "end": {"line": 186, "column": 27}}, {"start": {"line": 186, "column": 31}, "end": {"line": 186, "column": 58}}, {"start": {"line": 187, "column": 12}, "end": {"line": 194, "column": 19}}], "line": 186}, "14": {"loc": {"start": {"line": 198, "column": 11}, "end": {"line": 225, "column": 11}}, "type": "binary-expr", "locations": [{"start": {"line": 198, "column": 11}, "end": {"line": 198, "column": 32}}, {"start": {"line": 199, "column": 10}, "end": {"line": 224, "column": 17}}], "line": 198}, "15": {"loc": {"start": {"line": 205, "column": 32}, "end": {"line": 206, "column": 49}}, "type": "binary-expr", "locations": [{"start": {"line": 205, "column": 32}, "end": {"line": 205, "column": 53}}, {"start": {"line": 206, "column": 21}, "end": {"line": 206, "column": 49}}], "line": 205}, "16": {"loc": {"start": {"line": 213, "column": 24}, "end": {"line": 213, "column": 58}}, "type": "binary-expr", "locations": [{"start": {"line": 213, "column": 24}, "end": {"line": 213, "column": 44}}, {"start": {"line": 213, "column": 48}, "end": {"line": 213, "column": 58}}], "line": 213}, "17": {"loc": {"start": {"line": 221, "column": 24}, "end": {"line": 221, "column": 58}}, "type": "binary-expr", "locations": [{"start": {"line": 221, "column": 24}, "end": {"line": 221, "column": 44}}, {"start": {"line": 221, "column": 48}, "end": {"line": 221, "column": 58}}], "line": 221}, "18": {"loc": {"start": {"line": 236, "column": 17}, "end": {"line": 236, "column": 61}}, "type": "cond-expr", "locations": [{"start": {"line": 236, "column": 41}, "end": {"line": 236, "column": 50}}, {"start": {"line": 236, "column": 53}, "end": {"line": 236, "column": 61}}], "line": 236}, "19": {"loc": {"start": {"line": 237, "column": 31}, "end": {"line": 237, "column": 62}}, "type": "cond-expr", "locations": [{"start": {"line": 237, "column": 55}, "end": {"line": 237, "column": 58}}, {"start": {"line": 237, "column": 61}, "end": {"line": 237, "column": 62}}], "line": 237}, "20": {"loc": {"start": {"line": 255, "column": 35}, "end": {"line": 255, "column": 62}}, "type": "cond-expr", "locations": [{"start": {"line": 255, "column": 43}, "end": {"line": 255, "column": 51}}, {"start": {"line": 255, "column": 54}, "end": {"line": 255, "column": 62}}], "line": 255}, "21": {"loc": {"start": {"line": 264, "column": 43}, "end": {"line": 266, "column": 52}}, "type": "cond-expr", "locations": [{"start": {"line": 265, "column": 24}, "end": {"line": 265, "column": 32}}, {"start": {"line": 266, "column": 24}, "end": {"line": 266, "column": 52}}], "line": 264}, "22": {"loc": {"start": {"line": 264, "column": 43}, "end": {"line": 264, "column": 91}}, "type": "binary-expr", "locations": [{"start": {"line": 264, "column": 43}, "end": {"line": 264, "column": 62}}, {"start": {"line": 264, "column": 66}, "end": {"line": 264, "column": 76}}, {"start": {"line": 264, "column": 80}, "end": {"line": 264, "column": 91}}], "line": 264}, "23": {"loc": {"start": {"line": 275, "column": 32}, "end": {"line": 275, "column": 80}}, "type": "binary-expr", "locations": [{"start": {"line": 275, "column": 32}, "end": {"line": 275, "column": 51}}, {"start": {"line": 275, "column": 55}, "end": {"line": 275, "column": 65}}, {"start": {"line": 275, "column": 69}, "end": {"line": 275, "column": 80}}], "line": 275}, "24": {"loc": {"start": {"line": 277, "column": 34}, "end": {"line": 277, "column": 84}}, "type": "binary-expr", "locations": [{"start": {"line": 277, "column": 34}, "end": {"line": 277, "column": 53}}, {"start": {"line": 277, "column": 57}, "end": {"line": 277, "column": 68}}, {"start": {"line": 277, "column": 72}, "end": {"line": 277, "column": 84}}], "line": 277}}, "s": {"0": 59, "1": 59, "2": 59, "3": 59, "4": 59, "5": 59, "6": 59, "7": 59, "8": 59, "9": 59, "10": 59, "11": 59, "12": 59, "13": 15, "14": 59, "15": 15, "16": 59, "17": 1, "18": 1, "19": 1, "20": 1, "21": 0, "22": 59, "23": 2, "24": 59, "25": 2, "26": 59, "27": 2, "28": 2, "29": 2, "30": 2, "31": 0, "32": 0, "33": 59, "34": 2, "35": 1, "36": 1, "37": 1, "38": 59, "39": 1, "40": 0, "41": 1, "42": 59, "43": 0, "44": 59, "45": 0, "46": 0, "47": 0, "48": 59, "49": 59, "50": 75, "51": 59, "52": 43, "53": 16, "54": 16, "55": 16, "56": 0, "57": 1, "58": 1, "59": 1, "60": 0, "61": 59, "62": 0, "63": 0, "64": 2, "65": 1}, "f": {"0": 59, "1": 15, "2": 15, "3": 1, "4": 0, "5": 2, "6": 2, "7": 2, "8": 2, "9": 1, "10": 0, "11": 0, "12": 59, "13": 75, "14": 16, "15": 0, "16": 1, "17": 1, "18": 1, "19": 0, "20": 0, "21": 0, "22": 2, "23": 1}, "b": {"0": [15, 15], "1": [2, 0], "2": [1, 1], "3": [0, 1], "4": [0, 0], "5": [0, 0], "6": [0, 0], "7": [43, 16], "8": [59, 59], "9": [29, 14], "10": [16, 0], "11": [16, 16], "12": [6, 10], "13": [16, 16, 0], "14": [16, 10], "15": [10, 1], "16": [10, 7], "17": [10, 7], "18": [59, 0], "19": [59, 0], "20": [2, 57], "21": [0, 0], "22": [0, 0, 0], "23": [59, 2, 2], "24": [59, 2, 2]}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "331a03a00d2c8a248908d099be20aa1299fa591b"}, "/Users/<USER>/Documents/PAASApplications/premium.audit.advisory.service.mobile/app/stylesheet.tsx": {"path": "/Users/<USER>/Documents/PAASApplications/premium.audit.advisory.service.mobile/app/stylesheet.tsx", "statementMap": {"0": {"start": {"line": 3, "column": 22}, "end": {"line": 41, "column": 2}}, "1": {"start": {"line": 43, "column": 25}, "end": {"line": 136, "column": 2}}, "2": {"start": {"line": 138, "column": 28}, "end": {"line": 187, "column": 2}}, "3": {"start": {"line": 189, "column": 25}, "end": {"line": 209, "column": 2}}, "4": {"start": {"line": 211, "column": 26}, "end": {"line": 321, "column": 2}}, "5": {"start": {"line": 323, "column": 29}, "end": {"line": 382, "column": 2}}, "6": {"start": {"line": 384, "column": 27}, "end": {"line": 583, "column": 2}}, "7": {"start": {"line": 585, "column": 29}, "end": {"line": 746, "column": 2}}, "8": {"start": {"line": 748, "column": 29}, "end": {"line": 804, "column": 2}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 3, "1": 3, "2": 3, "3": 3, "4": 3, "5": 3, "6": 3, "7": 3, "8": 3}, "f": {}, "b": {}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "e21a6708948989bc998fa185a6e2cfec5fe8a5f3"}, "/Users/<USER>/Documents/PAASApplications/premium.audit.advisory.service.mobile/app/helpers/model.tsx": {"path": "/Users/<USER>/Documents/PAASApplications/premium.audit.advisory.service.mobile/app/helpers/model.tsx", "statementMap": {"0": {"start": {"line": 2, "column": 19}, "end": {"line": 2, "column": 21}}, "1": {"start": {"line": 3, "column": 24}, "end": {"line": 3, "column": 26}}, "2": {"start": {"line": 4, "column": 20}, "end": {"line": 4, "column": 25}}, "3": {"start": {"line": 5, "column": 21}, "end": {"line": 5, "column": 23}}, "4": {"start": {"line": 9, "column": 4}, "end": {"line": 9, "column": 25}}, "5": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 35}}, "6": {"start": {"line": 11, "column": 4}, "end": {"line": 11, "column": 25}}, "7": {"start": {"line": 12, "column": 4}, "end": {"line": 12, "column": 29}}, "8": {"start": {"line": 17, "column": 19}, "end": {"line": 17, "column": 21}}, "9": {"start": {"line": 18, "column": 34}, "end": {"line": 18, "column": 36}}, "10": {"start": {"line": 21, "column": 4}, "end": {"line": 21, "column": 21}}, "11": {"start": {"line": 22, "column": 4}, "end": {"line": 39, "column": 6}}, "12": {"start": {"line": 114, "column": 22}, "end": {"line": 114, "column": 27}}, "13": {"start": {"line": 115, "column": 18}, "end": {"line": 115, "column": 20}}, "14": {"start": {"line": 118, "column": 4}, "end": {"line": 118, "column": 29}}, "15": {"start": {"line": 119, "column": 4}, "end": {"line": 119, "column": 23}}, "16": {"start": {"line": 124, "column": 17}, "end": {"line": 124, "column": 19}}, "17": {"start": {"line": 125, "column": 21}, "end": {"line": 125, "column": 23}}, "18": {"start": {"line": 126, "column": 18}, "end": {"line": 126, "column": 20}}, "19": {"start": {"line": 127, "column": 25}, "end": {"line": 127, "column": 30}}, "20": {"start": {"line": 128, "column": 23}, "end": {"line": 128, "column": 39}}, "21": {"start": {"line": 129, "column": 20}, "end": {"line": 129, "column": 22}}, "22": {"start": {"line": 130, "column": 27}, "end": {"line": 130, "column": 45}}, "23": {"start": {"line": 135, "column": 6}, "end": {"line": 135, "column": 23}}, "24": {"start": {"line": 136, "column": 6}, "end": {"line": 136, "column": 31}}, "25": {"start": {"line": 137, "column": 6}, "end": {"line": 137, "column": 25}}, "26": {"start": {"line": 138, "column": 6}, "end": {"line": 138, "column": 37}}, "27": {"start": {"line": 139, "column": 6}, "end": {"line": 139, "column": 31}}, "28": {"start": {"line": 140, "column": 6}, "end": {"line": 140, "column": 29}}, "29": {"start": {"line": 141, "column": 6}, "end": {"line": 141, "column": 43}}, "30": {"start": {"line": 159, "column": 4}, "end": {"line": 159, "column": 31}}, "31": {"start": {"line": 160, "column": 4}, "end": {"line": 160, "column": 31}}, "32": {"start": {"line": 168, "column": 34}, "end": {"line": 168, "column": 39}}, "33": {"start": {"line": 172, "column": 4}, "end": {"line": 172, "column": 31}}, "34": {"start": {"line": 174, "column": 4}, "end": {"line": 214, "column": 5}}, "35": {"start": {"line": 175, "column": 6}, "end": {"line": 175, "column": 33}}, "36": {"start": {"line": 176, "column": 6}, "end": {"line": 180, "column": 94}}, "37": {"start": {"line": 181, "column": 6}, "end": {"line": 181, "column": 40}}, "38": {"start": {"line": 182, "column": 6}, "end": {"line": 182, "column": 27}}, "39": {"start": {"line": 184, "column": 11}, "end": {"line": 214, "column": 5}}, "40": {"start": {"line": 185, "column": 6}, "end": {"line": 185, "column": 32}}, "41": {"start": {"line": 186, "column": 6}, "end": {"line": 186, "column": 36}}, "42": {"start": {"line": 187, "column": 6}, "end": {"line": 187, "column": 40}}, "43": {"start": {"line": 188, "column": 6}, "end": {"line": 188, "column": 27}}, "44": {"start": {"line": 190, "column": 11}, "end": {"line": 214, "column": 5}}, "45": {"start": {"line": 191, "column": 6}, "end": {"line": 191, "column": 41}}, "46": {"start": {"line": 192, "column": 6}, "end": {"line": 192, "column": 24}}, "47": {"start": {"line": 193, "column": 6}, "end": {"line": 193, "column": 39}}, "48": {"start": {"line": 194, "column": 6}, "end": {"line": 194, "column": 33}}, "49": {"start": {"line": 196, "column": 11}, "end": {"line": 214, "column": 5}}, "50": {"start": {"line": 197, "column": 6}, "end": {"line": 197, "column": 41}}, "51": {"start": {"line": 198, "column": 6}, "end": {"line": 199, "column": 35}}, "52": {"start": {"line": 200, "column": 6}, "end": {"line": 200, "column": 39}}, "53": {"start": {"line": 201, "column": 6}, "end": {"line": 201, "column": 33}}, "54": {"start": {"line": 203, "column": 11}, "end": {"line": 214, "column": 5}}, "55": {"start": {"line": 204, "column": 6}, "end": {"line": 204, "column": 38}}, "56": {"start": {"line": 205, "column": 6}, "end": {"line": 205, "column": 66}}, "57": {"start": {"line": 206, "column": 6}, "end": {"line": 206, "column": 39}}, "58": {"start": {"line": 207, "column": 6}, "end": {"line": 207, "column": 33}}, "59": {"start": {"line": 210, "column": 6}, "end": {"line": 210, "column": 22}}, "60": {"start": {"line": 211, "column": 6}, "end": {"line": 211, "column": 24}}, "61": {"start": {"line": 212, "column": 6}, "end": {"line": 212, "column": 40}}, "62": {"start": {"line": 213, "column": 6}, "end": {"line": 213, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 7, "column": 2}, "end": {"line": 7, "column": 3}}, "loc": {"start": {"line": 8, "column": 27}, "end": {"line": 13, "column": 3}}, "line": 8}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 20, "column": 2}, "end": {"line": 20, "column": 3}}, "loc": {"start": {"line": 20, "column": 16}, "end": {"line": 40, "column": 3}}, "line": 20}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 117, "column": 2}, "end": {"line": 117, "column": 3}}, "loc": {"start": {"line": 117, "column": 61}, "end": {"line": 120, "column": 3}}, "line": 117}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 132, "column": 2}, "end": {"line": 132, "column": 3}}, "loc": {"start": {"line": 134, "column": 70}, "end": {"line": 142, "column": 3}}, "line": 134}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 158, "column": 2}, "end": {"line": 158, "column": 3}}, "loc": {"start": {"line": 158, "column": 84}, "end": {"line": 161, "column": 3}}, "line": 158}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 171, "column": 2}, "end": {"line": 171, "column": 3}}, "loc": {"start": {"line": 171, "column": 56}, "end": {"line": 215, "column": 3}}, "line": 171}}, "branchMap": {"0": {"loc": {"start": {"line": 7, "column": 14}, "end": {"line": 7, "column": 33}}, "type": "default-arg", "locations": [{"start": {"line": 7, "column": 31}, "end": {"line": 7, "column": 33}}], "line": 7}, "1": {"loc": {"start": {"line": 7, "column": 35}, "end": {"line": 7, "column": 59}}, "type": "default-arg", "locations": [{"start": {"line": 7, "column": 57}, "end": {"line": 7, "column": 59}}], "line": 7}, "2": {"loc": {"start": {"line": 7, "column": 61}, "end": {"line": 7, "column": 84}}, "type": "default-arg", "locations": [{"start": {"line": 7, "column": 79}, "end": {"line": 7, "column": 84}}], "line": 7}, "3": {"loc": {"start": {"line": 8, "column": 4}, "end": {"line": 8, "column": 25}}, "type": "default-arg", "locations": [{"start": {"line": 8, "column": 23}, "end": {"line": 8, "column": 25}}], "line": 8}, "4": {"loc": {"start": {"line": 117, "column": 14}, "end": {"line": 117, "column": 39}}, "type": "default-arg", "locations": [{"start": {"line": 117, "column": 34}, "end": {"line": 117, "column": 39}}], "line": 117}, "5": {"loc": {"start": {"line": 117, "column": 41}, "end": {"line": 117, "column": 59}}, "type": "default-arg", "locations": [{"start": {"line": 117, "column": 57}, "end": {"line": 117, "column": 59}}], "line": 117}, "6": {"loc": {"start": {"line": 132, "column": 14}, "end": {"line": 132, "column": 31}}, "type": "default-arg", "locations": [{"start": {"line": 132, "column": 29}, "end": {"line": 132, "column": 31}}], "line": 132}, "7": {"loc": {"start": {"line": 132, "column": 33}, "end": {"line": 132, "column": 54}}, "type": "default-arg", "locations": [{"start": {"line": 132, "column": 52}, "end": {"line": 132, "column": 54}}], "line": 132}, "8": {"loc": {"start": {"line": 132, "column": 56}, "end": {"line": 132, "column": 74}}, "type": "default-arg", "locations": [{"start": {"line": 132, "column": 72}, "end": {"line": 132, "column": 74}}], "line": 132}, "9": {"loc": {"start": {"line": 133, "column": 4}, "end": {"line": 133, "column": 32}}, "type": "default-arg", "locations": [{"start": {"line": 133, "column": 27}, "end": {"line": 133, "column": 32}}], "line": 133}, "10": {"loc": {"start": {"line": 133, "column": 34}, "end": {"line": 133, "column": 71}}, "type": "default-arg", "locations": [{"start": {"line": 133, "column": 55}, "end": {"line": 133, "column": 71}}], "line": 133}, "11": {"loc": {"start": {"line": 134, "column": 4}, "end": {"line": 134, "column": 23}}, "type": "default-arg", "locations": [{"start": {"line": 134, "column": 21}, "end": {"line": 134, "column": 23}}], "line": 134}, "12": {"loc": {"start": {"line": 134, "column": 25}, "end": {"line": 134, "column": 68}}, "type": "default-arg", "locations": [{"start": {"line": 134, "column": 50}, "end": {"line": 134, "column": 68}}], "line": 134}, "13": {"loc": {"start": {"line": 158, "column": 14}, "end": {"line": 158, "column": 54}}, "type": "default-arg", "locations": [{"start": {"line": 158, "column": 37}, "end": {"line": 158, "column": 54}}], "line": 158}, "14": {"loc": {"start": {"line": 158, "column": 56}, "end": {"line": 158, "column": 82}}, "type": "default-arg", "locations": [{"start": {"line": 158, "column": 77}, "end": {"line": 158, "column": 82}}], "line": 158}, "15": {"loc": {"start": {"line": 171, "column": 14}, "end": {"line": 171, "column": 54}}, "type": "default-arg", "locations": [{"start": {"line": 171, "column": 37}, "end": {"line": 171, "column": 54}}], "line": 171}, "16": {"loc": {"start": {"line": 174, "column": 4}, "end": {"line": 214, "column": 5}}, "type": "if", "locations": [{"start": {"line": 174, "column": 4}, "end": {"line": 214, "column": 5}}, {"start": {"line": 184, "column": 11}, "end": {"line": 214, "column": 5}}], "line": 174}, "17": {"loc": {"start": {"line": 184, "column": 11}, "end": {"line": 214, "column": 5}}, "type": "if", "locations": [{"start": {"line": 184, "column": 11}, "end": {"line": 214, "column": 5}}, {"start": {"line": 190, "column": 11}, "end": {"line": 214, "column": 5}}], "line": 184}, "18": {"loc": {"start": {"line": 190, "column": 11}, "end": {"line": 214, "column": 5}}, "type": "if", "locations": [{"start": {"line": 190, "column": 11}, "end": {"line": 214, "column": 5}}, {"start": {"line": 196, "column": 11}, "end": {"line": 214, "column": 5}}], "line": 190}, "19": {"loc": {"start": {"line": 196, "column": 11}, "end": {"line": 214, "column": 5}}, "type": "if", "locations": [{"start": {"line": 196, "column": 11}, "end": {"line": 214, "column": 5}}, {"start": {"line": 203, "column": 11}, "end": {"line": 214, "column": 5}}], "line": 196}, "20": {"loc": {"start": {"line": 203, "column": 11}, "end": {"line": 214, "column": 5}}, "type": "if", "locations": [{"start": {"line": 203, "column": 11}, "end": {"line": 214, "column": 5}}, {"start": {"line": 209, "column": 11}, "end": {"line": 214, "column": 5}}], "line": 203}}, "s": {"0": 1, "1": 1, "2": 1, "3": 1, "4": 1, "5": 1, "6": 1, "7": 1, "8": 64, "9": 64, "10": 64, "11": 64, "12": 60, "13": 60, "14": 60, "15": 60, "16": 15, "17": 15, "18": 15, "19": 15, "20": 15, "21": 15, "22": 15, "23": 15, "24": 15, "25": 15, "26": 15, "27": 15, "28": 15, "29": 15, "30": 184, "31": 184, "32": 123, "33": 123, "34": 123, "35": 1, "36": 1, "37": 1, "38": 1, "39": 122, "40": 1, "41": 1, "42": 1, "43": 1, "44": 121, "45": 3, "46": 3, "47": 3, "48": 3, "49": 118, "50": 1, "51": 1, "52": 1, "53": 1, "54": 117, "55": 1, "56": 1, "57": 1, "58": 1, "59": 116, "60": 116, "61": 116, "62": 116}, "f": {"0": 1, "1": 64, "2": 60, "3": 15, "4": 184, "5": 123}, "b": {"0": [0], "1": [0], "2": [0], "3": [0], "4": [59], "5": [59], "6": [15], "7": [15], "8": [15], "9": [15], "10": [15], "11": [15], "12": [15], "13": [184], "14": [184], "15": [116], "16": [1, 122], "17": [1, 121], "18": [3, 118], "19": [1, 117], "20": [1, 116]}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "9ad0706e91781c7d325b479851a28d24e3cbf0a4"}, "/Users/<USER>/Documents/PAASApplications/premium.audit.advisory.service.mobile/app/modals/options.tsx": {"path": "/Users/<USER>/Documents/PAASApplications/premium.audit.advisory.service.mobile/app/modals/options.tsx", "statementMap": {"0": {"start": {"line": 18, "column": 38}, "end": {"line": 18, "column": 64}}, "1": {"start": {"line": 20, "column": 22}, "end": {"line": 22, "column": 3}}, "2": {"start": {"line": 21, "column": 4}, "end": {"line": 21, "column": 89}}, "3": {"start": {"line": 21, "column": 32}, "end": {"line": 21, "column": 86}}, "4": {"start": {"line": 24, "column": 22}, "end": {"line": 28, "column": 3}}, "5": {"start": {"line": 25, "column": 4}, "end": {"line": 25, "column": 69}}, "6": {"start": {"line": 25, "column": 32}, "end": {"line": 25, "column": 66}}, "7": {"start": {"line": 26, "column": 4}, "end": {"line": 26, "column": 48}}, "8": {"start": {"line": 26, "column": 30}, "end": {"line": 26, "column": 48}}, "9": {"start": {"line": 27, "column": 4}, "end": {"line": 27, "column": 91}}, "10": {"start": {"line": 27, "column": 73}, "end": {"line": 27, "column": 91}}, "11": {"start": {"line": 30, "column": 2}, "end": {"line": 76, "column": 4}}, "12": {"start": {"line": 39, "column": 29}, "end": {"line": 39, "column": 56}}, "13": {"start": {"line": 43, "column": 29}, "end": {"line": 43, "column": 56}}, "14": {"start": {"line": 48, "column": 37}, "end": {"line": 48, "column": 66}}, "15": {"start": {"line": 58, "column": 37}, "end": {"line": 58, "column": 66}}, "16": {"start": {"line": 73, "column": 43}, "end": {"line": 73, "column": 64}}}, "fnMap": {"0": {"name": "Options", "decl": {"start": {"line": 11, "column": 16}, "end": {"line": 11, "column": 23}}, "loc": {"start": {"line": 17, "column": 3}, "end": {"line": 77, "column": 1}}, "line": 17}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 20, "column": 22}, "end": {"line": 20, "column": 23}}, "loc": {"start": {"line": 20, "column": 48}, "end": {"line": 22, "column": 3}}, "line": 20}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 21, "column": 18}, "end": {"line": 21, "column": 19}}, "loc": {"start": {"line": 21, "column": 32}, "end": {"line": 21, "column": 86}}, "line": 21}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 24, "column": 22}, "end": {"line": 24, "column": 23}}, "loc": {"start": {"line": 24, "column": 46}, "end": {"line": 28, "column": 3}}, "line": 24}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 25, "column": 18}, "end": {"line": 25, "column": 19}}, "loc": {"start": {"line": 25, "column": 32}, "end": {"line": 25, "column": 66}}, "line": 25}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 39, "column": 23}, "end": {"line": 39, "column": 24}}, "loc": {"start": {"line": 39, "column": 29}, "end": {"line": 39, "column": 56}}, "line": 39}, "6": {"name": "(anonymous_6)", "decl": {"start": {"line": 43, "column": 23}, "end": {"line": 43, "column": 24}}, "loc": {"start": {"line": 43, "column": 29}, "end": {"line": 43, "column": 56}}, "line": 43}, "7": {"name": "(anonymous_7)", "decl": {"start": {"line": 48, "column": 31}, "end": {"line": 48, "column": 32}}, "loc": {"start": {"line": 48, "column": 35}, "end": {"line": 48, "column": 68}}, "line": 48}, "8": {"name": "(anonymous_8)", "decl": {"start": {"line": 58, "column": 31}, "end": {"line": 58, "column": 32}}, "loc": {"start": {"line": 58, "column": 35}, "end": {"line": 58, "column": 68}}, "line": 58}, "9": {"name": "(anonymous_9)", "decl": {"start": {"line": 73, "column": 19}, "end": {"line": 73, "column": 20}}, "loc": {"start": {"line": 73, "column": 43}, "end": {"line": 73, "column": 64}}, "line": 73}}, "branchMap": {"0": {"loc": {"start": {"line": 26, "column": 4}, "end": {"line": 26, "column": 48}}, "type": "if", "locations": [{"start": {"line": 26, "column": 4}, "end": {"line": 26, "column": 48}}, {"start": {}, "end": {}}], "line": 26}, "1": {"loc": {"start": {"line": 27, "column": 4}, "end": {"line": 27, "column": 91}}, "type": "if", "locations": [{"start": {"line": 27, "column": 4}, "end": {"line": 27, "column": 91}}, {"start": {}, "end": {}}], "line": 27}, "2": {"loc": {"start": {"line": 27, "column": 8}, "end": {"line": 27, "column": 71}}, "type": "binary-expr", "locations": [{"start": {"line": 27, "column": 8}, "end": {"line": 27, "column": 46}}, {"start": {"line": 27, "column": 50}, "end": {"line": 27, "column": 71}}], "line": 27}}, "s": {"0": 38, "1": 38, "2": 5, "3": 5, "4": 38, "5": 5, "6": 5, "7": 5, "8": 2, "9": 5, "10": 2, "11": 38, "12": 1, "13": 1, "14": 3, "15": 2, "16": 3}, "f": {"0": 38, "1": 5, "2": 5, "3": 5, "4": 5, "5": 1, "6": 1, "7": 3, "8": 2, "9": 3}, "b": {"0": [2, 3], "1": [2, 3], "2": [5, 2]}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "3d9a39bac616c9d885637b107c9e50290a137f2a"}, "/Users/<USER>/Documents/PAASApplications/premium.audit.advisory.service.mobile/app/modals/popup.tsx": {"path": "/Users/<USER>/Documents/PAASApplications/premium.audit.advisory.service.mobile/app/modals/popup.tsx", "statementMap": {"0": {"start": {"line": 27, "column": 20}, "end": {"line": 27, "column": 59}}, "1": {"start": {"line": 28, "column": 36}, "end": {"line": 28, "column": 61}}, "2": {"start": {"line": 29, "column": 38}, "end": {"line": 29, "column": 50}}, "3": {"start": {"line": 30, "column": 34}, "end": {"line": 30, "column": 47}}, "4": {"start": {"line": 31, "column": 39}, "end": {"line": 31, "column": 51}}, "5": {"start": {"line": 33, "column": 23}, "end": {"line": 52, "column": 9}}, "6": {"start": {"line": 33, "column": 38}, "end": {"line": 52, "column": 3}}, "7": {"start": {"line": 54, "column": 2}, "end": {"line": 60, "column": 18}}, "8": {"start": {"line": 55, "column": 4}, "end": {"line": 59, "column": 5}}, "9": {"start": {"line": 56, "column": 6}, "end": {"line": 56, "column": 24}}, "10": {"start": {"line": 57, "column": 6}, "end": {"line": 57, "column": 20}}, "11": {"start": {"line": 58, "column": 6}, "end": {"line": 58, "column": 22}}, "12": {"start": {"line": 63, "column": 2}, "end": {"line": 65, "column": 20}}, "13": {"start": {"line": 64, "column": 4}, "end": {"line": 64, "column": 45}}, "14": {"start": {"line": 64, "column": 29}, "end": {"line": 64, "column": 45}}, "15": {"start": {"line": 68, "column": 2}, "end": {"line": 71, "column": 18}}, "16": {"start": {"line": 69, "column": 4}, "end": {"line": 69, "column": 22}}, "17": {"start": {"line": 70, "column": 4}, "end": {"line": 70, "column": 80}}, "18": {"start": {"line": 74, "column": 2}, "end": {"line": 82, "column": 18}}, "19": {"start": {"line": 75, "column": 4}, "end": {"line": 81, "column": 5}}, "20": {"start": {"line": 76, "column": 6}, "end": {"line": 80, "column": 17}}, "21": {"start": {"line": 85, "column": 22}, "end": {"line": 94, "column": 3}}, "22": {"start": {"line": 86, "column": 4}, "end": {"line": 93, "column": 7}}, "23": {"start": {"line": 91, "column": 6}, "end": {"line": 91, "column": 30}}, "24": {"start": {"line": 92, "column": 6}, "end": {"line": 92, "column": 40}}, "25": {"start": {"line": 96, "column": 22}, "end": {"line": 118, "column": 3}}, "26": {"start": {"line": 97, "column": 24}, "end": {"line": 108, "column": 5}}, "27": {"start": {"line": 98, "column": 6}, "end": {"line": 107, "column": 7}}, "28": {"start": {"line": 100, "column": 10}, "end": {"line": 100, "column": 33}}, "29": {"start": {"line": 102, "column": 10}, "end": {"line": 102, "column": 33}}, "30": {"start": {"line": 104, "column": 10}, "end": {"line": 104, "column": 35}}, "31": {"start": {"line": 106, "column": 10}, "end": {"line": 106, "column": 34}}, "32": {"start": {"line": 109, "column": 4}, "end": {"line": 117, "column": 8}}, "33": {"start": {"line": 120, "column": 21}, "end": {"line": 129, "column": 3}}, "34": {"start": {"line": 121, "column": 3}, "end": {"line": 128, "column": 5}}, "35": {"start": {"line": 124, "column": 6}, "end": {"line": 124, "column": 18}}, "36": {"start": {"line": 125, "column": 11}, "end": {"line": 128, "column": 5}}, "37": {"start": {"line": 127, "column": 6}, "end": {"line": 127, "column": 18}}, "38": {"start": {"line": 132, "column": 28}, "end": {"line": 136, "column": 3}}, "39": {"start": {"line": 133, "column": 4}, "end": {"line": 135, "column": 10}}, "40": {"start": {"line": 138, "column": 2}, "end": {"line": 279, "column": 4}}, "41": {"start": {"line": 150, "column": 25}, "end": {"line": 150, "column": 43}}, "42": {"start": {"line": 158, "column": 58}, "end": {"line": 158, "column": 76}}, "43": {"start": {"line": 178, "column": 43}, "end": {"line": 180, "column": 41}}, "44": {"start": {"line": 209, "column": 49}, "end": {"line": 211, "column": 47}}, "45": {"start": {"line": 239, "column": 33}, "end": {"line": 239, "column": 50}}, "46": {"start": {"line": 254, "column": 33}, "end": {"line": 254, "column": 51}}, "47": {"start": {"line": 268, "column": 35}, "end": {"line": 268, "column": 53}}}, "fnMap": {"0": {"name": "PopUp", "decl": {"start": {"line": 16, "column": 16}, "end": {"line": 16, "column": 21}}, "loc": {"start": {"line": 26, "column": 3}, "end": {"line": 280, "column": 1}}, "line": 26}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 33, "column": 31}, "end": {"line": 33, "column": 32}}, "loc": {"start": {"line": 33, "column": 38}, "end": {"line": 52, "column": 3}}, "line": 33}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 54, "column": 12}, "end": {"line": 54, "column": 13}}, "loc": {"start": {"line": 54, "column": 18}, "end": {"line": 60, "column": 3}}, "line": 54}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 63, "column": 12}, "end": {"line": 63, "column": 13}}, "loc": {"start": {"line": 63, "column": 18}, "end": {"line": 65, "column": 3}}, "line": 63}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 68, "column": 12}, "end": {"line": 68, "column": 13}}, "loc": {"start": {"line": 68, "column": 18}, "end": {"line": 71, "column": 3}}, "line": 68}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 74, "column": 12}, "end": {"line": 74, "column": 13}}, "loc": {"start": {"line": 74, "column": 18}, "end": {"line": 82, "column": 3}}, "line": 74}, "6": {"name": "(anonymous_6)", "decl": {"start": {"line": 85, "column": 22}, "end": {"line": 85, "column": 23}}, "loc": {"start": {"line": 85, "column": 48}, "end": {"line": 94, "column": 3}}, "line": 85}, "7": {"name": "(anonymous_7)", "decl": {"start": {"line": 90, "column": 13}, "end": {"line": 90, "column": 14}}, "loc": {"start": {"line": 90, "column": 19}, "end": {"line": 93, "column": 5}}, "line": 90}, "8": {"name": "(anonymous_8)", "decl": {"start": {"line": 96, "column": 22}, "end": {"line": 96, "column": 23}}, "loc": {"start": {"line": 96, "column": 48}, "end": {"line": 118, "column": 3}}, "line": 96}, "9": {"name": "(anonymous_9)", "decl": {"start": {"line": 97, "column": 24}, "end": {"line": 97, "column": 25}}, "loc": {"start": {"line": 97, "column": 30}, "end": {"line": 108, "column": 5}}, "line": 97}, "10": {"name": "(anonymous_10)", "decl": {"start": {"line": 120, "column": 21}, "end": {"line": 120, "column": 22}}, "loc": {"start": {"line": 120, "column": 27}, "end": {"line": 129, "column": 3}}, "line": 120}, "11": {"name": "(anonymous_11)", "decl": {"start": {"line": 132, "column": 28}, "end": {"line": 132, "column": 29}}, "loc": {"start": {"line": 132, "column": 33}, "end": {"line": 136, "column": 3}}, "line": 132}, "12": {"name": "(anonymous_12)", "decl": {"start": {"line": 150, "column": 19}, "end": {"line": 150, "column": 20}}, "loc": {"start": {"line": 150, "column": 25}, "end": {"line": 150, "column": 43}}, "line": 150}, "13": {"name": "(anonymous_13)", "decl": {"start": {"line": 158, "column": 52}, "end": {"line": 158, "column": 53}}, "loc": {"start": {"line": 158, "column": 58}, "end": {"line": 158, "column": 76}}, "line": 158}, "14": {"name": "(anonymous_14)", "decl": {"start": {"line": 178, "column": 37}, "end": {"line": 178, "column": 38}}, "loc": {"start": {"line": 178, "column": 43}, "end": {"line": 180, "column": 41}}, "line": 178}, "15": {"name": "(anonymous_15)", "decl": {"start": {"line": 179, "column": 24}, "end": {"line": 179, "column": 25}}, "loc": {"start": {"line": 179, "column": 30}, "end": {"line": 179, "column": 32}}, "line": 179}, "16": {"name": "(anonymous_16)", "decl": {"start": {"line": 209, "column": 43}, "end": {"line": 209, "column": 44}}, "loc": {"start": {"line": 209, "column": 49}, "end": {"line": 211, "column": 47}}, "line": 209}, "17": {"name": "(anonymous_17)", "decl": {"start": {"line": 210, "column": 30}, "end": {"line": 210, "column": 31}}, "loc": {"start": {"line": 210, "column": 36}, "end": {"line": 210, "column": 38}}, "line": 210}, "18": {"name": "(anonymous_18)", "decl": {"start": {"line": 239, "column": 27}, "end": {"line": 239, "column": 28}}, "loc": {"start": {"line": 239, "column": 33}, "end": {"line": 239, "column": 50}}, "line": 239}, "19": {"name": "(anonymous_19)", "decl": {"start": {"line": 254, "column": 27}, "end": {"line": 254, "column": 28}}, "loc": {"start": {"line": 254, "column": 33}, "end": {"line": 254, "column": 51}}, "line": 254}, "20": {"name": "(anonymous_20)", "decl": {"start": {"line": 268, "column": 29}, "end": {"line": 268, "column": 30}}, "loc": {"start": {"line": 268, "column": 35}, "end": {"line": 268, "column": 53}}, "line": 268}}, "branchMap": {"0": {"loc": {"start": {"line": 55, "column": 4}, "end": {"line": 59, "column": 5}}, "type": "if", "locations": [{"start": {"line": 55, "column": 4}, "end": {"line": 59, "column": 5}}, {"start": {}, "end": {}}], "line": 55}, "1": {"loc": {"start": {"line": 64, "column": 4}, "end": {"line": 64, "column": 45}}, "type": "if", "locations": [{"start": {"line": 64, "column": 4}, "end": {"line": 64, "column": 45}}, {"start": {}, "end": {}}], "line": 64}, "2": {"loc": {"start": {"line": 70, "column": 4}, "end": {"line": 70, "column": 79}}, "type": "binary-expr", "locations": [{"start": {"line": 70, "column": 4}, "end": {"line": 70, "column": 37}}, {"start": {"line": 70, "column": 41}, "end": {"line": 70, "column": 79}}], "line": 70}, "3": {"loc": {"start": {"line": 75, "column": 4}, "end": {"line": 81, "column": 5}}, "type": "if", "locations": [{"start": {"line": 75, "column": 4}, "end": {"line": 81, "column": 5}}, {"start": {}, "end": {}}], "line": 75}, "4": {"loc": {"start": {"line": 98, "column": 6}, "end": {"line": 107, "column": 7}}, "type": "switch", "locations": [{"start": {"line": 99, "column": 8}, "end": {"line": 100, "column": 33}}, {"start": {"line": 101, "column": 8}, "end": {"line": 102, "column": 33}}, {"start": {"line": 103, "column": 8}, "end": {"line": 104, "column": 35}}, {"start": {"line": 105, "column": 8}, "end": {"line": 106, "column": 34}}], "line": 98}, "5": {"loc": {"start": {"line": 110, "column": 12}, "end": {"line": 110, "column": 67}}, "type": "cond-expr", "locations": [{"start": {"line": 110, "column": 45}, "end": {"line": 110, "column": 62}}, {"start": {"line": 110, "column": 65}, "end": {"line": 110, "column": 67}}], "line": 110}, "6": {"loc": {"start": {"line": 111, "column": 16}, "end": {"line": 111, "column": 76}}, "type": "cond-expr", "locations": [{"start": {"line": 111, "column": 51}, "end": {"line": 111, "column": 71}}, {"start": {"line": 111, "column": 74}, "end": {"line": 111, "column": 76}}], "line": 111}, "7": {"loc": {"start": {"line": 112, "column": 13}, "end": {"line": 112, "column": 58}}, "type": "cond-expr", "locations": [{"start": {"line": 112, "column": 48}, "end": {"line": 112, "column": 53}}, {"start": {"line": 112, "column": 56}, "end": {"line": 112, "column": 58}}], "line": 112}, "8": {"loc": {"start": {"line": 113, "column": 19}, "end": {"line": 113, "column": 72}}, "type": "cond-expr", "locations": [{"start": {"line": 113, "column": 54}, "end": {"line": 113, "column": 58}}, {"start": {"line": 113, "column": 61}, "end": {"line": 113, "column": 72}}], "line": 113}, "9": {"loc": {"start": {"line": 121, "column": 3}, "end": {"line": 128, "column": 5}}, "type": "if", "locations": [{"start": {"line": 121, "column": 3}, "end": {"line": 128, "column": 5}}, {"start": {"line": 125, "column": 11}, "end": {"line": 128, "column": 5}}], "line": 121}, "10": {"loc": {"start": {"line": 121, "column": 7}, "end": {"line": 123, "column": 70}}, "type": "binary-expr", "locations": [{"start": {"line": 121, "column": 7}, "end": {"line": 121, "column": 37}}, {"start": {"line": 122, "column": 5}, "end": {"line": 122, "column": 29}}, {"start": {"line": 122, "column": 33}, "end": {"line": 122, "column": 58}}, {"start": {"line": 123, "column": 9}, "end": {"line": 123, "column": 69}}], "line": 121}, "11": {"loc": {"start": {"line": 125, "column": 11}, "end": {"line": 128, "column": 5}}, "type": "if", "locations": [{"start": {"line": 125, "column": 11}, "end": {"line": 128, "column": 5}}, {"start": {}, "end": {}}], "line": 125}, "12": {"loc": {"start": {"line": 125, "column": 15}, "end": {"line": 126, "column": 92}}, "type": "binary-expr", "locations": [{"start": {"line": 125, "column": 15}, "end": {"line": 125, "column": 47}}, {"start": {"line": 126, "column": 7}, "end": {"line": 126, "column": 26}}, {"start": {"line": 126, "column": 31}, "end": {"line": 126, "column": 58}}, {"start": {"line": 126, "column": 62}, "end": {"line": 126, "column": 90}}], "line": 125}, "13": {"loc": {"start": {"line": 133, "column": 11}, "end": {"line": 135, "column": 9}}, "type": "cond-expr", "locations": [{"start": {"line": 134, "column": 7}, "end": {"line": 134, "column": 52}}, {"start": {"line": 135, "column": 8}, "end": {"line": 135, "column": 9}}], "line": 133}, "14": {"loc": {"start": {"line": 134, "column": 7}, "end": {"line": 134, "column": 52}}, "type": "cond-expr", "locations": [{"start": {"line": 134, "column": 42}, "end": {"line": 134, "column": 45}}, {"start": {"line": 134, "column": 48}, "end": {"line": 134, "column": 52}}], "line": 134}, "15": {"loc": {"start": {"line": 144, "column": 25}, "end": {"line": 144, "column": 69}}, "type": "cond-expr", "locations": [{"start": {"line": 144, "column": 49}, "end": {"line": 144, "column": 58}}, {"start": {"line": 144, "column": 61}, "end": {"line": 144, "column": 69}}], "line": 144}, "16": {"loc": {"start": {"line": 165, "column": 13}, "end": {"line": 229, "column": 13}}, "type": "cond-expr", "locations": [{"start": {"line": 166, "column": 14}, "end": {"line": 184, "column": 21}}, {"start": {"line": 186, "column": 16}, "end": {"line": 229, "column": 13}}], "line": 165}, "17": {"loc": {"start": {"line": 171, "column": 35}, "end": {"line": 171, "column": 67}}, "type": "cond-expr", "locations": [{"start": {"line": 171, "column": 48}, "end": {"line": 171, "column": 56}}, {"start": {"line": 171, "column": 59}, "end": {"line": 171, "column": 67}}], "line": 171}, "18": {"loc": {"start": {"line": 178, "column": 43}, "end": {"line": 180, "column": 41}}, "type": "cond-expr", "locations": [{"start": {"line": 179, "column": 24}, "end": {"line": 179, "column": 32}}, {"start": {"line": 180, "column": 24}, "end": {"line": 180, "column": 41}}], "line": 178}, "19": {"loc": {"start": {"line": 178, "column": 43}, "end": {"line": 178, "column": 96}}, "type": "binary-expr", "locations": [{"start": {"line": 178, "column": 43}, "end": {"line": 178, "column": 67}}, {"start": {"line": 178, "column": 71}, "end": {"line": 178, "column": 96}}], "line": 178}, "20": {"loc": {"start": {"line": 186, "column": 16}, "end": {"line": 229, "column": 13}}, "type": "cond-expr", "locations": [{"start": {"line": 187, "column": 14}, "end": {"line": 216, "column": 41}}, {"start": {"line": 219, "column": 14}, "end": {"line": 228, "column": 27}}], "line": 186}, "21": {"loc": {"start": {"line": 201, "column": 41}, "end": {"line": 201, "column": 76}}, "type": "cond-expr", "locations": [{"start": {"line": 201, "column": 57}, "end": {"line": 201, "column": 65}}, {"start": {"line": 201, "column": 68}, "end": {"line": 201, "column": 76}}], "line": 201}, "22": {"loc": {"start": {"line": 209, "column": 49}, "end": {"line": 211, "column": 47}}, "type": "cond-expr", "locations": [{"start": {"line": 210, "column": 30}, "end": {"line": 210, "column": 38}}, {"start": {"line": 211, "column": 30}, "end": {"line": 211, "column": 47}}], "line": 209}, "23": {"loc": {"start": {"line": 209, "column": 49}, "end": {"line": 209, "column": 108}}, "type": "binary-expr", "locations": [{"start": {"line": 209, "column": 49}, "end": {"line": 209, "column": 76}}, {"start": {"line": 209, "column": 80}, "end": {"line": 209, "column": 108}}], "line": 209}, "24": {"loc": {"start": {"line": 222, "column": 22}, "end": {"line": 224, "column": 40}}, "type": "cond-expr", "locations": [{"start": {"line": 223, "column": 24}, "end": {"line": 223, "column": 50}}, {"start": {"line": 224, "column": 24}, "end": {"line": 224, "column": 40}}], "line": 222}, "25": {"loc": {"start": {"line": 232, "column": 11}, "end": {"line": 274, "column": 13}}, "type": "cond-expr", "locations": [{"start": {"line": 233, "column": 10}, "end": {"line": 261, "column": 39}}, {"start": {"line": 264, "column": 14}, "end": {"line": 273, "column": 41}}], "line": 232}, "26": {"loc": {"start": {"line": 242, "column": 20}, "end": {"line": 242, "column": 62}}, "type": "binary-expr", "locations": [{"start": {"line": 242, "column": 20}, "end": {"line": 242, "column": 32}}, {"start": {"line": 242, "column": 36}, "end": {"line": 242, "column": 62}}], "line": 242}, "27": {"loc": {"start": {"line": 246, "column": 28}, "end": {"line": 246, "column": 93}}, "type": "cond-expr", "locations": [{"start": {"line": 246, "column": 43}, "end": {"line": 246, "column": 67}}, {"start": {"line": 246, "column": 70}, "end": {"line": 246, "column": 93}}], "line": 246}, "28": {"loc": {"start": {"line": 257, "column": 21}, "end": {"line": 257, "column": 73}}, "type": "cond-expr", "locations": [{"start": {"line": 257, "column": 56}, "end": {"line": 257, "column": 62}}, {"start": {"line": 257, "column": 65}, "end": {"line": 257, "column": 73}}], "line": 257}}, "s": {"0": 116, "1": 116, "2": 116, "3": 116, "4": 116, "5": 116, "6": 21, "7": 116, "8": 37, "9": 27, "10": 27, "11": 27, "12": 116, "13": 24, "14": 23, "15": 116, "16": 26, "17": 26, "18": 116, "19": 37, "20": 10, "21": 116, "22": 8, "23": 8, "24": 8, "25": 116, "26": 8, "27": 8, "28": 1, "29": 2, "30": 1, "31": 4, "32": 8, "33": 116, "34": 54, "35": 18, "36": 36, "37": 3, "38": 116, "39": 116, "40": 116, "41": 2, "42": 1, "43": 0, "44": 0, "45": 3, "46": 1, "47": 1}, "f": {"0": 116, "1": 21, "2": 37, "3": 24, "4": 26, "5": 37, "6": 8, "7": 8, "8": 8, "9": 8, "10": 54, "11": 116, "12": 2, "13": 1, "14": 0, "15": 0, "16": 0, "17": 0, "18": 3, "19": 1, "20": 1}, "b": {"0": [27, 10], "1": [23, 1], "2": [26, 7], "3": [10, 27], "4": [1, 2, 1, 4], "5": [1, 7], "6": [1, 7], "7": [1, 7], "8": [1, 7], "9": [18, 36], "10": [54, 21, 3, 3], "11": [3, 33], "12": [36, 24, 9, 6], "13": [116, 0], "14": [9, 107], "15": [116, 0], "16": [10, 106], "17": [2, 8], "18": [0, 0], "19": [0, 0], "20": [9, 97], "21": [2, 7], "22": [0, 0], "23": [0, 0], "24": [5, 92], "25": [18, 98], "26": [18, 7], "27": [7, 11], "28": [8, 10]}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "d6b312249329babbfdf7ba59f7701f84fd67d1af"}, "/Users/<USER>/Documents/PAASApplications/premium.audit.advisory.service.mobile/app/modals/sidebar.tsx": {"path": "/Users/<USER>/Documents/PAASApplications/premium.audit.advisory.service.mobile/app/modals/sidebar.tsx", "statementMap": {"0": {"start": {"line": 13, "column": 18}, "end": {"line": 18, "column": 1}}, "1": {"start": {"line": 14, "column": 2}, "end": {"line": 17, "column": 5}}, "2": {"start": {"line": 29, "column": 50}, "end": {"line": 29, "column": 68}}, "3": {"start": {"line": 30, "column": 38}, "end": {"line": 30, "column": 50}}, "4": {"start": {"line": 31, "column": 48}, "end": {"line": 31, "column": 63}}, "5": {"start": {"line": 32, "column": 20}, "end": {"line": 32, "column": 60}}, "6": {"start": {"line": 33, "column": 24}, "end": {"line": 33, "column": 48}}, "7": {"start": {"line": 34, "column": 38}, "end": {"line": 34, "column": 64}}, "8": {"start": {"line": 36, "column": 2}, "end": {"line": 38, "column": 17}}, "9": {"start": {"line": 37, "column": 4}, "end": {"line": 37, "column": 34}}, "10": {"start": {"line": 41, "column": 2}, "end": {"line": 52, "column": 18}}, "11": {"start": {"line": 42, "column": 4}, "end": {"line": 51, "column": 5}}, "12": {"start": {"line": 43, "column": 6}, "end": {"line": 47, "column": 17}}, "13": {"start": {"line": 49, "column": 6}, "end": {"line": 49, "column": 24}}, "14": {"start": {"line": 50, "column": 6}, "end": {"line": 50, "column": 36}}, "15": {"start": {"line": 55, "column": 22}, "end": {"line": 65, "column": 3}}, "16": {"start": {"line": 56, "column": 4}, "end": {"line": 64, "column": 7}}, "17": {"start": {"line": 61, "column": 6}, "end": {"line": 61, "column": 31}}, "18": {"start": {"line": 62, "column": 6}, "end": {"line": 62, "column": 31}}, "19": {"start": {"line": 63, "column": 6}, "end": {"line": 63, "column": 27}}, "20": {"start": {"line": 68, "column": 29}, "end": {"line": 71, "column": 3}}, "21": {"start": {"line": 69, "column": 4}, "end": {"line": 69, "column": 62}}, "22": {"start": {"line": 70, "column": 4}, "end": {"line": 70, "column": 28}}, "23": {"start": {"line": 73, "column": 23}, "end": {"line": 80, "column": 3}}, "24": {"start": {"line": 74, "column": 24}, "end": {"line": 74, "column": 59}}, "25": {"start": {"line": 75, "column": 25}, "end": {"line": 78, "column": 6}}, "26": {"start": {"line": 76, "column": 20}, "end": {"line": 76, "column": 52}}, "27": {"start": {"line": 77, "column": 6}, "end": {"line": 77, "column": 61}}, "28": {"start": {"line": 77, "column": 39}, "end": {"line": 77, "column": 59}}, "29": {"start": {"line": 79, "column": 4}, "end": {"line": 79, "column": 70}}, "30": {"start": {"line": 82, "column": 20}, "end": {"line": 88, "column": 3}}, "31": {"start": {"line": 83, "column": 25}, "end": {"line": 86, "column": 38}}, "32": {"start": {"line": 87, "column": 4}, "end": {"line": 87, "column": 29}}, "33": {"start": {"line": 90, "column": 29}, "end": {"line": 96, "column": 3}}, "34": {"start": {"line": 91, "column": 4}, "end": {"line": 91, "column": 29}}, "35": {"start": {"line": 93, "column": 4}, "end": {"line": 95, "column": 5}}, "36": {"start": {"line": 94, "column": 6}, "end": {"line": 94, "column": 28}}, "37": {"start": {"line": 99, "column": 20}, "end": {"line": 101, "column": 1}}, "38": {"start": {"line": 100, "column": 2}, "end": {"line": 100, "column": 76}}, "39": {"start": {"line": 103, "column": 27}, "end": {"line": 106, "column": 1}}, "40": {"start": {"line": 104, "column": 2}, "end": {"line": 104, "column": 45}}, "41": {"start": {"line": 104, "column": 38}, "end": {"line": 104, "column": 45}}, "42": {"start": {"line": 105, "column": 2}, "end": {"line": 105, "column": 23}}, "43": {"start": {"line": 108, "column": 27}, "end": {"line": 158, "column": 3}}, "44": {"start": {"line": 109, "column": 4}, "end": {"line": 157, "column": 5}}, "45": {"start": {"line": 111, "column": 35}, "end": {"line": 112, "column": 54}}, "46": {"start": {"line": 112, "column": 32}, "end": {"line": 112, "column": 53}}, "47": {"start": {"line": 113, "column": 35}, "end": {"line": 119, "column": 10}}, "48": {"start": {"line": 115, "column": 24}, "end": {"line": 115, "column": 34}}, "49": {"start": {"line": 116, "column": 35}, "end": {"line": 116, "column": 62}}, "50": {"start": {"line": 117, "column": 10}, "end": {"line": 118, "column": 84}}, "51": {"start": {"line": 120, "column": 8}, "end": {"line": 123, "column": 9}}, "52": {"start": {"line": 121, "column": 10}, "end": {"line": 122, "column": 61}}, "53": {"start": {"line": 122, "column": 36}, "end": {"line": 122, "column": 58}}, "54": {"start": {"line": 124, "column": 8}, "end": {"line": 124, "column": 106}}, "55": {"start": {"line": 124, "column": 49}, "end": {"line": 124, "column": 104}}, "56": {"start": {"line": 127, "column": 38}, "end": {"line": 134, "column": 10}}, "57": {"start": {"line": 128, "column": 24}, "end": {"line": 128, "column": 34}}, "58": {"start": {"line": 129, "column": 35}, "end": {"line": 129, "column": 62}}, "59": {"start": {"line": 130, "column": 27}, "end": {"line": 130, "column": 81}}, "60": {"start": {"line": 131, "column": 27}, "end": {"line": 131, "column": 70}}, "61": {"start": {"line": 132, "column": 10}, "end": {"line": 133, "column": 58}}, "62": {"start": {"line": 135, "column": 8}, "end": {"line": 135, "column": 109}}, "63": {"start": {"line": 135, "column": 52}, "end": {"line": 135, "column": 107}}, "64": {"start": {"line": 138, "column": 39}, "end": {"line": 144, "column": 10}}, "65": {"start": {"line": 139, "column": 24}, "end": {"line": 139, "column": 34}}, "66": {"start": {"line": 140, "column": 35}, "end": {"line": 140, "column": 62}}, "67": {"start": {"line": 141, "column": 27}, "end": {"line": 141, "column": 81}}, "68": {"start": {"line": 142, "column": 27}, "end": {"line": 142, "column": 70}}, "69": {"start": {"line": 143, "column": 10}, "end": {"line": 143, "column": 74}}, "70": {"start": {"line": 145, "column": 8}, "end": {"line": 145, "column": 110}}, "71": {"start": {"line": 145, "column": 53}, "end": {"line": 145, "column": 108}}, "72": {"start": {"line": 148, "column": 39}, "end": {"line": 154, "column": 10}}, "73": {"start": {"line": 149, "column": 24}, "end": {"line": 149, "column": 34}}, "74": {"start": {"line": 150, "column": 35}, "end": {"line": 150, "column": 62}}, "75": {"start": {"line": 151, "column": 27}, "end": {"line": 151, "column": 81}}, "76": {"start": {"line": 152, "column": 27}, "end": {"line": 152, "column": 70}}, "77": {"start": {"line": 153, "column": 10}, "end": {"line": 153, "column": 57}}, "78": {"start": {"line": 155, "column": 8}, "end": {"line": 155, "column": 110}}, "79": {"start": {"line": 155, "column": 53}, "end": {"line": 155, "column": 108}}, "80": {"start": {"line": 160, "column": 21}, "end": {"line": 188, "column": 3}}, "81": {"start": {"line": 161, "column": 4}, "end": {"line": 187, "column": 11}}, "82": {"start": {"line": 167, "column": 22}, "end": {"line": 167, "column": 60}}, "83": {"start": {"line": 180, "column": 25}, "end": {"line": 180, "column": 45}}, "84": {"start": {"line": 190, "column": 2}, "end": {"line": 312, "column": 4}}, "85": {"start": {"line": 195, "column": 28}, "end": {"line": 195, "column": 55}}, "86": {"start": {"line": 202, "column": 23}, "end": {"line": 202, "column": 50}}, "87": {"start": {"line": 226, "column": 31}, "end": {"line": 226, "column": 45}}, "88": {"start": {"line": 234, "column": 35}, "end": {"line": 234, "column": 63}}, "89": {"start": {"line": 240, "column": 14}, "end": {"line": 247, "column": 21}}, "90": {"start": {"line": 251, "column": 14}, "end": {"line": 258, "column": 21}}, "91": {"start": {"line": 262, "column": 14}, "end": {"line": 269, "column": 21}}, "92": {"start": {"line": 273, "column": 14}, "end": {"line": 280, "column": 21}}, "93": {"start": {"line": 288, "column": 31}, "end": {"line": 288, "column": 62}}, "94": {"start": {"line": 295, "column": 31}, "end": {"line": 295, "column": 64}}, "95": {"start": {"line": 308, "column": 37}, "end": {"line": 309, "column": 42}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 13, "column": 18}, "end": {"line": 13, "column": 19}}, "loc": {"start": {"line": 13, "column": 30}, "end": {"line": 18, "column": 1}}, "line": 13}, "1": {"name": "Sidebar", "decl": {"start": {"line": 20, "column": 16}, "end": {"line": 20, "column": 23}}, "loc": {"start": {"line": 28, "column": 3}, "end": {"line": 313, "column": 1}}, "line": 28}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 36, "column": 12}, "end": {"line": 36, "column": 13}}, "loc": {"start": {"line": 36, "column": 18}, "end": {"line": 38, "column": 3}}, "line": 36}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 41, "column": 12}, "end": {"line": 41, "column": 13}}, "loc": {"start": {"line": 41, "column": 18}, "end": {"line": 52, "column": 3}}, "line": 41}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 55, "column": 22}, "end": {"line": 55, "column": 23}}, "loc": {"start": {"line": 55, "column": 46}, "end": {"line": 65, "column": 3}}, "line": 55}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 60, "column": 13}, "end": {"line": 60, "column": 14}}, "loc": {"start": {"line": 60, "column": 19}, "end": {"line": 64, "column": 5}}, "line": 60}, "6": {"name": "(anonymous_6)", "decl": {"start": {"line": 68, "column": 29}, "end": {"line": 68, "column": 30}}, "loc": {"start": {"line": 68, "column": 35}, "end": {"line": 71, "column": 3}}, "line": 68}, "7": {"name": "(anonymous_7)", "decl": {"start": {"line": 73, "column": 23}, "end": {"line": 73, "column": 24}}, "loc": {"start": {"line": 73, "column": 29}, "end": {"line": 80, "column": 3}}, "line": 73}, "8": {"name": "(anonymous_8)", "decl": {"start": {"line": 75, "column": 55}, "end": {"line": 75, "column": 56}}, "loc": {"start": {"line": 75, "column": 71}, "end": {"line": 78, "column": 5}}, "line": 75}, "9": {"name": "(anonymous_9)", "decl": {"start": {"line": 77, "column": 31}, "end": {"line": 77, "column": 32}}, "loc": {"start": {"line": 77, "column": 39}, "end": {"line": 77, "column": 59}}, "line": 77}, "10": {"name": "(anonymous_10)", "decl": {"start": {"line": 82, "column": 20}, "end": {"line": 82, "column": 21}}, "loc": {"start": {"line": 82, "column": 48}, "end": {"line": 88, "column": 3}}, "line": 82}, "11": {"name": "(anonymous_11)", "decl": {"start": {"line": 90, "column": 29}, "end": {"line": 90, "column": 30}}, "loc": {"start": {"line": 90, "column": 53}, "end": {"line": 96, "column": 3}}, "line": 90}, "12": {"name": "(anonymous_12)", "decl": {"start": {"line": 99, "column": 20}, "end": {"line": 99, "column": 21}}, "loc": {"start": {"line": 99, "column": 46}, "end": {"line": 101, "column": 1}}, "line": 99}, "13": {"name": "(anonymous_13)", "decl": {"start": {"line": 103, "column": 27}, "end": {"line": 103, "column": 28}}, "loc": {"start": {"line": 103, "column": 51}, "end": {"line": 106, "column": 1}}, "line": 103}, "14": {"name": "(anonymous_14)", "decl": {"start": {"line": 108, "column": 27}, "end": {"line": 108, "column": 28}}, "loc": {"start": {"line": 108, "column": 49}, "end": {"line": 158, "column": 3}}, "line": 108}, "15": {"name": "(anonymous_15)", "decl": {"start": {"line": 112, "column": 16}, "end": {"line": 112, "column": 17}}, "loc": {"start": {"line": 112, "column": 32}, "end": {"line": 112, "column": 53}}, "line": 112}, "16": {"name": "(anonymous_16)", "decl": {"start": {"line": 114, "column": 18}, "end": {"line": 114, "column": 19}}, "loc": {"start": {"line": 114, "column": 34}, "end": {"line": 119, "column": 9}}, "line": 114}, "17": {"name": "(anonymous_17)", "decl": {"start": {"line": 122, "column": 20}, "end": {"line": 122, "column": 21}}, "loc": {"start": {"line": 122, "column": 36}, "end": {"line": 122, "column": 58}}, "line": 122}, "18": {"name": "(anonymous_18)", "decl": {"start": {"line": 124, "column": 39}, "end": {"line": 124, "column": 40}}, "loc": {"start": {"line": 124, "column": 49}, "end": {"line": 124, "column": 104}}, "line": 124}, "19": {"name": "(anonymous_19)", "decl": {"start": {"line": 127, "column": 76}, "end": {"line": 127, "column": 77}}, "loc": {"start": {"line": 127, "column": 92}, "end": {"line": 134, "column": 9}}, "line": 127}, "20": {"name": "(anonymous_20)", "decl": {"start": {"line": 135, "column": 42}, "end": {"line": 135, "column": 43}}, "loc": {"start": {"line": 135, "column": 52}, "end": {"line": 135, "column": 107}}, "line": 135}, "21": {"name": "(anonymous_21)", "decl": {"start": {"line": 138, "column": 77}, "end": {"line": 138, "column": 78}}, "loc": {"start": {"line": 138, "column": 93}, "end": {"line": 144, "column": 9}}, "line": 138}, "22": {"name": "(anonymous_22)", "decl": {"start": {"line": 145, "column": 43}, "end": {"line": 145, "column": 44}}, "loc": {"start": {"line": 145, "column": 53}, "end": {"line": 145, "column": 108}}, "line": 145}, "23": {"name": "(anonymous_23)", "decl": {"start": {"line": 148, "column": 77}, "end": {"line": 148, "column": 78}}, "loc": {"start": {"line": 148, "column": 93}, "end": {"line": 154, "column": 9}}, "line": 148}, "24": {"name": "(anonymous_24)", "decl": {"start": {"line": 155, "column": 43}, "end": {"line": 155, "column": 44}}, "loc": {"start": {"line": 155, "column": 53}, "end": {"line": 155, "column": 108}}, "line": 155}, "25": {"name": "(anonymous_25)", "decl": {"start": {"line": 160, "column": 21}, "end": {"line": 160, "column": 22}}, "loc": {"start": {"line": 160, "column": 53}, "end": {"line": 188, "column": 3}}, "line": 160}, "26": {"name": "(anonymous_26)", "decl": {"start": {"line": 167, "column": 16}, "end": {"line": 167, "column": 17}}, "loc": {"start": {"line": 167, "column": 22}, "end": {"line": 167, "column": 60}}, "line": 167}, "27": {"name": "(anonymous_27)", "decl": {"start": {"line": 180, "column": 19}, "end": {"line": 180, "column": 20}}, "loc": {"start": {"line": 180, "column": 25}, "end": {"line": 180, "column": 45}}, "line": 180}, "28": {"name": "(anonymous_28)", "decl": {"start": {"line": 195, "column": 20}, "end": {"line": 195, "column": 21}}, "loc": {"start": {"line": 195, "column": 26}, "end": {"line": 195, "column": 57}}, "line": 195}, "29": {"name": "(anonymous_29)", "decl": {"start": {"line": 202, "column": 17}, "end": {"line": 202, "column": 18}}, "loc": {"start": {"line": 202, "column": 23}, "end": {"line": 202, "column": 50}}, "line": 202}, "30": {"name": "(anonymous_30)", "decl": {"start": {"line": 226, "column": 25}, "end": {"line": 226, "column": 26}}, "loc": {"start": {"line": 226, "column": 31}, "end": {"line": 226, "column": 45}}, "line": 226}, "31": {"name": "(anonymous_31)", "decl": {"start": {"line": 234, "column": 21}, "end": {"line": 234, "column": 22}}, "loc": {"start": {"line": 234, "column": 35}, "end": {"line": 234, "column": 63}}, "line": 234}, "32": {"name": "(anonymous_32)", "decl": {"start": {"line": 239, "column": 43}, "end": {"line": 239, "column": 44}}, "loc": {"start": {"line": 240, "column": 14}, "end": {"line": 247, "column": 21}}, "line": 240}, "33": {"name": "(anonymous_33)", "decl": {"start": {"line": 250, "column": 46}, "end": {"line": 250, "column": 47}}, "loc": {"start": {"line": 251, "column": 14}, "end": {"line": 258, "column": 21}}, "line": 251}, "34": {"name": "(anonymous_34)", "decl": {"start": {"line": 261, "column": 47}, "end": {"line": 261, "column": 48}}, "loc": {"start": {"line": 262, "column": 14}, "end": {"line": 269, "column": 21}}, "line": 262}, "35": {"name": "(anonymous_35)", "decl": {"start": {"line": 272, "column": 43}, "end": {"line": 272, "column": 44}}, "loc": {"start": {"line": 273, "column": 14}, "end": {"line": 280, "column": 21}}, "line": 273}, "36": {"name": "(anonymous_36)", "decl": {"start": {"line": 288, "column": 25}, "end": {"line": 288, "column": 26}}, "loc": {"start": {"line": 288, "column": 31}, "end": {"line": 288, "column": 62}}, "line": 288}, "37": {"name": "(anonymous_37)", "decl": {"start": {"line": 295, "column": 25}, "end": {"line": 295, "column": 26}}, "loc": {"start": {"line": 295, "column": 31}, "end": {"line": 295, "column": 64}}, "line": 295}, "38": {"name": "(anonymous_38)", "decl": {"start": {"line": 308, "column": 21}, "end": {"line": 308, "column": 22}}, "loc": {"start": {"line": 308, "column": 35}, "end": {"line": 309, "column": 43}}, "line": 308}}, "branchMap": {"0": {"loc": {"start": {"line": 42, "column": 4}, "end": {"line": 51, "column": 5}}, "type": "if", "locations": [{"start": {"line": 42, "column": 4}, "end": {"line": 51, "column": 5}}, {"start": {"line": 48, "column": 11}, "end": {"line": 51, "column": 5}}], "line": 42}, "1": {"loc": {"start": {"line": 93, "column": 4}, "end": {"line": 95, "column": 5}}, "type": "if", "locations": [{"start": {"line": 93, "column": 4}, "end": {"line": 95, "column": 5}}, {"start": {}, "end": {}}], "line": 93}, "2": {"loc": {"start": {"line": 104, "column": 2}, "end": {"line": 104, "column": 45}}, "type": "if", "locations": [{"start": {"line": 104, "column": 2}, "end": {"line": 104, "column": 45}}, {"start": {}, "end": {}}], "line": 104}, "3": {"loc": {"start": {"line": 109, "column": 4}, "end": {"line": 157, "column": 5}}, "type": "switch", "locations": [{"start": {"line": 110, "column": 6}, "end": {"line": 125, "column": 7}}, {"start": {"line": 126, "column": 6}, "end": {"line": 136, "column": 7}}, {"start": {"line": 137, "column": 6}, "end": {"line": 146, "column": 7}}, {"start": {"line": 147, "column": 6}, "end": {"line": 156, "column": 7}}], "line": 109}, "4": {"loc": {"start": {"line": 117, "column": 17}, "end": {"line": 118, "column": 83}}, "type": "binary-expr", "locations": [{"start": {"line": 117, "column": 17}, "end": {"line": 117, "column": 73}}, {"start": {"line": 118, "column": 16}, "end": {"line": 118, "column": 57}}, {"start": {"line": 118, "column": 61}, "end": {"line": 118, "column": 82}}], "line": 117}, "5": {"loc": {"start": {"line": 120, "column": 8}, "end": {"line": 123, "column": 9}}, "type": "if", "locations": [{"start": {"line": 120, "column": 8}, "end": {"line": 123, "column": 9}}, {"start": {}, "end": {}}], "line": 120}, "6": {"loc": {"start": {"line": 132, "column": 17}, "end": {"line": 133, "column": 57}}, "type": "binary-expr", "locations": [{"start": {"line": 132, "column": 17}, "end": {"line": 132, "column": 29}}, {"start": {"line": 132, "column": 33}, "end": {"line": 132, "column": 46}}, {"start": {"line": 132, "column": 50}, "end": {"line": 133, "column": 31}}, {"start": {"line": 133, "column": 35}, "end": {"line": 133, "column": 57}}], "line": 132}, "7": {"loc": {"start": {"line": 143, "column": 17}, "end": {"line": 143, "column": 73}}, "type": "binary-expr", "locations": [{"start": {"line": 143, "column": 17}, "end": {"line": 143, "column": 29}}, {"start": {"line": 143, "column": 33}, "end": {"line": 143, "column": 47}}, {"start": {"line": 143, "column": 51}, "end": {"line": 143, "column": 73}}], "line": 143}, "8": {"loc": {"start": {"line": 153, "column": 17}, "end": {"line": 153, "column": 56}}, "type": "binary-expr", "locations": [{"start": {"line": 153, "column": 17}, "end": {"line": 153, "column": 30}}, {"start": {"line": 153, "column": 34}, "end": {"line": 153, "column": 56}}], "line": 153}, "9": {"loc": {"start": {"line": 166, "column": 21}, "end": {"line": 167, "column": 60}}, "type": "cond-expr", "locations": [{"start": {"line": 166, "column": 45}, "end": {"line": 166, "column": 54}}, {"start": {"line": 167, "column": 16}, "end": {"line": 167, "column": 60}}], "line": 166}, "10": {"loc": {"start": {"line": 171, "column": 21}, "end": {"line": 172, "column": 65}}, "type": "cond-expr", "locations": [{"start": {"line": 172, "column": 18}, "end": {"line": 172, "column": 43}}, {"start": {"line": 172, "column": 46}, "end": {"line": 172, "column": 65}}], "line": 171}, "11": {"loc": {"start": {"line": 177, "column": 9}, "end": {"line": 184, "column": 20}}, "type": "binary-expr", "locations": [{"start": {"line": 177, "column": 9}, "end": {"line": 177, "column": 30}}, {"start": {"line": 177, "column": 34}, "end": {"line": 177, "column": 75}}, {"start": {"line": 178, "column": 8}, "end": {"line": 184, "column": 20}}], "line": 177}, "12": {"loc": {"start": {"line": 213, "column": 33}, "end": {"line": 213, "column": 65}}, "type": "cond-expr", "locations": [{"start": {"line": 213, "column": 46}, "end": {"line": 213, "column": 54}}, {"start": {"line": 213, "column": 57}, "end": {"line": 213, "column": 65}}], "line": 213}, "13": {"loc": {"start": {"line": 241, "column": 17}, "end": {"line": 244, "column": 25}}, "type": "binary-expr", "locations": [{"start": {"line": 241, "column": 17}, "end": {"line": 241, "column": 28}}, {"start": {"line": 242, "column": 16}, "end": {"line": 244, "column": 25}}], "line": 241}, "14": {"loc": {"start": {"line": 252, "column": 17}, "end": {"line": 255, "column": 25}}, "type": "binary-expr", "locations": [{"start": {"line": 252, "column": 17}, "end": {"line": 252, "column": 28}}, {"start": {"line": 253, "column": 16}, "end": {"line": 255, "column": 25}}], "line": 252}, "15": {"loc": {"start": {"line": 263, "column": 17}, "end": {"line": 266, "column": 23}}, "type": "binary-expr", "locations": [{"start": {"line": 263, "column": 17}, "end": {"line": 263, "column": 28}}, {"start": {"line": 264, "column": 16}, "end": {"line": 266, "column": 23}}], "line": 263}, "16": {"loc": {"start": {"line": 274, "column": 17}, "end": {"line": 277, "column": 25}}, "type": "binary-expr", "locations": [{"start": {"line": 274, "column": 17}, "end": {"line": 274, "column": 28}}, {"start": {"line": 275, "column": 16}, "end": {"line": 277, "column": 25}}], "line": 274}}, "s": {"0": 2, "1": 0, "2": 87, "3": 87, "4": 87, "5": 87, "6": 87, "7": 87, "8": 87, "9": 29, "10": 87, "11": 18, "12": 6, "13": 12, "14": 12, "15": 87, "16": 3, "17": 3, "18": 3, "19": 3, "20": 87, "21": 5, "22": 5, "23": 87, "24": 1, "25": 1, "26": 4, "27": 4, "28": 4, "29": 1, "30": 87, "31": 0, "32": 0, "33": 87, "34": 4, "35": 4, "36": 2, "37": 87, "38": 8, "39": 87, "40": 4, "41": 4, "42": 0, "43": 87, "44": 348, "45": 87, "46": 101, "47": 87, "48": 167, "49": 167, "50": 167, "51": 87, "52": 86, "53": 108, "54": 1, "55": 0, "56": 87, "57": 167, "58": 167, "59": 167, "60": 167, "61": 167, "62": 87, "63": 0, "64": 87, "65": 167, "66": 167, "67": 167, "68": 167, "69": 167, "70": 87, "71": 0, "72": 87, "73": 167, "74": 167, "75": 167, "76": 167, "77": 167, "78": 87, "79": 22, "80": 87, "81": 131, "82": 0, "83": 5, "84": 87, "85": 0, "86": 1, "87": 1, "88": 4, "89": 86, "90": 0, "91": 0, "92": 45, "93": 3, "94": 1, "95": 4}, "f": {"0": 0, "1": 87, "2": 29, "3": 18, "4": 3, "5": 3, "6": 5, "7": 1, "8": 4, "9": 4, "10": 0, "11": 4, "12": 8, "13": 4, "14": 348, "15": 101, "16": 167, "17": 108, "18": 0, "19": 167, "20": 0, "21": 167, "22": 0, "23": 167, "24": 22, "25": 131, "26": 0, "27": 5, "28": 0, "29": 1, "30": 1, "31": 4, "32": 86, "33": 0, "34": 0, "35": 45, "36": 3, "37": 1, "38": 4}, "b": {"0": [6, 12], "1": [2, 2], "2": [4, 0], "3": [87, 87, 87, 87], "4": [167, 122, 98], "5": [86, 1], "6": [167, 158, 113, 0], "7": [167, 45, 0], "8": [167, 45], "9": [86, 45], "10": [86, 45], "11": [131, 86, 24], "12": [2, 85], "13": [86, 86], "14": [0, 0], "15": [0, 0], "16": [45, 23]}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "430659f972291c17c4f11687549462570e0ee616"}, "/Users/<USER>/Documents/PAASApplications/premium.audit.advisory.service.mobile/assets/icons/CloseIcon.tsx": {"path": "/Users/<USER>/Documents/PAASApplications/premium.audit.advisory.service.mobile/assets/icons/CloseIcon.tsx", "statementMap": {"0": {"start": {"line": 5, "column": 15}, "end": {"line": 11, "column": 2}}, "1": {"start": {"line": 16, "column": 20}, "end": {"line": 30, "column": 5}}, "2": {"start": {"line": 17, "column": 8}, "end": {"line": 29, "column": 10}}, "3": {"start": {"line": 32, "column": 4}, "end": {"line": 36, "column": 6}}}, "fnMap": {"0": {"name": "Close", "decl": {"start": {"line": 13, "column": 24}, "end": {"line": 13, "column": 29}}, "loc": {"start": {"line": 15, "column": 3}, "end": {"line": 37, "column": 1}}, "line": 15}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 16, "column": 20}, "end": {"line": 16, "column": 21}}, "loc": {"start": {"line": 16, "column": 36}, "end": {"line": 30, "column": 5}}, "line": 16}}, "branchMap": {}, "s": {"0": 2, "1": 24, "2": 24, "3": 24}, "f": {"0": 24, "1": 24}, "b": {}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "0aba9225c8745e7512d7229f70d9b962cf062a2f"}, "/Users/<USER>/Documents/PAASApplications/premium.audit.advisory.service.mobile/assets/icons/CopyIcon.tsx": {"path": "/Users/<USER>/Documents/PAASApplications/premium.audit.advisory.service.mobile/assets/icons/CopyIcon.tsx", "statementMap": {"0": {"start": {"line": 5, "column": 15}, "end": {"line": 17, "column": 2}}, "1": {"start": {"line": 24, "column": 20}, "end": {"line": 57, "column": 5}}, "2": {"start": {"line": 25, "column": 8}, "end": {"line": 55, "column": 9}}, "3": {"start": {"line": 26, "column": 12}, "end": {"line": 48, "column": 14}}, "4": {"start": {"line": 50, "column": 12}, "end": {"line": 54, "column": 14}}, "5": {"start": {"line": 59, "column": 4}, "end": {"line": 63, "column": 6}}}, "fnMap": {"0": {"name": "Copy", "decl": {"start": {"line": 19, "column": 24}, "end": {"line": 19, "column": 28}}, "loc": {"start": {"line": 23, "column": 3}, "end": {"line": 64, "column": 1}}, "line": 23}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 24, "column": 20}, "end": {"line": 24, "column": 21}}, "loc": {"start": {"line": 24, "column": 36}, "end": {"line": 57, "column": 5}}, "line": 24}}, "branchMap": {"0": {"loc": {"start": {"line": 25, "column": 8}, "end": {"line": 55, "column": 9}}, "type": "if", "locations": [{"start": {"line": 25, "column": 8}, "end": {"line": 55, "column": 9}}, {"start": {"line": 49, "column": 15}, "end": {"line": 55, "column": 9}}], "line": 25}, "1": {"loc": {"start": {"line": 60, "column": 31}, "end": {"line": 60, "column": 56}}, "type": "binary-expr", "locations": [{"start": {"line": 60, "column": 31}, "end": {"line": 60, "column": 39}}, {"start": {"line": 60, "column": 43}, "end": {"line": 60, "column": 56}}], "line": 60}}, "s": {"0": 1, "1": 10, "2": 10, "3": 1, "4": 9, "5": 10}, "f": {"0": 10, "1": 10}, "b": {"0": [1, 9], "1": [10, 1]}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "b01f19f50db65e326d95fdbcdb88e54cb359a7ec"}, "/Users/<USER>/Documents/PAASApplications/premium.audit.advisory.service.mobile/assets/icons/DeleteIcon.tsx": {"path": "/Users/<USER>/Documents/PAASApplications/premium.audit.advisory.service.mobile/assets/icons/DeleteIcon.tsx", "statementMap": {"0": {"start": {"line": 5, "column": 15}, "end": {"line": 11, "column": 2}}, "1": {"start": {"line": 14, "column": 20}, "end": {"line": 38, "column": 5}}, "2": {"start": {"line": 15, "column": 8}, "end": {"line": 37, "column": 10}}, "3": {"start": {"line": 40, "column": 4}, "end": {"line": 44, "column": 6}}}, "fnMap": {"0": {"name": "DeleteIcon", "decl": {"start": {"line": 13, "column": 24}, "end": {"line": 13, "column": 34}}, "loc": {"start": {"line": 13, "column": 47}, "end": {"line": 45, "column": 1}}, "line": 13}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 14, "column": 20}, "end": {"line": 14, "column": 21}}, "loc": {"start": {"line": 14, "column": 36}, "end": {"line": 38, "column": 5}}, "line": 14}}, "branchMap": {}, "s": {"0": 2, "1": 10, "2": 10, "3": 10}, "f": {"0": 10, "1": 10}, "b": {}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "5e3b9347193c28c1a4a08770f6294a732ad9667c"}, "/Users/<USER>/Documents/PAASApplications/premium.audit.advisory.service.mobile/assets/icons/EditIcon.tsx": {"path": "/Users/<USER>/Documents/PAASApplications/premium.audit.advisory.service.mobile/assets/icons/EditIcon.tsx", "statementMap": {"0": {"start": {"line": 5, "column": 15}, "end": {"line": 11, "column": 2}}, "1": {"start": {"line": 14, "column": 20}, "end": {"line": 23, "column": 5}}, "2": {"start": {"line": 15, "column": 8}, "end": {"line": 22, "column": 10}}, "3": {"start": {"line": 25, "column": 4}, "end": {"line": 29, "column": 6}}}, "fnMap": {"0": {"name": "EditIcon", "decl": {"start": {"line": 13, "column": 24}, "end": {"line": 13, "column": 32}}, "loc": {"start": {"line": 13, "column": 45}, "end": {"line": 30, "column": 1}}, "line": 13}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 14, "column": 20}, "end": {"line": 14, "column": 21}}, "loc": {"start": {"line": 14, "column": 36}, "end": {"line": 23, "column": 5}}, "line": 14}}, "branchMap": {}, "s": {"0": 2, "1": 10, "2": 10, "3": 10}, "f": {"0": 10, "1": 10}, "b": {}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "610ea729d8726e398db684ce7650b8bcdcbff7c1"}, "/Users/<USER>/Documents/PAASApplications/premium.audit.advisory.service.mobile/assets/icons/EllipsesIcon.tsx": {"path": "/Users/<USER>/Documents/PAASApplications/premium.audit.advisory.service.mobile/assets/icons/EllipsesIcon.tsx", "statementMap": {"0": {"start": {"line": 5, "column": 15}, "end": {"line": 11, "column": 2}}, "1": {"start": {"line": 14, "column": 20}, "end": {"line": 23, "column": 5}}, "2": {"start": {"line": 15, "column": 8}, "end": {"line": 22, "column": 10}}, "3": {"start": {"line": 25, "column": 4}, "end": {"line": 29, "column": 6}}}, "fnMap": {"0": {"name": "Ellipses<PERSON><PERSON>", "decl": {"start": {"line": 13, "column": 24}, "end": {"line": 13, "column": 36}}, "loc": {"start": {"line": 13, "column": 49}, "end": {"line": 30, "column": 1}}, "line": 13}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 14, "column": 20}, "end": {"line": 14, "column": 21}}, "loc": {"start": {"line": 14, "column": 36}, "end": {"line": 23, "column": 5}}, "line": 14}}, "branchMap": {}, "s": {"0": 2, "1": 10, "2": 10, "3": 10}, "f": {"0": 10, "1": 10}, "b": {}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "c5ab17a8f76a4bc1455c407ea1a80a5294b60b52"}, "/Users/<USER>/Documents/PAASApplications/premium.audit.advisory.service.mobile/assets/icons/LogoutIcon.tsx": {"path": "/Users/<USER>/Documents/PAASApplications/premium.audit.advisory.service.mobile/assets/icons/LogoutIcon.tsx", "statementMap": {"0": {"start": {"line": 5, "column": 15}, "end": {"line": 12, "column": 2}}, "1": {"start": {"line": 17, "column": 20}, "end": {"line": 26, "column": 5}}, "2": {"start": {"line": 18, "column": 8}, "end": {"line": 25, "column": 10}}, "3": {"start": {"line": 28, "column": 4}, "end": {"line": 32, "column": 6}}}, "fnMap": {"0": {"name": "LogoutIcon", "decl": {"start": {"line": 14, "column": 24}, "end": {"line": 14, "column": 34}}, "loc": {"start": {"line": 16, "column": 3}, "end": {"line": 33, "column": 1}}, "line": 16}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 17, "column": 20}, "end": {"line": 17, "column": 21}}, "loc": {"start": {"line": 17, "column": 36}, "end": {"line": 26, "column": 5}}, "line": 17}}, "branchMap": {}, "s": {"0": 1, "1": 3, "2": 3, "3": 3}, "f": {"0": 3, "1": 3}, "b": {}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "e58317005075837d3d539e8bd7dea7c5e6403410"}, "/Users/<USER>/Documents/PAASApplications/premium.audit.advisory.service.mobile/assets/icons/NewConversationIcon.tsx": {"path": "/Users/<USER>/Documents/PAASApplications/premium.audit.advisory.service.mobile/assets/icons/NewConversationIcon.tsx", "statementMap": {"0": {"start": {"line": 5, "column": 15}, "end": {"line": 11, "column": 2}}, "1": {"start": {"line": 14, "column": 20}, "end": {"line": 23, "column": 5}}, "2": {"start": {"line": 15, "column": 8}, "end": {"line": 22, "column": 10}}, "3": {"start": {"line": 25, "column": 4}, "end": {"line": 29, "column": 6}}}, "fnMap": {"0": {"name": "Close", "decl": {"start": {"line": 13, "column": 24}, "end": {"line": 13, "column": 29}}, "loc": {"start": {"line": 13, "column": 32}, "end": {"line": 30, "column": 1}}, "line": 13}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 14, "column": 20}, "end": {"line": 14, "column": 21}}, "loc": {"start": {"line": 14, "column": 36}, "end": {"line": 23, "column": 5}}, "line": 14}}, "branchMap": {}, "s": {"0": 1, "1": 51, "2": 51, "3": 51}, "f": {"0": 51, "1": 51}, "b": {}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "bc292d07f9078786fdf3bdd221ad388d4a96d574"}, "/Users/<USER>/Documents/PAASApplications/premium.audit.advisory.service.mobile/assets/icons/ProfileIcon.tsx": {"path": "/Users/<USER>/Documents/PAASApplications/premium.audit.advisory.service.mobile/assets/icons/ProfileIcon.tsx", "statementMap": {"0": {"start": {"line": 5, "column": 15}, "end": {"line": 10, "column": 2}}, "1": {"start": {"line": 13, "column": 20}, "end": {"line": 28, "column": 5}}, "2": {"start": {"line": 14, "column": 8}, "end": {"line": 27, "column": 10}}, "3": {"start": {"line": 30, "column": 4}, "end": {"line": 34, "column": 6}}}, "fnMap": {"0": {"name": "ProfileIcon", "decl": {"start": {"line": 12, "column": 24}, "end": {"line": 12, "column": 35}}, "loc": {"start": {"line": 12, "column": 38}, "end": {"line": 35, "column": 1}}, "line": 12}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 13, "column": 20}, "end": {"line": 13, "column": 21}}, "loc": {"start": {"line": 13, "column": 36}, "end": {"line": 28, "column": 5}}, "line": 13}}, "branchMap": {}, "s": {"0": 1, "1": 51, "2": 51, "3": 51}, "f": {"0": 51, "1": 51}, "b": {}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "1a6109693ec393487d0cdad787da12f349c47e58"}, "/Users/<USER>/Documents/PAASApplications/premium.audit.advisory.service.mobile/assets/icons/SearchIcon.tsx": {"path": "/Users/<USER>/Documents/PAASApplications/premium.audit.advisory.service.mobile/assets/icons/SearchIcon.tsx", "statementMap": {"0": {"start": {"line": 5, "column": 15}, "end": {"line": 11, "column": 2}}, "1": {"start": {"line": 14, "column": 20}, "end": {"line": 38, "column": 5}}, "2": {"start": {"line": 15, "column": 8}, "end": {"line": 37, "column": 10}}, "3": {"start": {"line": 40, "column": 4}, "end": {"line": 44, "column": 6}}}, "fnMap": {"0": {"name": "SearchIcon", "decl": {"start": {"line": 13, "column": 24}, "end": {"line": 13, "column": 34}}, "loc": {"start": {"line": 13, "column": 47}, "end": {"line": 45, "column": 1}}, "line": 13}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 14, "column": 20}, "end": {"line": 14, "column": 21}}, "loc": {"start": {"line": 14, "column": 36}, "end": {"line": 38, "column": 5}}, "line": 14}}, "branchMap": {}, "s": {"0": 2, "1": 32, "2": 32, "3": 32}, "f": {"0": 32, "1": 32}, "b": {}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "66275780ee65d358e047765cd8211c9585face53"}, "/Users/<USER>/Documents/PAASApplications/premium.audit.advisory.service.mobile/assets/icons/SidebarIcon.tsx": {"path": "/Users/<USER>/Documents/PAASApplications/premium.audit.advisory.service.mobile/assets/icons/SidebarIcon.tsx", "statementMap": {"0": {"start": {"line": 5, "column": 15}, "end": {"line": 10, "column": 2}}, "1": {"start": {"line": 17, "column": 18}, "end": {"line": 31, "column": 5}}, "2": {"start": {"line": 18, "column": 8}, "end": {"line": 30, "column": 10}}, "3": {"start": {"line": 33, "column": 4}, "end": {"line": 37, "column": 6}}}, "fnMap": {"0": {"name": "SidebarIcon", "decl": {"start": {"line": 16, "column": 24}, "end": {"line": 16, "column": 35}}, "loc": {"start": {"line": 16, "column": 68}, "end": {"line": 38, "column": 1}}, "line": 16}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 17, "column": 18}, "end": {"line": 17, "column": 19}}, "loc": {"start": {"line": 17, "column": 34}, "end": {"line": 31, "column": 5}}, "line": 17}}, "branchMap": {}, "s": {"0": 1, "1": 51, "2": 51, "3": 51}, "f": {"0": 51, "1": 51}, "b": {}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "ec74fa6e9c331c1fae1691ebaaa1075100e97f7f"}, "/Users/<USER>/Documents/PAASApplications/premium.audit.advisory.service.mobile/assets/icons/SubmitIcon.tsx": {"path": "/Users/<USER>/Documents/PAASApplications/premium.audit.advisory.service.mobile/assets/icons/SubmitIcon.tsx", "statementMap": {"0": {"start": {"line": 10, "column": 19}, "end": {"line": 26, "column": 6}}, "1": {"start": {"line": 28, "column": 26}, "end": {"line": 42, "column": 5}}, "2": {"start": {"line": 29, "column": 8}, "end": {"line": 41, "column": 10}}, "3": {"start": {"line": 44, "column": 26}, "end": {"line": 58, "column": 5}}, "4": {"start": {"line": 45, "column": 8}, "end": {"line": 57, "column": 10}}, "5": {"start": {"line": 61, "column": 4}, "end": {"line": 69, "column": 6}}}, "fnMap": {"0": {"name": "SubmitIcon", "decl": {"start": {"line": 5, "column": 24}, "end": {"line": 5, "column": 34}}, "loc": {"start": {"line": 9, "column": 3}, "end": {"line": 70, "column": 1}}, "line": 9}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 28, "column": 26}, "end": {"line": 28, "column": 27}}, "loc": {"start": {"line": 28, "column": 42}, "end": {"line": 42, "column": 5}}, "line": 28}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 44, "column": 26}, "end": {"line": 44, "column": 27}}, "loc": {"start": {"line": 44, "column": 42}, "end": {"line": 58, "column": 5}}, "line": 44}}, "branchMap": {"0": {"loc": {"start": {"line": 16, "column": 27}, "end": {"line": 16, "column": 59}}, "type": "cond-expr", "locations": [{"start": {"line": 16, "column": 38}, "end": {"line": 16, "column": 47}}, {"start": {"line": 16, "column": 50}, "end": {"line": 16, "column": 59}}], "line": 16}, "1": {"loc": {"start": {"line": 65, "column": 13}, "end": {"line": 65, "column": 65}}, "type": "cond-expr", "locations": [{"start": {"line": 65, "column": 24}, "end": {"line": 65, "column": 43}}, {"start": {"line": 65, "column": 46}, "end": {"line": 65, "column": 65}}], "line": 65}}, "s": {"0": 59, "1": 59, "2": 57, "3": 59, "4": 2, "5": 59}, "f": {"0": 59, "1": 57, "2": 2}, "b": {"0": [2, 57], "1": [2, 57]}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "d383551c0b2442e39da9709107aa7fbf80884aef"}, "/Users/<USER>/Documents/PAASApplications/premium.audit.advisory.service.mobile/assets/icons/ThumbDown.tsx": {"path": "/Users/<USER>/Documents/PAASApplications/premium.audit.advisory.service.mobile/assets/icons/ThumbDown.tsx", "statementMap": {"0": {"start": {"line": 6, "column": 15}, "end": {"line": 14, "column": 2}}, "1": {"start": {"line": 22, "column": 21}, "end": {"line": 24, "column": 5}}, "2": {"start": {"line": 23, "column": 8}, "end": {"line": 23, "column": 48}}, "3": {"start": {"line": 26, "column": 25}, "end": {"line": 43, "column": 5}}, "4": {"start": {"line": 27, "column": 8}, "end": {"line": 42, "column": 10}}, "5": {"start": {"line": 45, "column": 4}, "end": {"line": 49, "column": 6}}}, "fnMap": {"0": {"name": "ThumbDown", "decl": {"start": {"line": 16, "column": 24}, "end": {"line": 16, "column": 33}}, "loc": {"start": {"line": 20, "column": 3}, "end": {"line": 50, "column": 1}}, "line": 20}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 22, "column": 21}, "end": {"line": 22, "column": 22}}, "loc": {"start": {"line": 22, "column": 27}, "end": {"line": 24, "column": 5}}, "line": 22}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 26, "column": 25}, "end": {"line": 26, "column": 26}}, "loc": {"start": {"line": 26, "column": 31}, "end": {"line": 43, "column": 5}}, "line": 26}}, "branchMap": {"0": {"loc": {"start": {"line": 23, "column": 15}, "end": {"line": 23, "column": 47}}, "type": "cond-expr", "locations": [{"start": {"line": 23, "column": 26}, "end": {"line": 23, "column": 35}}, {"start": {"line": 23, "column": 38}, "end": {"line": 23, "column": 47}}], "line": 23}}, "s": {"0": 1, "1": 10, "2": 10, "3": 10, "4": 10, "5": 10}, "f": {"0": 10, "1": 10, "2": 10}, "b": {"0": [1, 9]}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "97ea7fbcd57ac270330ae2db595110184206d1d6"}, "/Users/<USER>/Documents/PAASApplications/premium.audit.advisory.service.mobile/assets/icons/ThumbUp.tsx": {"path": "/Users/<USER>/Documents/PAASApplications/premium.audit.advisory.service.mobile/assets/icons/ThumbUp.tsx", "statementMap": {"0": {"start": {"line": 6, "column": 15}, "end": {"line": 13, "column": 2}}, "1": {"start": {"line": 21, "column": 21}, "end": {"line": 23, "column": 5}}, "2": {"start": {"line": 22, "column": 8}, "end": {"line": 22, "column": 48}}, "3": {"start": {"line": 25, "column": 23}, "end": {"line": 42, "column": 5}}, "4": {"start": {"line": 26, "column": 8}, "end": {"line": 41, "column": 10}}, "5": {"start": {"line": 44, "column": 4}, "end": {"line": 48, "column": 6}}}, "fnMap": {"0": {"name": "ThumbUp", "decl": {"start": {"line": 15, "column": 24}, "end": {"line": 15, "column": 31}}, "loc": {"start": {"line": 19, "column": 3}, "end": {"line": 49, "column": 1}}, "line": 19}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 21, "column": 21}, "end": {"line": 21, "column": 22}}, "loc": {"start": {"line": 21, "column": 27}, "end": {"line": 23, "column": 5}}, "line": 21}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 25, "column": 23}, "end": {"line": 25, "column": 24}}, "loc": {"start": {"line": 25, "column": 29}, "end": {"line": 42, "column": 5}}, "line": 25}}, "branchMap": {"0": {"loc": {"start": {"line": 22, "column": 15}, "end": {"line": 22, "column": 47}}, "type": "cond-expr", "locations": [{"start": {"line": 22, "column": 26}, "end": {"line": 22, "column": 35}}, {"start": {"line": 22, "column": 38}, "end": {"line": 22, "column": 47}}], "line": 22}}, "s": {"0": 1, "1": 10, "2": 10, "3": 10, "4": 10, "5": 10}, "f": {"0": 10, "1": 10, "2": 10}, "b": {"0": [2, 8]}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "f3f634e27c4fa612500024b4242bf21475dcf7b1"}, "/Users/<USER>/Documents/PAASApplications/premium.audit.advisory.service.mobile/assets/icons/TypingIndicator.tsx": {"path": "/Users/<USER>/Documents/PAASApplications/premium.audit.advisory.service.mobile/assets/icons/TypingIndicator.tsx", "statementMap": {"0": {"start": {"line": 4, "column": 24}, "end": {"line": 51, "column": 1}}, "1": {"start": {"line": 5, "column": 25}, "end": {"line": 11, "column": 3}}, "2": {"start": {"line": 13, "column": 2}, "end": {"line": 35, "column": 23}}, "3": {"start": {"line": 14, "column": 20}, "end": {"line": 33, "column": 5}}, "4": {"start": {"line": 15, "column": 6}, "end": {"line": 32, "column": 9}}, "5": {"start": {"line": 16, "column": 8}, "end": {"line": 31, "column": 18}}, "6": {"start": {"line": 34, "column": 4}, "end": {"line": 34, "column": 14}}, "7": {"start": {"line": 37, "column": 17}, "end": {"line": 37, "column": 50}}, "8": {"start": {"line": 38, "column": 29}, "end": {"line": 42, "column": 5}}, "9": {"start": {"line": 39, "column": 35}, "end": {"line": 42, "column": 4}}, "10": {"start": {"line": 44, "column": 2}, "end": {"line": 50, "column": 4}}, "11": {"start": {"line": 47, "column": 8}, "end": {"line": 47, "column": 86}}, "12": {"start": {"line": 53, "column": 15}, "end": {"line": 62, "column": 2}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 4, "column": 24}, "end": {"line": 4, "column": 25}}, "loc": {"start": {"line": 4, "column": 30}, "end": {"line": 51, "column": 1}}, "line": 4}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 13, "column": 12}, "end": {"line": 13, "column": 13}}, "loc": {"start": {"line": 13, "column": 18}, "end": {"line": 35, "column": 3}}, "line": 13}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 14, "column": 20}, "end": {"line": 14, "column": 21}}, "loc": {"start": {"line": 14, "column": 26}, "end": {"line": 33, "column": 5}}, "line": 14}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 15, "column": 29}, "end": {"line": 15, "column": 30}}, "loc": {"start": {"line": 15, "column": 55}, "end": {"line": 32, "column": 7}}, "line": 15}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 39, "column": 9}, "end": {"line": 39, "column": 10}}, "loc": {"start": {"line": 39, "column": 35}, "end": {"line": 42, "column": 4}}, "line": 39}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 46, "column": 30}, "end": {"line": 46, "column": 31}}, "loc": {"start": {"line": 47, "column": 8}, "end": {"line": 47, "column": 86}}, "line": 47}}, "branchMap": {}, "s": {"0": 1, "1": 6, "2": 6, "3": 6, "4": 6, "5": 30, "6": 6, "7": 6, "8": 6, "9": 30, "10": 6, "11": 30, "12": 1}, "f": {"0": 6, "1": 6, "2": 6, "3": 30, "4": 30, "5": 30}, "b": {}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "1f49a65387a69183eba03ee3f1141a81c11c6337"}, "/Users/<USER>/Documents/PAASApplications/premium.audit.advisory.service.mobile/assets/icons/UserIcon.tsx": {"path": "/Users/<USER>/Documents/PAASApplications/premium.audit.advisory.service.mobile/assets/icons/UserIcon.tsx", "statementMap": {"0": {"start": {"line": 9, "column": 19}, "end": {"line": 15, "column": 6}}, "1": {"start": {"line": 17, "column": 24}, "end": {"line": 22, "column": 5}}, "2": {"start": {"line": 18, "column": 21}, "end": {"line": 18, "column": 40}}, "3": {"start": {"line": 19, "column": 8}, "end": {"line": 21, "column": 24}}, "4": {"start": {"line": 24, "column": 4}, "end": {"line": 36, "column": 6}}}, "fnMap": {"0": {"name": "Icon", "decl": {"start": {"line": 4, "column": 24}, "end": {"line": 4, "column": 28}}, "loc": {"start": {"line": 8, "column": 3}, "end": {"line": 37, "column": 1}}, "line": 8}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 17, "column": 24}, "end": {"line": 17, "column": 25}}, "loc": {"start": {"line": 17, "column": 30}, "end": {"line": 22, "column": 5}}, "line": 17}}, "branchMap": {"0": {"loc": {"start": {"line": 11, "column": 19}, "end": {"line": 11, "column": 61}}, "type": "cond-expr", "locations": [{"start": {"line": 11, "column": 44}, "end": {"line": 11, "column": 51}}, {"start": {"line": 11, "column": 54}, "end": {"line": 11, "column": 61}}], "line": 11}, "1": {"loc": {"start": {"line": 29, "column": 29}, "end": {"line": 30, "column": 39}}, "type": "cond-expr", "locations": [{"start": {"line": 30, "column": 18}, "end": {"line": 30, "column": 27}}, {"start": {"line": 30, "column": 30}, "end": {"line": 30, "column": 39}}], "line": 29}}, "s": {"0": 46, "1": 46, "2": 46, "3": 46, "4": 46}, "f": {"0": 46, "1": 46}, "b": {"0": [30, 16], "1": [30, 16]}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "9c4649a5b213e1acd4f53b42f09538f0fc0b894c"}, "/Users/<USER>/Documents/PAASApplications/premium.audit.advisory.service.mobile/assets/icons/VeriskLogo.tsx": {"path": "/Users/<USER>/Documents/PAASApplications/premium.audit.advisory.service.mobile/assets/icons/VeriskLogo.tsx", "statementMap": {"0": {"start": {"line": 5, "column": 15}, "end": {"line": 11, "column": 2}}, "1": {"start": {"line": 14, "column": 20}, "end": {"line": 34, "column": 5}}, "2": {"start": {"line": 15, "column": 8}, "end": {"line": 33, "column": 10}}, "3": {"start": {"line": 36, "column": 4}, "end": {"line": 40, "column": 6}}}, "fnMap": {"0": {"name": "VeriskLogo", "decl": {"start": {"line": 13, "column": 24}, "end": {"line": 13, "column": 34}}, "loc": {"start": {"line": 13, "column": 47}, "end": {"line": 41, "column": 1}}, "line": 13}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 14, "column": 20}, "end": {"line": 14, "column": 21}}, "loc": {"start": {"line": 14, "column": 36}, "end": {"line": 34, "column": 5}}, "line": 14}}, "branchMap": {}, "s": {"0": 2, "1": 54, "2": 54, "3": 54}, "f": {"0": 54, "1": 54}, "b": {}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "5c9077af445ffc830ca5d70c596dd0fb33f0ba43"}, "/Users/<USER>/Documents/PAASApplications/premium.audit.advisory.service.mobile/assets/icons/VeriskWhiteLogo.tsx": {"path": "/Users/<USER>/Documents/PAASApplications/premium.audit.advisory.service.mobile/assets/icons/VeriskWhiteLogo.tsx", "statementMap": {"0": {"start": {"line": 5, "column": 15}, "end": {"line": 10, "column": 2}}, "1": {"start": {"line": 13, "column": 20}, "end": {"line": 29, "column": 5}}, "2": {"start": {"line": 14, "column": 8}, "end": {"line": 28, "column": 10}}, "3": {"start": {"line": 31, "column": 4}, "end": {"line": 35, "column": 6}}}, "fnMap": {"0": {"name": "VeriskWhiteLogo", "decl": {"start": {"line": 12, "column": 24}, "end": {"line": 12, "column": 39}}, "loc": {"start": {"line": 12, "column": 52}, "end": {"line": 36, "column": 1}}, "line": 12}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 13, "column": 20}, "end": {"line": 13, "column": 21}}, "loc": {"start": {"line": 13, "column": 36}, "end": {"line": 29, "column": 5}}, "line": 13}}, "branchMap": {}, "s": {"0": 2, "1": 54, "2": 54, "3": 54}, "f": {"0": 54, "1": 54}, "b": {}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "7a336b7beff1665add350ac1e77823399b279b0f"}}