{"expo": {"name": "Premium Audit Advisory Service Mobile", "slug": "premium-audit-advisory-service-mobile", "privacy": "unlisted", "platforms": ["ios", "android", "web"], "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "com.verisk.int.sso", "userInterfaceStyle": "automatic", "ios": {"buildNumber": "1.0.0", "bundleIdentifier": "com.verisk.paas", "CFBundleIdentifier": "com.verisk.paas", "CFBundleDisplayName": "PAAS Mobile", "CFBundleURLTypes": [{"CFBundleURLSchemes": ["com.verisk.paas"], "CFBundleURLName": "com.verisk.paas"}], "supportsTablet": true, "infoPlist": {"NSAppTransportSecurity": {"NSAllowsArbitraryLoads": true, "NSExceptionDomains": {"localhost": {"NSExceptionAllowsInsecureHTTPLoads": true, "NSIncludesSubdomains": true, "NSTemporaryExceptionMinimumTLSVersion": "TLSv1.1", "NSTemporaryExceptionAllowsInsecureHTTPLoads": true}, "com.verisk.paas": {"NSExceptionAllowsInsecureHTTPLoads": true, "NSIncludesSubdomains": true, "NSTemporaryExceptionMinimumTLSVersion": "TLSv1.1", "NSTemporaryExceptionAllowsInsecureHTTPLoads": true}, "http://com.verisk.paas": {"NSExceptionAllowsInsecureHTTPLoads": true, "NSIncludesSubdomains": true, "NSTemporaryExceptionMinimumTLSVersion": "TLSv1.1", "NSTemporaryExceptionAllowsInsecureHTTPLoads": true}}}, "CFBundleDevelopmentRegion": "en", "ITSAppUsesNonExemptEncryption": "no", "NSContactsUsageDescription": "Allow this app to access your contacts.", "NSLocationWhenInUseUsageDescription": "Allow this app to access your location while you use the app.", "NSLocationAlwaysUsageDescription": "Allow this app to access your location even when you are not using the app.", "NSLocationAlwaysAndWhenInUseUsageDescription": "Allow this app to access your location.", "NSCameraUsageDescription": "Allow this app to use the camera.", "NSCalendarsUsageDescription": "Allow this app to access your calendar.", "NSRemindersUsageDescription": "Allow this app to access your reminders.", "NSPhotoLibraryAddUsageDescription": "Allow this app to save photos to your photo library.", "NSPhotoLibraryUsageDescription": "Allow this app to access your photo library.", "NSMicrophoneUsageDescription": "Allow this app to use the microphone.", "NSAllowsArbitraryLoads": true, "NSExceptionDomains": {"localhost": {"NSExceptionAllowsInsecureHTTPLoads": true, "NSIncludesSubdomains": true, "NSTemporaryExceptionMinimumTLSVersion": "TLSv1.1"}, "com.verisk.paas": {"NSExceptionAllowsInsecureHTTPLoads": true, "NSIncludesSubdomains": true, "NSTemporaryExceptionMinimumTLSVersion": "TLSv1.1"}}, "NSSpeechRecognitionUsageDescription": "Allow this app to use speech recognition."}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#ffffff"}, "permissions": ["android.permission.RECORD_AUDIO"], "package": "com.verisk.paas"}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-font", ["expo-font", {"fonts": ["./assets/fonts/Roboto/Roboto-Regular.ttf", "./assets/fonts/Roboto/Roboto-Bold.ttf"]}], "expo-router", "expo-web-browser"], "experiments": {"typedRoutes": true}, "extra": {"eas": {"projectId": "877835a1-c727-4bb5-831a-dc2edbd49cf7"}, "router": {"origin": false}}, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}