import React, { useEffect, useRef } from 'react';
import { Animated, StyleSheet, View } from 'react-native';

const TypingIndicator = () => {
  const animatedValues = [
    useRef(new Animated.Value(0)).current, 
    useRef(new Animated.Value(0)).current, 
    useRef(new Animated.Value(0)).current, 
    useRef(new Animated.Value(0)).current, 
    useRef(new Animated.Value(0)).current
  ];

  useEffect(() => {
    const animate = () => {
      animatedValues.forEach((animatedValue, index) => {
        Animated.loop(
          Animated.sequence([
            Animated.timing(animatedValue, { 
              toValue: 1, 
              duration: 1500, 
              useNativeDriver: false }),
            Animated.timing(animatedValue, { 
              toValue: 2, 
              duration: 1500, 
              useNativeDriver: false }),
            Animated.timing(animatedValue, { 
              toValue: 0, 
              duration: 1500, 
              useNativeDriver: false }),
          ])
        ).start();
      });
    };
    animate();
  }, [animatedValues]);

  const colors = ['#5EBBFE', '#1E4B6B', '#E6EBF4'];
  const interpolatedColors = animatedValues
    .map((animatedValue, index) => animatedValue.interpolate({
    inputRange: [0, 1, 2],
    outputRange: colors.slice(index).concat(colors.slice(0, index)),
  }));

  return (
    <View style={styles.container}>
      {interpolatedColors.map((color, index) => (
        <Animated.View key={index} style={[styles.dot, { backgroundColor: color }]} />
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  container: { 
    flexDirection: 'row' 
  },
  dot: {
     width: 12, 
     height: 12, 
     borderRadius: 12, 
     margin: 4 },
});

export default TypingIndicator;