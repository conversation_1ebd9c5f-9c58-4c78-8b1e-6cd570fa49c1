import { useEffect, useState } from 'react';
import * as WebBrowser from 'expo-web-browser';
import * as AuthSession from 'expo-auth-session';
import { View, Text, TouchableOpacity, Button, Modal, Platform, Alert } from 'react-native';
import { TokenResponse } from 'expo-auth-session';
import { useAuth } from '../helpers/authContext';
import * as Linking from 'expo-linking';

WebBrowser.maybeCompleteAuthSession();
// const gatewayUrl = process.env.EXPO_PUBLIC_GATEWAY_URI || '';
const gatewayUrl = (process.env.EXPO_PUBLIC_GATEWAY_URI || '') + '?appType=webview';
const successurl = 'https://gateway-logint.verisk.com/app/paasmobile/auth/successful-login';
const oktaClientId = process.env.EXPO_PUBLIC_OKTA_CLIENT_ID || '';
const oktaDomain =  process.env.EXPO_PUBLIC_OKTA_DOMAIN_URL || '';
const authorizationEndpoint = oktaDomain + process.env.EXPO_PUBLIC_OKTA_AUTH_ENDPOINT;
const tokenEndpoint = oktaDomain + process.env.EXPO_PUBLIC_OKTA_TOKEN_ENDPOINT;
const userInfoEndpoint = oktaDomain + process.env.EXPO_PUBLIC_OKTA_USER_INFO_ENDPOINT;
const redirectUri = AuthSession.makeRedirectUri({
  native: process.env.EXPO_PUBLIC_OKTA_REDIRECT_URI
});
const oktaLogin = true;

import { StyleSheet } from 'react-native';
import { styles, topStyles } from '../helpers/stylesheet';
import VeriskLogo from '@/assets/icons/VeriskLogo';
import VeriskWhiteLogo from '@/assets/icons/VeriskWhiteLogo';
import { SitecoreData } from '../helpers/model';
import React from 'react';
const bottomCopyrightText = "Copyright © 2024 Insurance Services Office, Inc, All rights reserved.";


export default function Authentication() {
  const { setAuthData } = useAuth();
  const [ state, setState ] = React.useState<{ redirectData: any | null }>({ redirectData: null });
  const [result, setResult] = useState<WebBrowser.WebBrowserAuthSessionResult | null>(null)

  const [request, response, promptAsync] = AuthSession.useAuthRequest(
    {
      clientId: oktaClientId,
      scopes: ['openid', 'profile'],
      responseType: 'code',
      redirectUri: redirectUri
    },
    { 
      // authorizationEndpoint: Platform.OS == 'web' ? authorizationEndpoint : gatewayUrl, //use okta for web
      authorizationEndpoint: authorizationEndpoint, // use okta until gateway implementation
    }
  );

  useEffect(() => {
    console.log(result)
  }, [result])


  useEffect(() => {
    if (response?.type === 'success') {
      const params = response.params as Record<string, string>;
      getAuthData(params);
    }
  }, [response]);

  const getAuthData = async (params: Record<string, string>) => {
    const tokenRes: TokenResponse = await AuthSession.exchangeCodeAsync(
      {
        code: params["code"],
        redirectUri,
        clientId: oktaClientId,
        extraParams: {
          code_verifier: request?.codeVerifier || ''
        }
      },
      { tokenEndpoint }
    );

    if (tokenRes) {
      const userRes = await fetch(userInfoEndpoint, {
        method: 'GET',
        headers: {
          Accept: 'application/json',
          Authorization: `Bearer ${tokenRes.accessToken}`,
          'Content-Type': 'application/json'
        },
      });

      // get user data - email for API use and full name for initials display
      const userInfo = await userRes.json();
      const username = userInfo['preferred_username'];

      // store in cache
      const newAuthData = {
        accessToken: tokenRes.accessToken, 
        userId: username.includes('@') ? username.split('@')[0] : username,
        isAuth: true,
        username: userInfo['given_name'] + " " + userInfo['family_name'],
        hasAgreed: false,
        sitecore: new SitecoreData(),
      };

      setAuthData(newAuthData);
    }
  };

  const handleLoginPress = async () => {
    console.log(Platform.OS);
      promptAsync();
  }


  return (
    <View testID='authentication' style={loginStyles.container}>
      <View style={loginStyles.toolbar}/>
        <View style={loginStyles.header}>
          <View style={topStyles.topRow}>
            <View style={loginStyles.logo}>
              <VeriskLogo/>
            </View>
          </View>
        </View>
        <View style={styles.banner}>
          <Text style={loginStyles.bannerText} adjustsFontSizeToFit 
            numberOfLines={1} minimumFontScale={1}> 
            Premium Audit Advisory Service
          </Text>      
        </View>
        <View style={loginStyles.content}>
          <TouchableOpacity
            testID='login-button'
            style={loginStyles.button}
            onPress={() => {
              handleLoginPress();
            }}>
            <Text>Login to PAAS</Text>
          </TouchableOpacity>
        </View>
        <View style={loginStyles.bottomCopyright}>
          <VeriskWhiteLogo/>
          <Text style={loginStyles.copyrightText}>{bottomCopyrightText}</Text>
        </View>
        <View style={loginStyles.bottomToolbar}/>
    </View>
  );
}

const loginStyles = StyleSheet.create({
  container: {
    flex: 1,
    width: '100%',
    borderColor: 'black',
    borderWidth: 1,
  },
  toolbar:{
    backgroundColor: 'rgba(255, 255, 255, 0.25)',
    height: '5%',
    width: '100%',
  },
  header: {
    height: '5%',
    width: '100%',
    backgroundColor: 'rgba(255, 255, 255, 0.25)',
    flexDirection: 'column',
  },
  logo: {
    flex: 1, 
    justifyContent: 'center', 
    alignItems: 'center'
  },
  banner: {
    height: '8%',
    width: '100%',
    backgroundColor: '#00358E',
    justifyContent: 'center',
    flexWrap: 'nowrap',
  },
  bannerText: {
    color: 'white',
    textAlign: 'center',
    fontFamily: 'Roboto',
    fontStyle: 'normal',
    fontWeight: '400',
    lineHeight: 36,  
    flexShrink: 1,
    fontSize: 21
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 50,
    marginBottom: 50,
  },
  button: {
    fontFamily: 'Roboto',
    fontSize: 16,
    backgroundColor: '#FFC600',
    width: 260,
    padding: 12,
    borderWidth: 1,
    borderColor: '#FFC600',
    borderRadius: 4,
    alignItems: 'center',
    flexDirection: 'row',
    gap: 8,
    alignContent: 'center',
    justifyContent: 'center',
    
  },
  bottomCopyright: {
    height: '16%',
    backgroundColor: '#00358E',
    padding: 15,
    alignItems: 'center',
    paddingBottom: 15,
    paddingTop: 20,
    fontSize: 12,
  },
  copyrightText: {
    width: '60%',
    color: '#FFF',
    textAlign: 'center',
    fontFamily: 'Roboto',
    fontSize: 12,
    fontStyle: 'normal',
    fontWeight: '400',
    lineHeight: 20,
    paddingTop: 17,
    paddingBottom: 17,
  },
  bottomToolbar: {
    backgroundColor: '#00358E',
    height: '2%',
    width: '100%',
  }
});