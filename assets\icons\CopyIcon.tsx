import React, { act } from 'react';
import { View, StyleSheet } from 'react-native';
import Svg, { Defs, G, Mask, Path, Rect } from 'react-native-svg';

const styles = StyleSheet.create({
    icon: {
        height: 30,
        width: 30,
        padding: 3,
        marginRight: 10
    },  
    active: {
        padding: 4,
        backgroundColor: 'rgba(92, 199, 130, 0.1)',
        borderRadius: 20,
    }
});

export default function Copy({
    isActive,
}: { 
    isActive: boolean, 
}) { 
    const getIcon = (props: any) => {
        if (isActive) { 
            return (
                <Svg xmlns="http://www.w3.org/2000/svg" fill="none" {...props}>
                <Mask
                  id="a"
                  width={22}
                  height={22}
                  x={0}
                  y={0}
                  maskUnits="userSpaceOnUse"
                  style={{
                    maskType: "alpha",
                  }}
                >
                  <Path fill="#D9D9D9" d="M.091.091h21.818v21.818H.091z" />
                </Mask>
                <G mask="url(#a)">
                  <Path
                    fill="#00358E"
                    d="M8.773 16.454 3.59 11.273l1.295-1.296 3.887 3.887 8.34-8.341 1.296 1.295-9.636 9.637Z"
                  />
                </G>
              </Svg>
            );
        } else {
            return (
                <Svg xmlns="http://www.w3.org/2000/svg" fill="none" {...props}>
                <Path fill="00358E" d="M14.182 17.818H6.909V8.364a.73.73 0 0 0-.727-.728.73.73 0 0 0-.727.728v9.454c0 .8.654 1.455 1.454 1.455h7.273a.73.73 0 0 0 .727-.727.73.73 0 0 0-.727-.728Zm3.636-2.909V6.182c0-.8-.654-1.455-1.454-1.455H9.818c-.8 0-1.454.655-1.454 1.455v8.727c0 .8.654 1.455 1.454 1.455h6.546c.8 0 1.454-.655 1.454-1.455Zm-1.454 0H9.818V6.182h6.546v8.727Z" />
              </Svg>
            );
        }

    };

    return (
    <View style={[styles.icon, isActive && styles.active]}>
        { getIcon(null) }
    </View>
    );
};