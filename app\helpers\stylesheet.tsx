import { StyleSheet } from "react-native";

export const styles = StyleSheet.create({
  container: {
    flex: 1,
    height: '100%',
    flexDirection: 'column',
  },  
  scrollview: {
    flex: 1,
    height: '100%',
  },
  topToolbar: {
    height: '5.5%',
    width: '100%',
  },
  header: {
    flexDirection: 'column',
  },
  banner: {
    backgroundColor: '#00358E',
    justifyContent: 'center',
    flexWrap: 'nowrap',
    paddingTop: 5,
    paddingBottom: 5,
  },
  content: {
    height: '60%',
  },
  footer: {
    height: '22%',
    backgroundColor: '#00358E',
    flexDirection: 'column',
    flex: 1,
  },
  bottomToolbar:{
    backgroundColor: '#00358E',
    height: '3%',
    width: '100%',
  }
});

export const topStyles = StyleSheet.create({
  topRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    flexWrap: 'nowrap', 
    alignItems: 'center',
    alignContent: 'center',
    paddingLeft: 10,
    paddingRight: 10,
  },
  leftTop: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    gap: 8,
    alignContent: 'center',
    alignItems: 'center',
    width: '25%',
  },
  centerTop:{
    flexDirection: 'row',
    alignContent: 'center',
    alignItems: 'center',
    justifyContent: 'center', 
    width: '50%',
    marginRight: 20 //TODO
  },
  rightTop:{
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center', 
    width: '15%',
    height: 42,
  },
  profileBackdrop: {
    width: '100%',
    height: '100%',
  },
  userProfileContainer: {
    width: 110,
    height: 40,
    backgroundColor: 'white',
    position: 'absolute',
    right: "0%",
    top: '11%',
    zIndex:2,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 6,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
    paddingRight: 5,
    paddingLeft: 5,
    borderColor: 'rgba(0, 0, 0, 0.2)',
    borderWidth: 1,
  },
  userProfileRow:{
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 3
  },
  userProfile:{
    fontSize: 16, 
    fontFamily: 'Roboto',
    fontStyle: 'normal',
    fontWeight: '400',
    lineHeight: 16,
    color: 'rgba(0, 0, 0, 0.80)',
    paddingTop: 4,
  },
  text: {
    fontSize: 16,
    color: '#00358E',
    fontFamily: 'Roboto',
    fontWeight: '400',
    lineHeight: 24
  },
  topBannerText: {
    color: 'white',
    textAlign: 'center',
    fontFamily: 'Roboto',
    fontStyle: 'normal',
    fontWeight: '500',
    lineHeight: 36,  
    flexShrink: 1,
    fontSize: 21
  }
});

export const bottomStyles = StyleSheet.create({
  bottomLogoContainer:{
    justifyContent: 'center',
    alignItems: 'center',
    padding: 0,
    paddingTop: 20,
    paddingBottom: 10,
    
  },
  bottomCopyrightText: {
    width: '100%',
    color: '#FFF',
    justifyContent: 'center',
    textAlign: 'center',
    fontFamily: 'Roboto',
    fontSize: 12,
    fontStyle: 'normal',
    fontWeight: '400',
    lineHeight: 20,
    paddingTop: 5,
    paddingBottom: 10,
    paddingLeft: 20,
    paddingRight: 20,
    flexWrap: 'wrap',
  },
  bottomLinkText: {
    width: '100%',
    color: '#FFF',
    textAlign: 'center',
    fontFamily: 'Roboto',
    fontSize: 12,
    fontStyle: 'normal',
    fontWeight: '400',
    lineHeight: 20,
    paddingBottom: 1,
  },
  bottomDisclaimerText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontFamily: 'Roboto',
    fontWeight: '500',
    lineHeight: 18,
    alignItems: 'center',
    textAlign: 'center',
    paddingLeft: 15,
    paddingRight: 15,
    paddingTop: 10,
    paddingBottom: 10,
  }
});

export const msgStyles = StyleSheet.create({
  message: {
    fontFamily: 'Roboto',
    fontSize: 14,
    fontStyle: 'normal',
    fontWeight: '400',
    lineHeight: 24,
    color: 'black',
    marginBottom: 10
  },
  list: {
    fontFamily: 'Roboto',
    fontSize: 14,
    fontStyle: 'normal',
    fontWeight: '400',
    lineHeight: 24,
    color: 'black',
    marginBottom: 5,
    marginLeft: 5
  },
    messageContainer: {
    marginVertical: 5,
  },
  greeting: {
    fontStyle: 'italic',
  },
  sourcesContainer: {
    marginTop: 10,
  },
  sourcesLabel: {
    fontSize: 14,
    fontWeight: 'bold',
    marginBottom: 5,
    color: '#1A1A1A',
  },
  sourceLink: {
    marginVertical: 2,
  },
  sourceLinkText: {
    fontSize: 14,
    fontWeight:'400',
    color: '#00358E',
  },
});

export const convStyles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'space-between',
  },
  conversation: {
    flex: 1,
    overflow: 'scroll',
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    height: '10%',
  },
  newConversationButton: {
    fontFamily: 'Roboto',
    fontSize: 16,
    backgroundColor: '#FFC600',
    width: 260,
    padding: 12,
    borderWidth: 1,
    borderColor: '#FFC600',
    borderRadius: 4,
    alignItems: 'center',
    flexDirection: 'row',
    gap: 8,
    alignContent: 'center',
    justifyContent: 'center',
    
  },
  addIcon: {
    height: 18,
    width: 18,
  },
  inputRow: {
    justifyContent: 'center',
    alignContent: 'center',
    alignItems: 'center',
    marginTop: 15,
    marginBottom: 15,
  },
  inputContainer: {
    width: '90%',
    borderWidth: 1,
    borderRadius: 10,
    borderColor: '#00358E',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  input: {
    width: '85%',
    maxHeight: 60, // Dynamic stretching for multiline
    fontSize: 16,
    fontFamily: 'Roboto',
    fontStyle: 'normal',
    fontWeight: '400',
    lineHeight: 19,
    borderRadius: 8,
    paddingLeft: 15,
    paddingTop: 10,
    paddingBottom: 10,
    paddingRight: 15,
  },
  submitDisabled: {
    backgroundColor: 'yellow',
    borderRadius: 25,
    justifyContent: 'center',
    alignItems: 'center',
  },
  inputButtonsContainer: {
    width: '10%',
    flexDirection: 'row',
    // justifyContent: 'space-between', TODO: Microphone
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 10,
  },
  submitButton: {

  },
  divider: {

  },
  microphoneButton: {

  },
  footer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-evenly',
    borderTopWidth: 1,
    borderTopColor: 'rgba(140, 140, 140, 0.25)',
    paddingLeft: 10,
    paddingRight: 10,
    marginLeft: 30,
    marginRight: 30,
  },
  buttonText: {
    color: '#00358E',
    fontSize: 12,
    fontFamily: 'Roboto',
    fontWeight: '400',
    lineHeight: 24,
  },
  submitIcon: {
    width: 24,
    height: 24,
  }
});

export const messageStyles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 15,
  },
  message: {
    marginTop: 15,
    marginLeft: 12, 
    marginRight: 30,
  },
  text: {
    fontFamily: 'Roboto',
    fontSize: 14,
    lineHeight: 24,
    fontStyle: 'normal',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'center',
    gap: 8,
  },
  headerText: {
    fontFamily: 'Roboto-Bold',
    fontSize: 14,
    fontWeight: 600,
    lineHeight: 24,
    fontStyle: 'normal',
  },
  greeting: {
    fontFamily: 'Roboto',
    fontSize: 20,
    lineHeight: 24,
    fontStyle: 'normal',
    marginTop: 10,
    marginBottom: 15,
  },
  content: {
    marginLeft: 32,
  },
  sourcesHeader: {
    fontFamily: 'Roboto-Bold',
    fontSize: 14,
    lineHeight: 24,
    fontStyle: 'normal',
    marginTop: 15,

  },
  sources: {
    marginLeft: 32,
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    marginTop: 10,
    marginLeft: 32,
    gap: 3,
  },
  greeetingsContent:{
    marginLeft: 32,
    marginBottom: 50,
  },
  conversationStartersDescription: {
    fontSize: 14,
    marginBottom: 16,
  },
  conversationStartersList: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 4,
  },
});

export const popupStyles = StyleSheet.create({
  outerContainer: {
    flex: 1,
    flexDirection: 'column',
    justifyContent: 'center',
  },
  backdrop:{
    backgroundColor: 'rgba(0, 0, 0, 0.2)',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  container: {
    overflowY: 'scroll',
    minHeight: '30%',
    maxHeight: '55%',
    marginLeft: 1,
    marginRight: 1,
    backgroundColor: "#FFFFFF",
    borderRadius: 10,
    fontFamily: 'Roboto',
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.2,
    shadowRadius: 3.84,
    elevation: 4,
  },
  header: {
    minHeight: 60,
    maxHeight: 60,
    justifyContent: 'space-between',
    flexDirection: 'row',
    alignItems: 'center',
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0, 0, 0, 0.2)',
    paddingLeft: 20,
    paddingRight: 20,
    paddingTop: 0,
    paddingBottom: 0,
  },
  title: {
    height: 30,
    width: '90%',
    color: "#1A1A1A",
    fontFamily: 'Roboto',
    fontSize: 18,
    fontStyle: 'normal',
    fontWeight: 500,
    lineHeight: 30,
  },
  close: {
    width: '10%',
    height: 20,
  },
  bodyContainer: {
    flex: 1,
    minHeight: 140,
    paddingTop: 10,
    paddingBottom: 10,
    paddingLeft: 20,
    paddingRight: 15,
    alignItems: 'center',
    justifyContent: 'center',
    textAlign: 'center',
  },
  body: {
    overflowY: 'scroll',
    alignContent: 'center',
    alignItems: 'center',
    justifyContent: 'center',
    textAlign: 'center',

  },
  bodyDisclaimer: {
    overflowY: 'scroll',
    alignContent: 'center',
    alignItems: 'center',
    backgroundColor: 'white',
  },
  bodyFeedback : {
    paddingTop: 10,
    paddingBottom: 10,
    justifyContent: 'center',
    marginLeft: 20,
    marginRight: 20,
  },
  feedbackRadio: {
    marginTop: 7,
    marginBottom: 7,
    justifyContent: 'flex-start',
    alignItems: 'flex-start',
  },
  inputContainer: {
    width: '80%',
    borderWidth: 1,
    borderRadius: 10,
    borderColor: 'rgba(0, 0, 0, 0.8)',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginLeft: 35,
  },
  feedbackInput:{
    height: 30,
    width: '80%',
    alignContent: 'center',
    justifyContent: 'flex-start',
    fontSize: 16,
    fontFamily: 'Roboto',
    fontStyle: 'normal',
    fontWeight: '400',
    lineHeight: 18,
    paddingLeft: 12,
    paddingRight: 12,
    borderRadius: 10,
  },
  rename: {
    flex: 1,
    paddingTop: 10,
    paddingBottom: 10,
    justifyContent: 'center',
    alignItems: 'center',

  },
  renameContainer: {
    width: '90%',
    borderWidth: 1,
    borderRadius: 10,
    borderColor: 'rgba(0, 0, 0, 0.8)',
    },
  input: {
    fontSize: 16, 
    fontFamily: 'Roboto',
    fontStyle: 'normal',
    fontWeight: '400',
    minHeight: 30,
    maxHeight: 60,
    lineHeight: 18,
    paddingLeft: 12,
    paddingRight: 12,
    borderRadius: 10,
    },
  footer: {
    height: 60,
    gap: 10,
    backgroundColor: "#FFFFFF",
    flexDirection: 'row',
    justifyContent: 'flex-end',
    borderTopWidth: 1,
    borderTopColor: 'rgba(0, 0, 0, 0.2)',
    alignItems: 'center',
    paddingRight: 20,
    borderBottomLeftRadius: 10,
    borderBottomRightRadius: 10,
  },
  cancelButton: {
    height: 30,
    backgroundColor: '#FFC600',
    borderRadius: 5,
    paddingVertical: 6,
    paddingHorizontal: 10,
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
  },
  cancelText: {
    fontFamily: 'Roboto-Bold', 
    color: 'black',
    fontSize: 12
  },
  confirmButton: {
    height: 30,
    backgroundColor: '#e80238',
    borderRadius: 5,
    paddingVertical: 6,
    paddingHorizontal: 10,
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
  },
  confirmText: {
    fontFamily: 'Roboto-Bold', 
    color: 'white',
    fontSize: 12
  },
  disabledButton: {
    height: 30,
    backgroundColor: 'rgba(156, 25, 55, 0.1)',
  },
  disabledText: {
    fontFamily: 'Roboto-Bold', 
    color: 'rgba(0, 0, 0, 0.2)',
    fontSize: 12
  },
  // Terms of Agreement specific styles
  termsHeader: {
    paddingHorizontal: 24,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderColor: '#eee',
    justifyContent: 'flex-start',
    alignItems: 'center',
    minHeight: 60,
  },
  termsTitle: {
    fontWeight: 'bold',
    fontSize: 20,
    color: '#222',
  },
  termsContent: {
    paddingHorizontal: 24,
    paddingVertical: 2,
    flex: 1,
    minHeight: 200,
  },
  termsFooter: {
    marginTop: 0,
    paddingHorizontal: 24,
    paddingVertical: 16,
    borderTopWidth: 1,
    borderColor: '#eee',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderBottomLeftRadius: 16,
    borderBottomRightRadius: 16,
    backgroundColor: '#fff',
  },
  termsCheckboxContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  termsCheckbox: {
    marginRight: 10,
  },
  termsCheckboxInner: {
    width: 18,
    height: 18,
    borderRadius: 4,
    borderWidth: 1,
    borderColor: '#888',
    alignItems: 'center',
    justifyContent: 'center',
  },
  termsCheckboxText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: 'bold',
  },
  termsAgreementText: {
    fontSize: 15,
    color: '#222',
  },
  termsContinueButton: {
    minWidth: 110,
    paddingHorizontal: 24,
    paddingVertical: 10,
    borderRadius: 6,
    alignItems: 'center',
  },
  termsContinueText: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  termsOrDisclaimerContainer: {
    maxWidth: 800,
    width: '100%',
    alignSelf: 'center',
    borderRadius: 16,
    padding: 0,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 8,
    overflow: 'visible',
  },
});

export const sidebarStyles = StyleSheet.create({
  outerContainer: {
    flex: 1,
  },
  backdrop: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    zIndex: 1,
  },
  container: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: '20%',
    bottom: 0,
    zIndex: 2,
    backgroundColor: "#FFFFFF",
    shadowColor: "#000",
    shadowOffset: {
      width: 1,
      height: 5,
    },
    shadowOpacity: 0.15,
    shadowRadius: 3.84,
    elevation: 5,
  },
  topBoundary:{
    height: '6.5%'
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    flexWrap: 'nowrap', 
    paddingTop: 5,
    paddingLeft: 15,
    paddingRight: 15,
    paddingBottom: 5,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(140, 140, 140, 0.15)',
    height: 36
  },
  searchRow: {
    paddingLeft: 10,
    paddingRight: 10,
    flexDirection: 'row',
    gap: 5,
    marginTop: 5,
    marginBottom: 5,
 },
 inputContainer: {
  width: '85%',
  borderWidth: 1,
  borderRadius: 10,
  borderColor: '#00358E',
  flexDirection: 'row',
  alignItems: 'center',
  justifyContent: 'space-between',
},
  searchBar: {
    fontSize: 16, 
    fontFamily: 'Roboto',
    fontStyle: 'normal',
    fontWeight: '400',
    lineHeight: 18,
    paddingTop: 10,
    paddingBottom: 8,
    paddingLeft: 12,
    paddingRight: 12,
    borderRadius: 10,
  },
  searchButton: {
    width: '15%',
    justifyContent: 'center',
    alignItems: 'center',
    gap: 10,
    backgroundColor: '#FFC600',
    borderWidth: 1,
    borderRadius: 4,
    borderColor: '#FFC600',
  },
  content:{
    flex: 1,
    overflow: 'scroll',
  },
  category: {
    height: 44,
    backgroundColor: '#F4F4F4',
    width: '100%',
    justifyContent: 'center',
    paddingLeft: 12,
    fontSize: 14,
    fontFamily: 'Roboto-Bold',
    fontStyle: 'normal',
    lineHeight: 24,
    color: '#1A1A1A',
  },
  titleRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    height: 44,
    paddingLeft: 18,
    paddingRight: 5,
    overflow: 'hidden',
  },
  titleClick: {
    width: '90%',
    height: '100%',
    alignItems: 'center',
    flexDirection: 'row',
 },
  title: { 
    fontSize: 14, 
    fontFamily: 'Roboto',
    fontStyle: 'normal',
    fontWeight: 300,
    lineHeight: 15,
    color: '#1A1A1A',
  },
  activeTitle: {
    fontSize: 15, 
    fontFamily: 'Roboto-Bold',
    fontStyle: 'normal',
    lineHeight: 14,
    color: '#1A1A1A',
  },
  options:{

  },
  optionsIcon: {
    width: 22,
    height: 22,
  },
  footer: {
    height: 44,
    flexDirection: 'row',
    flexWrap: 'nowrap', 
    borderTopWidth: 1,
    borderTopColor: 'rgba(140, 140, 140, 0.15)',
  },
  footerButton: {
    width: '50%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  footerButtonRight: {
    width: '50%',
    justifyContent: 'center',
    alignItems: 'center',
    borderLeftWidth: 0.8,
    borderLeftColor: 'rgba(140, 140, 140, 0.15)',
  },
  buttonText: {
    color: '#00358E',
    fontSize: 12,
    fontFamily: 'Roboto',
    fontWeight: '400',
    lineHeight: 24,
  },
  bottomBoundary:{
    height: '1%'
  }
});

export const optionsStyles = StyleSheet.create({
  backdrop:{
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  container: {
    flex: 1,
  },
  containerInner: {
    flex: 1,
    height: '100%',
    top: '0%',
    marginRight: '20.3%',
    justifyContent: 'flex-end',
    flexDirection: 'row',
  },
  content: {
    marginTop: 125,
    left: 1,
    backgroundColor: 'white',
    width: 130,
    height: 75,
    shadowColor: "#000",
    shadowOffset: {
      width: -1,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
    justifyContent: 'space-between',
    flexDirection: 'column',
    borderWidth:0.1,
    borderColor: 'rgba(140, 140, 140, 0.25)',
  },
  tab: { 
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    gap: 15,
    paddingLeft: 15,
    borderWidth:0.1,
    borderColor: 'rgba(140, 140, 140, 0.1)',
  },
  text: {
    fontSize: 14, 
    fontFamily: 'Roboto',
    fontStyle: 'normal',
    fontWeight: '400',
    lineHeight: 24,
    color: 'black',
    marginTop: 2
  }
});

// Default export
export default {
  styles,
  topStyles,
  bottomStyles,
  msgStyles,
  convStyles,
  messageStyles,
  popupStyles,
  sidebarStyles,
  optionsStyles
};