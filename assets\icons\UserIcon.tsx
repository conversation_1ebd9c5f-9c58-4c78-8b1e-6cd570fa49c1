import React from 'react';
import { View, Text, StyleSheet } from 'react-native';

export default function Icon({
    userName,
}: { 
    userName: string, 
}) { 
    const styles = StyleSheet.create({
        icon: {
            color: userName === 'PAAS AI' ? 'white' : 'black',
            fontFamily: 'Roboto',
            fontSize: 13,
        },  
    });

    const getInitials = () => {
        const name = userName.split(' ');
        return (name.length > 2) 
                    ? <Text style={styles.icon}>
                        {name[0].charAt(0) + name[1].charAt(0)}
                     </Text>
                    : (name.length > 1)
                        ? <Text style={styles.icon}>
                            {name[0].charAt(0)}
                        </Text>
                        : <Text style={styles.icon}>
                            ME
                        </Text>
    };

    return (
    <View>
        <View style={{
            width: 24,
            height: 24,
            backgroundColor: userName == 'PAAS AI' 
                ? '#00308E' : '#FFC600',
            borderRadius: 50,
            alignItems: 'center',
            justifyContent: 'center',
        }}>{ getInitials() }</View>
    </View>
    );
};