<?xml version="1.0" encoding="UTF-8"?>
<coverage generated="1743469654131" clover="3.2.0">
  <project timestamp="1743469654131" name="All files">
    <metrics statements="534" coveredstatements="472" conditionals="281" coveredconditionals="208" methods="185" coveredmethods="156" elements="1000" coveredelements="836" complexity="0" loc="534" ncloc="534" packages="4" files="25" classes="25"/>
    <package name="app">
      <metrics statements="236" coveredstatements="184" conditionals="128" coveredconditionals="76" methods="68" coveredmethods="52"/>
      <file name="authentication.tsx" path="/Users/<USER>/Documents/PAASApplications/premium.audit.advisory.service.mobile/app/authentication.tsx">
        <metrics statements="25" coveredstatements="16" conditionals="12" coveredconditionals="5" methods="4" coveredmethods="3"/>
        <line num="8" count="1" type="stmt"/>
        <line num="9" count="1" type="cond" truecount="2" falsecount="0"/>
        <line num="10" count="1" type="cond" truecount="2" falsecount="0"/>
        <line num="11" count="1" type="stmt"/>
        <line num="12" count="1" type="stmt"/>
        <line num="13" count="1" type="stmt"/>
        <line num="14" count="1" type="stmt"/>
        <line num="22" count="1" type="stmt"/>
        <line num="26" count="3" type="stmt"/>
        <line num="27" count="3" type="stmt"/>
        <line num="39" count="3" type="stmt"/>
        <line num="41" count="3" type="cond" truecount="1" falsecount="1"/>
        <line num="42" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="47" count="3" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="61" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="62" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="76" count="0" type="stmt"/>
        <line num="83" count="0" type="stmt"/>
        <line num="88" count="3" type="stmt"/>
        <line num="109" count="1" type="stmt"/>
        <line num="123" count="1" type="stmt"/>
      </file>
      <file name="chatbox.tsx" path="/Users/<USER>/Documents/PAASApplications/premium.audit.advisory.service.mobile/app/chatbox.tsx">
        <metrics statements="137" coveredstatements="105" conditionals="62" coveredconditionals="34" methods="40" coveredmethods="32"/>
        <line num="17" count="1" type="stmt"/>
        <line num="18" count="1" type="stmt"/>
        <line num="23" count="53" type="stmt"/>
        <line num="24" count="53" type="stmt"/>
        <line num="25" count="53" type="stmt"/>
        <line num="26" count="53" type="stmt"/>
        <line num="27" count="53" type="stmt"/>
        <line num="28" count="53" type="stmt"/>
        <line num="29" count="53" type="stmt"/>
        <line num="33" count="53" type="stmt"/>
        <line num="34" count="53" type="stmt"/>
        <line num="36" count="53" type="stmt"/>
        <line num="37" count="11" type="cond" truecount="1" falsecount="1"/>
        <line num="38" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="39" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="44" count="11" type="cond" truecount="1" falsecount="1"/>
        <line num="45" count="11" type="stmt"/>
        <line num="47" count="11" type="stmt"/>
        <line num="49" count="11" type="stmt"/>
        <line num="54" count="53" type="stmt"/>
        <line num="55" count="53" type="stmt"/>
        <line num="56" count="159" type="stmt"/>
        <line num="58" count="53" type="stmt"/>
        <line num="62" count="159" type="stmt"/>
        <line num="72" count="11" type="stmt"/>
        <line num="73" count="11" type="stmt"/>
        <line num="76" count="11" type="stmt"/>
        <line num="83" count="11" type="stmt"/>
        <line num="84" count="11" type="cond" truecount="4" falsecount="1"/>
        <line num="87" count="11" type="stmt"/>
        <line num="89" count="88" type="cond" truecount="3" falsecount="1"/>
        <line num="90" count="22" type="stmt"/>
        <line num="96" count="22" type="stmt"/>
        <line num="107" count="11" type="stmt"/>
        <line num="109" count="11" type="stmt"/>
        <line num="115" count="53" type="stmt"/>
        <line num="116" count="0" type="stmt"/>
        <line num="119" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="126" count="53" type="stmt"/>
        <line num="127" count="8" type="stmt"/>
        <line num="131" count="53" type="stmt"/>
        <line num="132" count="2" type="stmt"/>
        <line num="133" count="2" type="cond" truecount="1" falsecount="1"/>
        <line num="139" count="53" type="stmt"/>
        <line num="140" count="1" type="stmt"/>
        <line num="141" count="1" type="stmt"/>
        <line num="142" count="1" type="stmt"/>
        <line num="143" count="1" type="stmt"/>
        <line num="144" count="1" type="stmt"/>
        <line num="148" count="53" type="stmt"/>
        <line num="149" count="6" type="stmt"/>
        <line num="150" count="1" type="stmt"/>
        <line num="151" count="1" type="stmt"/>
        <line num="153" count="4" type="cond" truecount="4" falsecount="0"/>
        <line num="155" count="2" type="stmt"/>
        <line num="156" count="2" type="cond" truecount="1" falsecount="1"/>
        <line num="157" count="0" type="stmt"/>
        <line num="159" count="2" type="stmt"/>
        <line num="161" count="1" type="stmt"/>
        <line num="164" count="6" type="stmt"/>
        <line num="165" count="0" type="stmt"/>
        <line num="168" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="169" count="0" type="stmt"/>
        <line num="176" count="0" type="stmt"/>
        <line num="177" count="0" type="stmt"/>
        <line num="178" count="0" type="stmt"/>
        <line num="179" count="0" type="stmt"/>
        <line num="180" count="0" type="stmt"/>
        <line num="184" count="6" type="stmt"/>
        <line num="186" count="0" type="stmt"/>
        <line num="189" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="190" count="0" type="stmt"/>
        <line num="198" count="0" type="stmt"/>
        <line num="201" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="206" count="0" type="stmt"/>
        <line num="210" count="6" type="stmt"/>
        <line num="211" count="2" type="stmt"/>
        <line num="212" count="2" type="cond" truecount="2" falsecount="0"/>
        <line num="214" count="2" type="cond" truecount="1" falsecount="1"/>
        <line num="216" count="2" type="stmt"/>
        <line num="226" count="2" type="stmt"/>
        <line num="228" count="2" type="cond" truecount="3" falsecount="1"/>
        <line num="229" count="2" type="stmt"/>
        <line num="231" count="2" type="stmt"/>
        <line num="234" count="4" type="cond" truecount="2" falsecount="0"/>
        <line num="242" count="6" type="stmt"/>
        <line num="243" count="2" type="stmt"/>
        <line num="244" count="2" type="stmt"/>
        <line num="247" count="2" type="cond" truecount="1" falsecount="1"/>
        <line num="249" count="2" type="stmt"/>
        <line num="250" count="2" type="stmt"/>
        <line num="256" count="2" type="stmt"/>
        <line num="273" count="0" type="stmt"/>
        <line num="276" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="277" count="0" type="stmt"/>
        <line num="286" count="0" type="stmt"/>
        <line num="289" count="0" type="stmt"/>
        <line num="291" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="297" count="2" type="stmt"/>
        <line num="298" count="2" type="stmt"/>
        <line num="301" count="2" type="stmt"/>
        <line num="309" count="2" type="stmt"/>
        <line num="312" count="2" type="stmt"/>
        <line num="313" count="4" type="cond" truecount="2" falsecount="0"/>
        <line num="317" count="2" type="cond" truecount="1" falsecount="1"/>
        <line num="325" count="2" type="stmt"/>
        <line num="326" count="2" type="stmt"/>
        <line num="330" count="6" type="stmt"/>
        <line num="331" count="6" type="cond" truecount="2" falsecount="0"/>
        <line num="333" count="5" type="cond" truecount="4" falsecount="3"/>
        <line num="335" count="1" type="stmt"/>
        <line num="336" count="1" type="stmt"/>
        <line num="337" count="1" type="stmt"/>
        <line num="340" count="0" type="stmt"/>
        <line num="341" count="0" type="stmt"/>
        <line num="344" count="0" type="stmt"/>
        <line num="345" count="0" type="stmt"/>
        <line num="348" count="2" type="stmt"/>
        <line num="349" count="2" type="stmt"/>
        <line num="353" count="2" type="stmt"/>
        <line num="354" count="2" type="stmt"/>
        <line num="357" count="0" type="stmt"/>
        <line num="361" count="53" type="stmt"/>
        <line num="362" count="1" type="stmt"/>
        <line num="363" count="1" type="stmt"/>
        <line num="369" count="1" type="stmt"/>
        <line num="372" count="53" type="stmt"/>
        <line num="381" count="1" type="stmt"/>
        <line num="388" count="1" type="stmt"/>
        <line num="398" count="1" type="stmt"/>
        <line num="414" count="5" type="stmt"/>
        <line num="420" count="1" type="stmt"/>
        <line num="435" count="2" type="stmt"/>
        <line num="456" count="15" type="stmt"/>
        <line num="457" count="4" type="stmt"/>
      </file>
      <file name="messages.tsx" path="/Users/<USER>/Documents/PAASApplications/premium.audit.advisory.service.mobile/app/messages.tsx">
        <metrics statements="65" coveredstatements="54" conditionals="54" coveredconditionals="37" methods="24" coveredmethods="17"/>
        <line num="34" count="59" type="stmt"/>
        <line num="35" count="59" type="stmt"/>
        <line num="36" count="59" type="stmt"/>
        <line num="39" count="59" type="stmt"/>
        <line num="40" count="59" type="stmt"/>
        <line num="41" count="59" type="stmt"/>
        <line num="42" count="59" type="stmt"/>
        <line num="44" count="59" type="stmt"/>
        <line num="45" count="59" type="stmt"/>
        <line num="47" count="59" type="stmt"/>
        <line num="50" count="59" type="stmt"/>
        <line num="51" count="59" type="stmt"/>
        <line num="53" count="59" type="stmt"/>
        <line num="54" count="15" type="stmt"/>
        <line num="58" count="59" type="stmt"/>
        <line num="59" count="15" type="cond" truecount="2" falsecount="0"/>
        <line num="63" count="59" type="stmt"/>
        <line num="64" count="1" type="stmt"/>
        <line num="66" count="1" type="stmt"/>
        <line num="68" count="1" type="stmt"/>
        <line num="69" count="1" type="stmt"/>
        <line num="70" count="0" type="stmt"/>
        <line num="74" count="59" type="stmt"/>
        <line num="75" count="2" type="stmt"/>
        <line num="78" count="59" type="stmt"/>
        <line num="79" count="2" type="stmt"/>
        <line num="82" count="59" type="stmt"/>
        <line num="83" count="2" type="cond" truecount="1" falsecount="1"/>
        <line num="85" count="2" type="stmt"/>
        <line num="87" count="2" type="stmt"/>
        <line num="89" count="2" type="stmt"/>
        <line num="92" count="0" type="stmt"/>
        <line num="93" count="0" type="stmt"/>
        <line num="98" count="59" type="stmt"/>
        <line num="99" count="2" type="cond" truecount="2" falsecount="0"/>
        <line num="100" count="1" type="stmt"/>
        <line num="101" count="1" type="stmt"/>
        <line num="103" count="1" type="stmt"/>
        <line num="108" count="59" type="stmt"/>
        <line num="109" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="110" count="1" type="stmt"/>
        <line num="113" count="59" type="stmt"/>
        <line num="114" count="0" type="stmt"/>
        <line num="117" count="59" type="stmt"/>
        <line num="118" count="0" type="cond" truecount="0" falsecount="6"/>
        <line num="119" count="0" type="stmt"/>
        <line num="120" count="0" type="stmt"/>
        <line num="125" count="59" type="stmt"/>
        <line num="126" count="59" type="stmt"/>
        <line num="127" count="75" type="stmt"/>
        <line num="130" count="59" type="cond" truecount="4" falsecount="0"/>
        <line num="131" count="43" type="stmt"/>
        <line num="154" count="16" type="cond" truecount="1" falsecount="1"/>
        <line num="155" count="16" type="stmt"/>
        <line num="157" count="16" type="stmt"/>
        <line num="190" count="0" type="stmt"/>
        <line num="204" count="1" type="stmt"/>
        <line num="212" count="1" type="stmt"/>
        <line num="220" count="1" type="stmt"/>
        <line num="231" count="0" type="stmt"/>
        <line num="234" count="59" type="stmt"/>
        <line num="244" count="0" type="stmt"/>
        <line num="264" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="274" count="2" type="stmt"/>
        <line num="298" count="1" type="stmt"/>
      </file>
      <file name="stylesheet.tsx" path="/Users/<USER>/Documents/PAASApplications/premium.audit.advisory.service.mobile/app/stylesheet.tsx">
        <metrics statements="9" coveredstatements="9" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="3" count="3" type="stmt"/>
        <line num="43" count="3" type="stmt"/>
        <line num="138" count="3" type="stmt"/>
        <line num="189" count="3" type="stmt"/>
        <line num="211" count="3" type="stmt"/>
        <line num="323" count="3" type="stmt"/>
        <line num="384" count="3" type="stmt"/>
        <line num="585" count="3" type="stmt"/>
        <line num="748" count="3" type="stmt"/>
      </file>
    </package>
    <package name="app.helpers">
      <metrics statements="63" coveredstatements="63" conditionals="26" coveredconditionals="22" methods="6" coveredmethods="6"/>
      <file name="model.tsx" path="/Users/<USER>/Documents/PAASApplications/premium.audit.advisory.service.mobile/app/helpers/model.tsx">
        <metrics statements="63" coveredstatements="63" conditionals="26" coveredconditionals="22" methods="6" coveredmethods="6"/>
        <line num="2" count="1" type="stmt"/>
        <line num="3" count="1" type="stmt"/>
        <line num="4" count="1" type="stmt"/>
        <line num="5" count="1" type="stmt"/>
        <line num="9" count="1" type="stmt"/>
        <line num="10" count="1" type="stmt"/>
        <line num="11" count="1" type="stmt"/>
        <line num="12" count="1" type="stmt"/>
        <line num="17" count="64" type="stmt"/>
        <line num="18" count="64" type="stmt"/>
        <line num="21" count="64" type="stmt"/>
        <line num="22" count="64" type="stmt"/>
        <line num="114" count="60" type="stmt"/>
        <line num="115" count="60" type="stmt"/>
        <line num="118" count="60" type="stmt"/>
        <line num="119" count="60" type="stmt"/>
        <line num="124" count="15" type="stmt"/>
        <line num="125" count="15" type="stmt"/>
        <line num="126" count="15" type="stmt"/>
        <line num="127" count="15" type="stmt"/>
        <line num="128" count="15" type="stmt"/>
        <line num="129" count="15" type="stmt"/>
        <line num="130" count="15" type="stmt"/>
        <line num="135" count="15" type="stmt"/>
        <line num="136" count="15" type="stmt"/>
        <line num="137" count="15" type="stmt"/>
        <line num="138" count="15" type="stmt"/>
        <line num="139" count="15" type="stmt"/>
        <line num="140" count="15" type="stmt"/>
        <line num="141" count="15" type="stmt"/>
        <line num="159" count="184" type="stmt"/>
        <line num="160" count="184" type="stmt"/>
        <line num="168" count="123" type="stmt"/>
        <line num="172" count="123" type="stmt"/>
        <line num="174" count="123" type="cond" truecount="2" falsecount="0"/>
        <line num="175" count="1" type="stmt"/>
        <line num="176" count="1" type="stmt"/>
        <line num="181" count="1" type="stmt"/>
        <line num="182" count="1" type="stmt"/>
        <line num="184" count="122" type="cond" truecount="2" falsecount="0"/>
        <line num="185" count="1" type="stmt"/>
        <line num="186" count="1" type="stmt"/>
        <line num="187" count="1" type="stmt"/>
        <line num="188" count="1" type="stmt"/>
        <line num="190" count="121" type="cond" truecount="2" falsecount="0"/>
        <line num="191" count="3" type="stmt"/>
        <line num="192" count="3" type="stmt"/>
        <line num="193" count="3" type="stmt"/>
        <line num="194" count="3" type="stmt"/>
        <line num="196" count="118" type="cond" truecount="2" falsecount="0"/>
        <line num="197" count="1" type="stmt"/>
        <line num="198" count="1" type="stmt"/>
        <line num="200" count="1" type="stmt"/>
        <line num="201" count="1" type="stmt"/>
        <line num="203" count="117" type="cond" truecount="2" falsecount="0"/>
        <line num="204" count="1" type="stmt"/>
        <line num="205" count="1" type="stmt"/>
        <line num="206" count="1" type="stmt"/>
        <line num="207" count="1" type="stmt"/>
        <line num="210" count="116" type="stmt"/>
        <line num="211" count="116" type="stmt"/>
        <line num="212" count="116" type="stmt"/>
        <line num="213" count="116" type="stmt"/>
      </file>
    </package>
    <package name="app.modals">
      <metrics statements="149" coveredstatements="139" conditionals="111" coveredconditionals="94" methods="70" coveredmethods="57"/>
      <file name="options.tsx" path="/Users/<USER>/Documents/PAASApplications/premium.audit.advisory.service.mobile/app/modals/options.tsx">
        <metrics statements="13" coveredstatements="13" conditionals="6" coveredconditionals="6" methods="10" coveredmethods="10"/>
        <line num="18" count="38" type="stmt"/>
        <line num="20" count="38" type="stmt"/>
        <line num="21" count="5" type="stmt"/>
        <line num="24" count="38" type="stmt"/>
        <line num="25" count="5" type="stmt"/>
        <line num="26" count="5" type="cond" truecount="2" falsecount="0"/>
        <line num="27" count="5" type="cond" truecount="4" falsecount="0"/>
        <line num="30" count="38" type="stmt"/>
        <line num="39" count="1" type="stmt"/>
        <line num="43" count="1" type="stmt"/>
        <line num="48" count="3" type="stmt"/>
        <line num="58" count="2" type="stmt"/>
        <line num="73" count="3" type="stmt"/>
      </file>
      <file name="popup.tsx" path="/Users/<USER>/Documents/PAASApplications/premium.audit.advisory.service.mobile/app/modals/popup.tsx">
        <metrics statements="46" coveredstatements="44" conditionals="64" coveredconditionals="54" methods="21" coveredmethods="17"/>
        <line num="27" count="116" type="stmt"/>
        <line num="28" count="116" type="stmt"/>
        <line num="29" count="116" type="stmt"/>
        <line num="30" count="116" type="stmt"/>
        <line num="31" count="116" type="stmt"/>
        <line num="33" count="116" type="stmt"/>
        <line num="54" count="116" type="stmt"/>
        <line num="55" count="37" type="cond" truecount="2" falsecount="0"/>
        <line num="56" count="27" type="stmt"/>
        <line num="57" count="27" type="stmt"/>
        <line num="58" count="27" type="stmt"/>
        <line num="63" count="116" type="stmt"/>
        <line num="64" count="24" type="cond" truecount="2" falsecount="0"/>
        <line num="68" count="116" type="stmt"/>
        <line num="69" count="26" type="stmt"/>
        <line num="70" count="26" type="cond" truecount="2" falsecount="0"/>
        <line num="74" count="116" type="stmt"/>
        <line num="75" count="37" type="cond" truecount="2" falsecount="0"/>
        <line num="76" count="10" type="stmt"/>
        <line num="85" count="116" type="stmt"/>
        <line num="86" count="8" type="stmt"/>
        <line num="91" count="8" type="stmt"/>
        <line num="92" count="8" type="stmt"/>
        <line num="96" count="116" type="stmt"/>
        <line num="97" count="8" type="stmt"/>
        <line num="98" count="8" type="cond" truecount="4" falsecount="0"/>
        <line num="100" count="1" type="stmt"/>
        <line num="102" count="2" type="stmt"/>
        <line num="104" count="1" type="stmt"/>
        <line num="106" count="4" type="stmt"/>
        <line num="109" count="8" type="stmt"/>
        <line num="120" count="116" type="stmt"/>
        <line num="121" count="54" type="cond" truecount="6" falsecount="0"/>
        <line num="124" count="18" type="stmt"/>
        <line num="125" count="36" type="cond" truecount="6" falsecount="0"/>
        <line num="127" count="3" type="stmt"/>
        <line num="132" count="116" type="stmt"/>
        <line num="133" count="116" type="cond" truecount="1" falsecount="1"/>
        <line num="138" count="116" type="stmt"/>
        <line num="150" count="2" type="stmt"/>
        <line num="158" count="1" type="stmt"/>
        <line num="178" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="209" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="239" count="3" type="stmt"/>
        <line num="254" count="1" type="stmt"/>
        <line num="268" count="1" type="stmt"/>
      </file>
      <file name="sidebar.tsx" path="/Users/<USER>/Documents/PAASApplications/premium.audit.advisory.service.mobile/app/modals/sidebar.tsx">
        <metrics statements="90" coveredstatements="82" conditionals="41" coveredconditionals="34" methods="39" coveredmethods="30"/>
        <line num="13" count="2" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="29" count="87" type="stmt"/>
        <line num="30" count="87" type="stmt"/>
        <line num="31" count="87" type="stmt"/>
        <line num="32" count="87" type="stmt"/>
        <line num="33" count="87" type="stmt"/>
        <line num="34" count="87" type="stmt"/>
        <line num="36" count="87" type="stmt"/>
        <line num="37" count="29" type="stmt"/>
        <line num="41" count="87" type="stmt"/>
        <line num="42" count="18" type="cond" truecount="2" falsecount="0"/>
        <line num="43" count="6" type="stmt"/>
        <line num="49" count="12" type="stmt"/>
        <line num="50" count="12" type="stmt"/>
        <line num="55" count="87" type="stmt"/>
        <line num="56" count="3" type="stmt"/>
        <line num="61" count="3" type="stmt"/>
        <line num="62" count="3" type="stmt"/>
        <line num="63" count="3" type="stmt"/>
        <line num="68" count="87" type="stmt"/>
        <line num="69" count="5" type="stmt"/>
        <line num="70" count="5" type="stmt"/>
        <line num="73" count="87" type="stmt"/>
        <line num="74" count="1" type="stmt"/>
        <line num="75" count="1" type="stmt"/>
        <line num="76" count="4" type="stmt"/>
        <line num="77" count="4" type="stmt"/>
        <line num="79" count="1" type="stmt"/>
        <line num="82" count="87" type="stmt"/>
        <line num="83" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
        <line num="90" count="87" type="stmt"/>
        <line num="91" count="4" type="stmt"/>
        <line num="93" count="4" type="cond" truecount="2" falsecount="0"/>
        <line num="94" count="2" type="stmt"/>
        <line num="99" count="87" type="stmt"/>
        <line num="100" count="8" type="stmt"/>
        <line num="103" count="87" type="stmt"/>
        <line num="104" count="4" type="cond" truecount="1" falsecount="1"/>
        <line num="105" count="0" type="stmt"/>
        <line num="108" count="87" type="stmt"/>
        <line num="109" count="348" type="cond" truecount="4" falsecount="0"/>
        <line num="111" count="87" type="stmt"/>
        <line num="112" count="101" type="stmt"/>
        <line num="113" count="87" type="stmt"/>
        <line num="115" count="167" type="stmt"/>
        <line num="116" count="167" type="stmt"/>
        <line num="117" count="167" type="cond" truecount="3" falsecount="0"/>
        <line num="120" count="87" type="cond" truecount="2" falsecount="0"/>
        <line num="121" count="86" type="stmt"/>
        <line num="122" count="108" type="stmt"/>
        <line num="124" count="1" type="stmt"/>
        <line num="127" count="87" type="stmt"/>
        <line num="128" count="167" type="stmt"/>
        <line num="129" count="167" type="stmt"/>
        <line num="130" count="167" type="stmt"/>
        <line num="131" count="167" type="stmt"/>
        <line num="132" count="167" type="cond" truecount="3" falsecount="1"/>
        <line num="135" count="87" type="stmt"/>
        <line num="138" count="87" type="stmt"/>
        <line num="139" count="167" type="stmt"/>
        <line num="140" count="167" type="stmt"/>
        <line num="141" count="167" type="stmt"/>
        <line num="142" count="167" type="stmt"/>
        <line num="143" count="167" type="cond" truecount="2" falsecount="1"/>
        <line num="145" count="87" type="stmt"/>
        <line num="148" count="87" type="stmt"/>
        <line num="149" count="167" type="stmt"/>
        <line num="150" count="167" type="stmt"/>
        <line num="151" count="167" type="stmt"/>
        <line num="152" count="167" type="stmt"/>
        <line num="153" count="167" type="cond" truecount="2" falsecount="0"/>
        <line num="155" count="87" type="stmt"/>
        <line num="160" count="87" type="stmt"/>
        <line num="161" count="131" type="stmt"/>
        <line num="167" count="0" type="stmt"/>
        <line num="180" count="5" type="stmt"/>
        <line num="190" count="87" type="stmt"/>
        <line num="195" count="0" type="stmt"/>
        <line num="202" count="1" type="stmt"/>
        <line num="226" count="1" type="stmt"/>
        <line num="234" count="4" type="stmt"/>
        <line num="240" count="86" type="stmt"/>
        <line num="251" count="0" type="stmt"/>
        <line num="262" count="0" type="stmt"/>
        <line num="273" count="45" type="stmt"/>
        <line num="288" count="3" type="stmt"/>
        <line num="295" count="1" type="stmt"/>
        <line num="308" count="4" type="stmt"/>
      </file>
    </package>
    <package name="assets.icons">
      <metrics statements="86" coveredstatements="86" conditionals="16" coveredconditionals="16" methods="41" coveredmethods="41"/>
      <file name="CloseIcon.tsx" path="/Users/<USER>/Documents/PAASApplications/premium.audit.advisory.service.mobile/assets/icons/CloseIcon.tsx">
        <metrics statements="4" coveredstatements="4" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="2"/>
        <line num="5" count="2" type="stmt"/>
        <line num="16" count="24" type="stmt"/>
        <line num="17" count="24" type="stmt"/>
        <line num="32" count="24" type="stmt"/>
      </file>
      <file name="CopyIcon.tsx" path="/Users/<USER>/Documents/PAASApplications/premium.audit.advisory.service.mobile/assets/icons/CopyIcon.tsx">
        <metrics statements="6" coveredstatements="6" conditionals="4" coveredconditionals="4" methods="2" coveredmethods="2"/>
        <line num="5" count="1" type="stmt"/>
        <line num="24" count="10" type="stmt"/>
        <line num="25" count="10" type="cond" truecount="2" falsecount="0"/>
        <line num="26" count="1" type="stmt"/>
        <line num="50" count="9" type="stmt"/>
        <line num="59" count="10" type="stmt"/>
      </file>
      <file name="DeleteIcon.tsx" path="/Users/<USER>/Documents/PAASApplications/premium.audit.advisory.service.mobile/assets/icons/DeleteIcon.tsx">
        <metrics statements="4" coveredstatements="4" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="2"/>
        <line num="5" count="2" type="stmt"/>
        <line num="14" count="10" type="stmt"/>
        <line num="15" count="10" type="stmt"/>
        <line num="40" count="10" type="stmt"/>
      </file>
      <file name="EditIcon.tsx" path="/Users/<USER>/Documents/PAASApplications/premium.audit.advisory.service.mobile/assets/icons/EditIcon.tsx">
        <metrics statements="4" coveredstatements="4" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="2"/>
        <line num="5" count="2" type="stmt"/>
        <line num="14" count="10" type="stmt"/>
        <line num="15" count="10" type="stmt"/>
        <line num="25" count="10" type="stmt"/>
      </file>
      <file name="EllipsesIcon.tsx" path="/Users/<USER>/Documents/PAASApplications/premium.audit.advisory.service.mobile/assets/icons/EllipsesIcon.tsx">
        <metrics statements="4" coveredstatements="4" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="2"/>
        <line num="5" count="2" type="stmt"/>
        <line num="14" count="10" type="stmt"/>
        <line num="15" count="10" type="stmt"/>
        <line num="25" count="10" type="stmt"/>
      </file>
      <file name="LogoutIcon.tsx" path="/Users/<USER>/Documents/PAASApplications/premium.audit.advisory.service.mobile/assets/icons/LogoutIcon.tsx">
        <metrics statements="4" coveredstatements="4" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="2"/>
        <line num="5" count="1" type="stmt"/>
        <line num="17" count="3" type="stmt"/>
        <line num="18" count="3" type="stmt"/>
        <line num="28" count="3" type="stmt"/>
      </file>
      <file name="NewConversationIcon.tsx" path="/Users/<USER>/Documents/PAASApplications/premium.audit.advisory.service.mobile/assets/icons/NewConversationIcon.tsx">
        <metrics statements="4" coveredstatements="4" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="2"/>
        <line num="5" count="1" type="stmt"/>
        <line num="14" count="51" type="stmt"/>
        <line num="15" count="51" type="stmt"/>
        <line num="25" count="51" type="stmt"/>
      </file>
      <file name="ProfileIcon.tsx" path="/Users/<USER>/Documents/PAASApplications/premium.audit.advisory.service.mobile/assets/icons/ProfileIcon.tsx">
        <metrics statements="4" coveredstatements="4" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="2"/>
        <line num="5" count="1" type="stmt"/>
        <line num="13" count="51" type="stmt"/>
        <line num="14" count="51" type="stmt"/>
        <line num="30" count="51" type="stmt"/>
      </file>
      <file name="SearchIcon.tsx" path="/Users/<USER>/Documents/PAASApplications/premium.audit.advisory.service.mobile/assets/icons/SearchIcon.tsx">
        <metrics statements="4" coveredstatements="4" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="2"/>
        <line num="5" count="2" type="stmt"/>
        <line num="14" count="32" type="stmt"/>
        <line num="15" count="32" type="stmt"/>
        <line num="40" count="32" type="stmt"/>
      </file>
      <file name="SidebarIcon.tsx" path="/Users/<USER>/Documents/PAASApplications/premium.audit.advisory.service.mobile/assets/icons/SidebarIcon.tsx">
        <metrics statements="4" coveredstatements="4" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="2"/>
        <line num="5" count="1" type="stmt"/>
        <line num="17" count="51" type="stmt"/>
        <line num="18" count="51" type="stmt"/>
        <line num="33" count="51" type="stmt"/>
      </file>
      <file name="SubmitIcon.tsx" path="/Users/<USER>/Documents/PAASApplications/premium.audit.advisory.service.mobile/assets/icons/SubmitIcon.tsx">
        <metrics statements="6" coveredstatements="6" conditionals="4" coveredconditionals="4" methods="3" coveredmethods="3"/>
        <line num="10" count="59" type="stmt"/>
        <line num="28" count="59" type="stmt"/>
        <line num="29" count="57" type="stmt"/>
        <line num="44" count="59" type="stmt"/>
        <line num="45" count="2" type="stmt"/>
        <line num="61" count="59" type="stmt"/>
      </file>
      <file name="ThumbDown.tsx" path="/Users/<USER>/Documents/PAASApplications/premium.audit.advisory.service.mobile/assets/icons/ThumbDown.tsx">
        <metrics statements="6" coveredstatements="6" conditionals="2" coveredconditionals="2" methods="3" coveredmethods="3"/>
        <line num="6" count="1" type="stmt"/>
        <line num="22" count="10" type="stmt"/>
        <line num="23" count="10" type="cond" truecount="2" falsecount="0"/>
        <line num="26" count="10" type="stmt"/>
        <line num="27" count="10" type="stmt"/>
        <line num="45" count="10" type="stmt"/>
      </file>
      <file name="ThumbUp.tsx" path="/Users/<USER>/Documents/PAASApplications/premium.audit.advisory.service.mobile/assets/icons/ThumbUp.tsx">
        <metrics statements="6" coveredstatements="6" conditionals="2" coveredconditionals="2" methods="3" coveredmethods="3"/>
        <line num="6" count="1" type="stmt"/>
        <line num="21" count="10" type="stmt"/>
        <line num="22" count="10" type="cond" truecount="2" falsecount="0"/>
        <line num="25" count="10" type="stmt"/>
        <line num="26" count="10" type="stmt"/>
        <line num="44" count="10" type="stmt"/>
      </file>
      <file name="TypingIndicator.tsx" path="/Users/<USER>/Documents/PAASApplications/premium.audit.advisory.service.mobile/assets/icons/TypingIndicator.tsx">
        <metrics statements="13" coveredstatements="13" conditionals="0" coveredconditionals="0" methods="6" coveredmethods="6"/>
        <line num="4" count="1" type="stmt"/>
        <line num="5" count="6" type="stmt"/>
        <line num="13" count="6" type="stmt"/>
        <line num="14" count="6" type="stmt"/>
        <line num="15" count="6" type="stmt"/>
        <line num="16" count="30" type="stmt"/>
        <line num="34" count="6" type="stmt"/>
        <line num="37" count="6" type="stmt"/>
        <line num="38" count="6" type="stmt"/>
        <line num="39" count="30" type="stmt"/>
        <line num="44" count="6" type="stmt"/>
        <line num="47" count="30" type="stmt"/>
        <line num="53" count="1" type="stmt"/>
      </file>
      <file name="UserIcon.tsx" path="/Users/<USER>/Documents/PAASApplications/premium.audit.advisory.service.mobile/assets/icons/UserIcon.tsx">
        <metrics statements="5" coveredstatements="5" conditionals="4" coveredconditionals="4" methods="2" coveredmethods="2"/>
        <line num="9" count="46" type="stmt"/>
        <line num="17" count="46" type="stmt"/>
        <line num="18" count="46" type="stmt"/>
        <line num="19" count="46" type="stmt"/>
        <line num="24" count="46" type="stmt"/>
      </file>
      <file name="VeriskLogo.tsx" path="/Users/<USER>/Documents/PAASApplications/premium.audit.advisory.service.mobile/assets/icons/VeriskLogo.tsx">
        <metrics statements="4" coveredstatements="4" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="2"/>
        <line num="5" count="2" type="stmt"/>
        <line num="14" count="54" type="stmt"/>
        <line num="15" count="54" type="stmt"/>
        <line num="36" count="54" type="stmt"/>
      </file>
      <file name="VeriskWhiteLogo.tsx" path="/Users/<USER>/Documents/PAASApplications/premium.audit.advisory.service.mobile/assets/icons/VeriskWhiteLogo.tsx">
        <metrics statements="4" coveredstatements="4" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="2"/>
        <line num="5" count="2" type="stmt"/>
        <line num="13" count="54" type="stmt"/>
        <line num="14" count="54" type="stmt"/>
        <line num="31" count="54" type="stmt"/>
      </file>
    </package>
  </project>
</coverage>
